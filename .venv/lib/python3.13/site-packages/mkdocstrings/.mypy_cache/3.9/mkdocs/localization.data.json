{".class": "MypyFile", "_fullname": "mkdocs.localization", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Extension": {".class": "SymbolTableNode", "cross_ref": "jinja2.ext.Extension", "kind": "Gdef"}, "InternationalizationExtension": {".class": "SymbolTableNode", "cross_ref": "jinja2.ext.InternationalizationExtension", "kind": "Gdef"}, "Locale": {".class": "SymbolTableNode", "cross_ref": "babel.core.Locale", "kind": "Gdef"}, "NoBabelExtension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.ext.InternationalizationExtension"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.localization.NoBabelExtension", "name": "NoBabelExtension", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.localization.NoBabelExtension", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.localization", "mro": ["mkdocs.localization.NoBabelExtension", "jinja2.ext.InternationalizationExtension", "jinja2.ext.Extension", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "environment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.localization.NoBabelExtension.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.localization.NoBabelExtension.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.localization.NoBabelExtension", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NullTranslations": {".class": "SymbolTableNode", "cross_ref": "babel.support.NullTranslations", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Translations": {".class": "SymbolTableNode", "cross_ref": "babel.support.Translations", "kind": "Gdef"}, "UnknownLocaleError": {".class": "SymbolTableNode", "cross_ref": "babel.core.UnknownLocaleError", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.base.ValidationError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.localization.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.localization.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.localization.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.localization.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.localization.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.localization.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_merged_translations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["theme_dirs", "locales_dir", "locale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.localization._get_merged_translations", "name": "_get_merged_translations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["theme_dirs", "locales_dir", "locale"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str", "babel.core.Locale"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_merged_translations", "ret_type": {".class": "UnionType", "items": ["babel.support.Translations", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "base_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mkdocs.localization.base_path", "name": "base_path", "type": "builtins.str"}}, "has_babel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.localization.has_babel", "name": "has_babel", "type": "builtins.bool"}}, "install_translations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["env", "locale", "theme_dirs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.localization.install_translations", "name": "install_translations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["env", "locale", "theme_dirs"], "arg_types": ["jinja2.environment.Environment", "babel.core.Locale", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "install_translations", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jinja2": {".class": "SymbolTableNode", "cross_ref": "jinja2", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mkdocs.localization.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_locale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["locale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.localization.parse_locale", "name": "parse_locale", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["locale"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_locale", "ret_type": "babel.core.Locale", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/localization.py"}
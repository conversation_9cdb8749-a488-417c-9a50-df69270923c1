{"data_mtime": 1741470549, "dep_lines": [6, 6, 3, 4, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["mkdocstrings._internal.plugin", "mkdocstrings._internal", "warnings", "typing", "builtins", "_frozen_importlib", "_warnings", "abc", "types"], "hash": "24674362b4aaec0b50af4e1869e11d0733204c02", "id": "mkdocstrings.plugin", "ignore_all": false, "interface_hash": "4872058bba4d90ec5e7e48a76dd5e609e233e509", "mtime": 1741452390, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/plugin.py", "plugin_data": null, "size": 383, "suppressed": [], "version_id": "1.15.0"}
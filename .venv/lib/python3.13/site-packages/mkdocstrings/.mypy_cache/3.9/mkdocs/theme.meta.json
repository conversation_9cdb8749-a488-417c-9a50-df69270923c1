{"data_mtime": 1741470549, "dep_lines": [17, 18, 16, 16, 1, 3, 4, 5, 6, 8, 9, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 10, 10, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mkdocs.config.base", "mkdocs.utils.templates", "mkdocs.localization", "mkdocs.utils", "__future__", "logging", "os", "warnings", "typing", "jinja2", "yaml", "mkdocs", "builtins", "_frozen_importlib", "_typeshed", "abc", "babel", "babel.core", "jinja2.environment", "posixpath", "yaml._yaml", "yaml.composer", "yaml.constructor", "yaml.cyaml", "yaml.loader", "yaml.parser", "yaml.reader", "yaml.resolver", "yaml.scanner"], "hash": "ba53d7726777aec28c2b748da06f7f1b5172e4df", "id": "mkdocs.theme", "ignore_all": true, "interface_hash": "490e8018fdd170b43863c8c18cdcdd359489caa8", "mtime": 1731169893, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/theme.py", "plugin_data": null, "size": 5304, "suppressed": [], "version_id": "1.15.0"}
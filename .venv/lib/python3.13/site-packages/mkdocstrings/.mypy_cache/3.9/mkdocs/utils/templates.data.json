{".class": "MypyFile", "_fullname": "mkdocs.utils.templates", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ExtraScriptValue": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.config_options.ExtraScriptValue", "kind": "Gdef"}, "File": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.files.File", "kind": "Gdef"}, "Markup": {".class": "SymbolTableNode", "cross_ref": "markupsafe.Markup", "kind": "Gdef"}, "MkDocsConfig": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.defaults.MkDocsConfig", "kind": "Gdef"}, "Navigation": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.nav.Navigation", "kind": "Gdef"}, "Page": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.pages.Page", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TemplateContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.utils.templates.TemplateContext", "name": "TemplateContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.utils.templates.TemplateContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.utils.templates", "mro": ["mkdocs.utils.templates.TemplateContext", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["nav", "mkdocs.structure.nav.Navigation"], ["pages", {".class": "Instance", "args": ["mkdocs.structure.files.File"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["base_url", "builtins.str"], ["extra_css", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["extra_javascript", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["mkdocs_version", "builtins.str"], ["build_date_utc", "datetime.datetime"], ["config", "mkdocs.config.defaults.MkDocsConfig"], ["page", {".class": "UnionType", "items": ["mkdocs.structure.pages.Page", {".class": "NoneType"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": ["base_url", "build_date_utc", "config", "extra_css", "extra_javascript", "mkdocs_version", "nav", "page", "pages"]}}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.templates.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.templates.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.templates.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.templates.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.templates.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.templates.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "contextfilter": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.pass_context", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "normalize_url": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils.normalize_url", "kind": "Gdef"}, "script_tag_filter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["context", "extra_script"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mkdocs.utils.templates.script_tag_filter", "name": "script_tag_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["context", "extra_script"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, "mkdocs.config.config_options.ExtraScriptValue"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "script_tag_filter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mkdocs.utils.templates.script_tag_filter", "name": "script_tag_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["context", "extra_script"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, "mkdocs.config.config_options.ExtraScriptValue"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "script_tag_filter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "url_filter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["context", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mkdocs.utils.templates.url_filter", "name": "url_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["context", "value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "url_filter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mkdocs.utils.templates.url_filter", "name": "url_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["context", "value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "url_filter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/utils/templates.py"}
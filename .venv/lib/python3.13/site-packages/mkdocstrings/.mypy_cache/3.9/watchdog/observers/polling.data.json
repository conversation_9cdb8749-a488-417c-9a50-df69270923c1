{".class": "MypyFile", "_fullname": "watchdog.observers.polling", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseObserver": {".class": "SymbolTableNode", "cross_ref": "watchdog.observers.api.BaseObserver", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DEFAULT_EMITTER_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "watchdog.observers.api.DEFAULT_EMITTER_TIMEOUT", "kind": "Gdef"}, "DEFAULT_OBSERVER_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "watchdog.observers.api.DEFAULT_OBSERVER_TIMEOUT", "kind": "Gdef"}, "DirCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "watchdog.events.DirCreatedEvent", "kind": "Gdef"}, "DirDeletedEvent": {".class": "SymbolTableNode", "cross_ref": "watchdog.events.DirDeletedEvent", "kind": "Gdef"}, "DirModifiedEvent": {".class": "SymbolTableNode", "cross_ref": "watchdog.events.DirModifiedEvent", "kind": "Gdef"}, "DirMovedEvent": {".class": "SymbolTableNode", "cross_ref": "watchdog.events.DirMovedEvent", "kind": "Gdef"}, "DirectorySnapshot": {".class": "SymbolTableNode", "cross_ref": "watchdog.utils.dirsnapshot.DirectorySnapshot", "kind": "Gdef"}, "DirectorySnapshotDiff": {".class": "SymbolTableNode", "cross_ref": "watchdog.utils.dirsnapshot.DirectorySnapshotDiff", "kind": "Gdef"}, "EmptyDirectorySnapshot": {".class": "SymbolTableNode", "cross_ref": "watchdog.utils.dirsnapshot.EmptyDirectorySnapshot", "kind": "Gdef"}, "EventEmitter": {".class": "SymbolTableNode", "cross_ref": "watchdog.observers.api.EventEmitter", "kind": "Gdef"}, "EventQueue": {".class": "SymbolTableNode", "cross_ref": "watchdog.observers.api.EventQueue", "kind": "Gdef"}, "FileCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "watchdog.events.FileCreatedEvent", "kind": "Gdef"}, "FileDeletedEvent": {".class": "SymbolTableNode", "cross_ref": "watchdog.events.FileDeletedEvent", "kind": "Gdef"}, "FileModifiedEvent": {".class": "SymbolTableNode", "cross_ref": "watchdog.events.FileModifiedEvent", "kind": "Gdef"}, "FileMovedEvent": {".class": "SymbolTableNode", "cross_ref": "watchdog.events.FileMovedEvent", "kind": "Gdef"}, "FileSystemEvent": {".class": "SymbolTableNode", "cross_ref": "watchdog.events.FileSystemEvent", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "ObservedWatch": {".class": "SymbolTableNode", "cross_ref": "watchdog.observers.api.ObservedWatch", "kind": "Gdef"}, "PollingEmitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["watchdog.observers.api.EventEmitter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "watchdog.observers.polling.PollingEmitter", "name": "PollingEmitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "watchdog.observers.polling.PollingEmitter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "watchdog.observers.polling", "mro": ["watchdog.observers.polling.PollingEmitter", "watchdog.observers.api.EventEmitter", "watchdog.utils.BaseThread", "threading.Thread", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["self", "event_queue", "watch", "timeout", "event_filter", "stat", "listdir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.polling.PollingEmitter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5], "arg_names": ["self", "event_queue", "watch", "timeout", "event_filter", "stat", "listdir"], "arg_types": ["watchdog.observers.polling.PollingEmitter", "watchdog.observers.api.EventQueue", "watchdog.observers.api.ObservedWatch", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "watchdog.events.FileSystemEvent"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "os.stat_result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.<PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PollingEmitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.polling.PollingEmitter._lock", "name": "_lock", "type": "_thread.LockType"}}, "_snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "watchdog.observers.polling.PollingEmitter._snapshot", "name": "_snapshot", "type": "watchdog.utils.dirsnapshot.DirectorySnapshot"}}, "_take_snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "watchdog.observers.polling.PollingEmitter._take_snapshot", "name": "_take_snapshot", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "watchdog.utils.dirsnapshot.DirectorySnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_thread_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.polling.PollingEmitter.on_thread_start", "name": "on_thread_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.polling.PollingEmitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_thread_start of PollingEmitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "queue_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.polling.PollingEmitter.queue_events", "name": "queue_events", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "timeout"], "arg_types": ["watchdog.observers.polling.PollingEmitter", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "queue_events of PollingEmitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "watchdog.observers.polling.PollingEmitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "watchdog.observers.polling.PollingEmitter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PollingObserver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["watchdog.observers.api.BaseObserver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "watchdog.observers.polling.PollingObserver", "name": "PollingObserver", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "watchdog.observers.polling.PollingObserver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "watchdog.observers.polling", "mro": ["watchdog.observers.polling.PollingObserver", "watchdog.observers.api.BaseObserver", "watchdog.observers.api.<PERSON>er", "watchdog.utils.BaseThread", "threading.Thread", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.polling.PollingObserver.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "timeout"], "arg_types": ["watchdog.observers.polling.PollingObserver", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PollingObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "watchdog.observers.polling.PollingObserver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "watchdog.observers.polling.PollingObserver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PollingObserverVFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["watchdog.observers.api.BaseObserver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "watchdog.observers.polling.PollingObserverVFS", "name": "PollingObserverVFS", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "watchdog.observers.polling.PollingObserverVFS", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "watchdog.observers.polling", "mro": ["watchdog.observers.polling.PollingObserverVFS", "watchdog.observers.api.BaseObserver", "watchdog.observers.api.<PERSON>er", "watchdog.utils.BaseThread", "threading.Thread", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "stat", "listdir", "polling_interval"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.polling.PollingObserverVFS.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "stat", "listdir", "polling_interval"], "arg_types": ["watchdog.observers.polling.PollingObserverVFS", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "os.stat_result"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.<PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PollingObserverVFS", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "watchdog.observers.polling.PollingObserverVFS.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "watchdog.observers.polling.PollingObserverVFS", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.polling.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.polling.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.polling.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.polling.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.polling.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.polling.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/watchdog/observers/polling.py"}
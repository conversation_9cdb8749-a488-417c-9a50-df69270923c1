{".class": "MypyFile", "_fullname": "zipimport", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "CodeType": {".class": "SymbolTableNode", "cross_ref": "types.CodeType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModuleSpec": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.ModuleSpec", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ResourceReader": {".class": "SymbolTableNode", "cross_ref": "importlib.abc.ResourceReader", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrOrBytesPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ZipImportError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ImportError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "zipimport.ZipImportError", "name": "ZipImportError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "zipimport.ZipImportError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "zipimport", "mro": ["zipimport.ZipImportError", "builtins.ImportError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zipimport.ZipImportError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zipimport.ZipImportError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LoaderBasics": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "zipimport._LoaderBasics", "line": 11, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.object"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "zipimport.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zipimport.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zipimport.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zipimport.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zipimport.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zipimport.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "zipimport.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "zipimporter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "zipimport.zipimporter", "name": "zipimporter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "zipimport.zipimporter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "zipimport", "mro": ["zipimport.zipimporter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zipimport.zipimporter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["zipimport.zipimporter", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of zipimporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "archive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "zipimport.zipimporter.archive", "name": "archive", "type": "builtins.str"}}, "find_loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "fullname", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zipimport.zipimporter.find_loader", "name": "find_loader", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "fullname", "path"], "arg_types": ["zipimport.zipimporter", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_loader of zipimporter", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["zipimport.zipimporter", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "fullname", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zipimport.zipimporter.find_module", "name": "find_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "fullname", "path"], "arg_types": ["zipimport.zipimporter", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_module of zipimporter", "ret_type": {".class": "UnionType", "items": ["zipimport.zipimporter", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zipimport.zipimporter.get_code", "name": "get_code", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "arg_types": ["zipimport.zipimporter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_code of zipimporter", "ret_type": "types.CodeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pathname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zipimport.zipimporter.get_data", "name": "get_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pathname"], "arg_types": ["zipimport.zipimporter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_data of zipimporter", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zipimport.zipimporter.get_filename", "name": "get_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "arg_types": ["zipimport.zipimporter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_filename of zipimporter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_reader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zipimport.zipimporter.get_resource_reader", "name": "get_resource_reader", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "arg_types": ["zipimport.zipimporter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_reader of zipimporter", "ret_type": {".class": "UnionType", "items": ["importlib.abc.ResourceReader", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zipimport.zipimporter.get_source", "name": "get_source", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "arg_types": ["zipimport.zipimporter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_source of zipimporter", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_package": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "zipimport.zipimporter.is_package", "name": "is_package", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "arg_types": ["zipimport.zipimporter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_package of zipimporter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "dataclass_transform_spec": null, "deprecated": "function zipimport.zipimporter.load_module is deprecated: Deprecated since 3.10; use exec_module() instead", "flags": ["is_decorated"], "fullname": "zipimport.zipimporter.load_module", "name": "load_module", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "arg_types": ["zipimport.zipimporter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_module of zipimporter", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "zipimport.zipimporter.load_module", "name": "load_module", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fullname"], "arg_types": ["zipimport.zipimporter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_module of zipimporter", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "zipimport.zipimporter.prefix", "name": "prefix", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "zipimport.zipimporter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "zipimport.zipimporter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "path": "/home/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/zipimport.pyi"}
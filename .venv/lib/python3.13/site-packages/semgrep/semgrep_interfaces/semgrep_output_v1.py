"""Generated by atdpy from type definitions in semgrep_output_v1.atd.

This implements classes for the types defined in 'semgrep_output_v1.atd', providing
methods and functions to convert data from/to JSON.
"""

# Disable flake8 entirely on this file:
# flake8: noqa

# Import annotations to allow forward references
from __future__ import annotations
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, NoReturn, Optional, Tuple, Union

import json

############################################################################
# Private functions
############################################################################


def _atd_missing_json_field(type_name: str, json_field_name: str) -> NoReturn:
    raise ValueError(f"missing field '{json_field_name}'"
                     f" in JSON object of type '{type_name}'")


def _atd_bad_json(expected_type: str, json_value: Any) -> NoReturn:
    value_str = str(json_value)
    if len(value_str) > 200:
        value_str = value_str[:200] + '…'

    raise ValueError(f"incompatible JSON value where"
                     f" type '{expected_type}' was expected: '{value_str}'")


def _atd_bad_python(expected_type: str, json_value: Any) -> NoReturn:
    value_str = str(json_value)
    if len(value_str) > 200:
        value_str = value_str[:200] + '…'

    raise ValueError(f"incompatible Python value where"
                     f" type '{expected_type}' was expected: '{value_str}'")


def _atd_read_unit(x: Any) -> None:
    if x is None:
        return x
    else:
        _atd_bad_json('unit', x)


def _atd_read_bool(x: Any) -> bool:
    if isinstance(x, bool):
        return x
    else:
        _atd_bad_json('bool', x)


def _atd_read_int(x: Any) -> int:
    if isinstance(x, int):
        return x
    else:
        _atd_bad_json('int', x)


def _atd_read_float(x: Any) -> float:
    if isinstance(x, (int, float)):
        return x
    else:
        _atd_bad_json('float', x)


def _atd_read_string(x: Any) -> str:
    if isinstance(x, str):
        return x
    else:
        _atd_bad_json('str', x)


def _atd_read_list(
            read_elt: Callable[[Any], Any]
        ) -> Callable[[List[Any]], List[Any]]:
    def read_list(elts: List[Any]) -> List[Any]:
        if isinstance(elts, list):
            return [read_elt(elt) for elt in elts]
        else:
            _atd_bad_json('array', elts)
    return read_list


def _atd_read_assoc_array_into_dict(
            read_key: Callable[[Any], Any],
            read_value: Callable[[Any], Any],
        ) -> Callable[[List[Any]], Dict[Any, Any]]:
    def read_assoc(elts: List[List[Any]]) -> Dict[str, Any]:
        if isinstance(elts, list):
            return {read_key(elt[0]): read_value(elt[1]) for elt in elts}
        else:
            _atd_bad_json('array', elts)
            raise AssertionError('impossible')  # keep mypy happy
    return read_assoc


def _atd_read_assoc_object_into_dict(
            read_value: Callable[[Any], Any]
        ) -> Callable[[Dict[str, Any]], Dict[str, Any]]:
    def read_assoc(elts: Dict[str, Any]) -> Dict[str, Any]:
        if isinstance(elts, dict):
            return {_atd_read_string(k): read_value(v)
                    for k, v in elts.items()}
        else:
            _atd_bad_json('object', elts)
            raise AssertionError('impossible')  # keep mypy happy
    return read_assoc


def _atd_read_assoc_object_into_list(
            read_value: Callable[[Any], Any]
        ) -> Callable[[Dict[str, Any]], List[Tuple[str, Any]]]:
    def read_assoc(elts: Dict[str, Any]) -> List[Tuple[str, Any]]:
        if isinstance(elts, dict):
            return [(_atd_read_string(k), read_value(v))
                    for k, v in elts.items()]
        else:
            _atd_bad_json('object', elts)
            raise AssertionError('impossible')  # keep mypy happy
    return read_assoc


def _atd_read_nullable(read_elt: Callable[[Any], Any]) \
        -> Callable[[Optional[Any]], Optional[Any]]:
    def read_nullable(x: Any) -> Any:
        if x is None:
            return None
        else:
            return read_elt(x)
    return read_nullable


def _atd_read_option(read_elt: Callable[[Any], Any]) \
        -> Callable[[Optional[Any]], Optional[Any]]:
    def read_option(x: Any) -> Any:
        if x == 'None':
            return None
        elif isinstance(x, List) and len(x) == 2 and x[0] == 'Some':
            return read_elt(x[1])
        else:
            _atd_bad_json('option', x)
            raise AssertionError('impossible')  # keep mypy happy
    return read_option


def _atd_write_unit(x: Any) -> None:
    if x is None:
        return x
    else:
        _atd_bad_python('unit', x)


def _atd_write_bool(x: Any) -> bool:
    if isinstance(x, bool):
        return x
    else:
        _atd_bad_python('bool', x)


def _atd_write_int(x: Any) -> int:
    if isinstance(x, int):
        return x
    else:
        _atd_bad_python('int', x)


def _atd_write_float(x: Any) -> float:
    if isinstance(x, (int, float)):
        return x
    else:
        _atd_bad_python('float', x)


def _atd_write_string(x: Any) -> str:
    if isinstance(x, str):
        return x
    else:
        _atd_bad_python('str', x)


def _atd_write_list(
            write_elt: Callable[[Any], Any]
        ) -> Callable[[List[Any]], List[Any]]:
    def write_list(elts: List[Any]) -> List[Any]:
        if isinstance(elts, list):
            return [write_elt(elt) for elt in elts]
        else:
            _atd_bad_python('list', elts)
    return write_list


def _atd_write_assoc_dict_to_array(
            write_key: Callable[[Any], Any],
            write_value: Callable[[Any], Any]
        ) -> Callable[[Dict[Any, Any]], List[Tuple[Any, Any]]]:
    def write_assoc(elts: Dict[str, Any]) -> List[Tuple[str, Any]]:
        if isinstance(elts, dict):
            return [(write_key(k), write_value(v)) for k, v in elts.items()]
        else:
            _atd_bad_python('Dict[str, <value type>]]', elts)
            raise AssertionError('impossible')  # keep mypy happy
    return write_assoc


def _atd_write_assoc_dict_to_object(
            write_value: Callable[[Any], Any]
        ) -> Callable[[Dict[str, Any]], Dict[str, Any]]:
    def write_assoc(elts: Dict[str, Any]) -> Dict[str, Any]:
        if isinstance(elts, dict):
            return {_atd_write_string(k): write_value(v)
                    for k, v in elts.items()}
        else:
            _atd_bad_python('Dict[str, <value type>]', elts)
            raise AssertionError('impossible')  # keep mypy happy
    return write_assoc


def _atd_write_assoc_list_to_object(
            write_value: Callable[[Any], Any],
        ) -> Callable[[List[Any]], Dict[str, Any]]:
    def write_assoc(elts: List[List[Any]]) -> Dict[str, Any]:
        if isinstance(elts, list):
            return {_atd_write_string(elt[0]): write_value(elt[1])
                    for elt in elts}
        else:
            _atd_bad_python('List[Tuple[<key type>, <value type>]]', elts)
            raise AssertionError('impossible')  # keep mypy happy
    return write_assoc


def _atd_write_nullable(write_elt: Callable[[Any], Any]) \
        -> Callable[[Optional[Any]], Optional[Any]]:
    def write_nullable(x: Any) -> Any:
        if x is None:
            return None
        else:
            return write_elt(x)
    return write_nullable


def _atd_write_option(write_elt: Callable[[Any], Any]) \
        -> Callable[[Optional[Any]], Optional[Any]]:
    def write_option(x: Any) -> Any:
        if x is None:
            return 'None'
        else:
            return ['Some', write_elt(x)]
    return write_option


############################################################################
# Public classes
############################################################################


@dataclass
class Datetime:
    """Original type: datetime"""

    value: str

    @classmethod
    def from_json(cls, x: Any) -> 'Datetime':
        return cls(_atd_read_string(x))

    def to_json(self) -> Any:
        return _atd_write_string(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Datetime':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class DependencyChild:
    """Original type: dependency_child = { ... }"""

    package: str
    version: str

    @classmethod
    def from_json(cls, x: Any) -> 'DependencyChild':
        if isinstance(x, dict):
            return cls(
                package=_atd_read_string(x['package']) if 'package' in x else _atd_missing_json_field('DependencyChild', 'package'),
                version=_atd_read_string(x['version']) if 'version' in x else _atd_missing_json_field('DependencyChild', 'version'),
            )
        else:
            _atd_bad_json('DependencyChild', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['package'] = _atd_write_string(self.package)
        res['version'] = _atd_write_string(self.version)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'DependencyChild':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Direct:
    """Original type: dependency_kind = [ ... | Direct | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Direct'

    @staticmethod
    def to_json() -> Any:
        return 'direct'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Transitive:
    """Original type: dependency_kind = [ ... | Transitive | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Transitive'

    @staticmethod
    def to_json() -> Any:
        return 'transitive'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Unknown:
    """Original type: dependency_kind = [ ... | Unknown | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Unknown'

    @staticmethod
    def to_json() -> Any:
        return 'unknown'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class DependencyKind:
    """Original type: dependency_kind = [ ... ]"""

    value: Union[Direct, Transitive, Unknown]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'DependencyKind':
        if isinstance(x, str):
            if x == 'direct':
                return cls(Direct())
            if x == 'transitive':
                return cls(Transitive())
            if x == 'unknown':
                return cls(Unknown())
            _atd_bad_json('DependencyKind', x)
        _atd_bad_json('DependencyKind', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'DependencyKind':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Npm:
    """Original type: ecosystem = [ ... | Npm | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Npm'

    @staticmethod
    def to_json() -> Any:
        return 'npm'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Pypi:
    """Original type: ecosystem = [ ... | Pypi | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Pypi'

    @staticmethod
    def to_json() -> Any:
        return 'pypi'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Gem:
    """Original type: ecosystem = [ ... | Gem | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Gem'

    @staticmethod
    def to_json() -> Any:
        return 'gem'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Gomod:
    """Original type: ecosystem = [ ... | Gomod | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Gomod'

    @staticmethod
    def to_json() -> Any:
        return 'gomod'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Cargo:
    """Original type: ecosystem = [ ... | Cargo | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Cargo'

    @staticmethod
    def to_json() -> Any:
        return 'cargo'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Maven:
    """Original type: ecosystem = [ ... | Maven | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Maven'

    @staticmethod
    def to_json() -> Any:
        return 'maven'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Composer:
    """Original type: ecosystem = [ ... | Composer | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Composer'

    @staticmethod
    def to_json() -> Any:
        return 'composer'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Nuget:
    """Original type: ecosystem = [ ... | Nuget | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Nuget'

    @staticmethod
    def to_json() -> Any:
        return 'nuget'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Pub:
    """Original type: ecosystem = [ ... | Pub | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Pub'

    @staticmethod
    def to_json() -> Any:
        return 'pub'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class SwiftPM:
    """Original type: ecosystem = [ ... | SwiftPM | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SwiftPM'

    @staticmethod
    def to_json() -> Any:
        return 'swiftpm'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Cocoapods:
    """Original type: ecosystem = [ ... | Cocoapods | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Cocoapods'

    @staticmethod
    def to_json() -> Any:
        return 'cocoapods'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Mix:
    """Original type: ecosystem = [ ... | Mix | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Mix'

    @staticmethod
    def to_json() -> Any:
        return 'mix'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Hex:
    """Original type: ecosystem = [ ... | Hex | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Hex'

    @staticmethod
    def to_json() -> Any:
        return 'hex'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Opam:
    """Original type: ecosystem = [ ... | Opam | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Opam'

    @staticmethod
    def to_json() -> Any:
        return 'opam'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Ecosystem:
    """Original type: ecosystem = [ ... ]"""

    value: Union[Npm, Pypi, Gem, Gomod, Cargo, Maven, Composer, Nuget, Pub, SwiftPM, Cocoapods, Mix, Hex, Opam]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'Ecosystem':
        if isinstance(x, str):
            if x == 'npm':
                return cls(Npm())
            if x == 'pypi':
                return cls(Pypi())
            if x == 'gem':
                return cls(Gem())
            if x == 'gomod':
                return cls(Gomod())
            if x == 'cargo':
                return cls(Cargo())
            if x == 'maven':
                return cls(Maven())
            if x == 'composer':
                return cls(Composer())
            if x == 'nuget':
                return cls(Nuget())
            if x == 'pub':
                return cls(Pub())
            if x == 'swiftpm':
                return cls(SwiftPM())
            if x == 'cocoapods':
                return cls(Cocoapods())
            if x == 'mix':
                return cls(Mix())
            if x == 'hex':
                return cls(Hex())
            if x == 'opam':
                return cls(Opam())
            _atd_bad_json('Ecosystem', x)
        _atd_bad_json('Ecosystem', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'Ecosystem':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class Fpath:
    """Original type: fpath"""

    value: str

    @classmethod
    def from_json(cls, x: Any) -> 'Fpath':
        return cls(_atd_read_string(x))

    def to_json(self) -> Any:
        return _atd_write_string(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Fpath':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class FoundDependency:
    """Original type: found_dependency = { ... }"""

    package: str
    version: str
    ecosystem: Ecosystem
    allowed_hashes: Dict[str, List[str]]
    transitivity: DependencyKind
    resolved_url: Optional[str] = None
    manifest_path: Optional[Fpath] = None
    lockfile_path: Optional[Fpath] = None
    line_number: Optional[int] = None
    children: Optional[List[DependencyChild]] = None
    git_ref: Optional[str] = None

    @classmethod
    def from_json(cls, x: Any) -> 'FoundDependency':
        if isinstance(x, dict):
            return cls(
                package=_atd_read_string(x['package']) if 'package' in x else _atd_missing_json_field('FoundDependency', 'package'),
                version=_atd_read_string(x['version']) if 'version' in x else _atd_missing_json_field('FoundDependency', 'version'),
                ecosystem=Ecosystem.from_json(x['ecosystem']) if 'ecosystem' in x else _atd_missing_json_field('FoundDependency', 'ecosystem'),
                allowed_hashes=_atd_read_assoc_object_into_dict(_atd_read_list(_atd_read_string))(x['allowed_hashes']) if 'allowed_hashes' in x else _atd_missing_json_field('FoundDependency', 'allowed_hashes'),
                transitivity=DependencyKind.from_json(x['transitivity']) if 'transitivity' in x else _atd_missing_json_field('FoundDependency', 'transitivity'),
                resolved_url=_atd_read_string(x['resolved_url']) if 'resolved_url' in x else None,
                manifest_path=Fpath.from_json(x['manifest_path']) if 'manifest_path' in x else None,
                lockfile_path=Fpath.from_json(x['lockfile_path']) if 'lockfile_path' in x else None,
                line_number=_atd_read_int(x['line_number']) if 'line_number' in x else None,
                children=_atd_read_list(DependencyChild.from_json)(x['children']) if 'children' in x else None,
                git_ref=_atd_read_string(x['git_ref']) if 'git_ref' in x else None,
            )
        else:
            _atd_bad_json('FoundDependency', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['package'] = _atd_write_string(self.package)
        res['version'] = _atd_write_string(self.version)
        res['ecosystem'] = (lambda x: x.to_json())(self.ecosystem)
        res['allowed_hashes'] = _atd_write_assoc_dict_to_object(_atd_write_list(_atd_write_string))(self.allowed_hashes)
        res['transitivity'] = (lambda x: x.to_json())(self.transitivity)
        if self.resolved_url is not None:
            res['resolved_url'] = _atd_write_string(self.resolved_url)
        if self.manifest_path is not None:
            res['manifest_path'] = (lambda x: x.to_json())(self.manifest_path)
        if self.lockfile_path is not None:
            res['lockfile_path'] = (lambda x: x.to_json())(self.lockfile_path)
        if self.line_number is not None:
            res['line_number'] = _atd_write_int(self.line_number)
        if self.children is not None:
            res['children'] = _atd_write_list((lambda x: x.to_json()))(self.children)
        if self.git_ref is not None:
            res['git_ref'] = _atd_write_string(self.git_ref)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'FoundDependency':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PipRequirementsTxt:
    """Original type: lockfile_kind = [ ... | PipRequirementsTxt | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PipRequirementsTxt'

    @staticmethod
    def to_json() -> Any:
        return 'PipRequirementsTxt'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PoetryLock:
    """Original type: lockfile_kind = [ ... | PoetryLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PoetryLock'

    @staticmethod
    def to_json() -> Any:
        return 'PoetryLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PipfileLock:
    """Original type: lockfile_kind = [ ... | PipfileLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PipfileLock'

    @staticmethod
    def to_json() -> Any:
        return 'PipfileLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class UvLock:
    """Original type: lockfile_kind = [ ... | UvLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'UvLock'

    @staticmethod
    def to_json() -> Any:
        return 'UvLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class NpmPackageLockJson:
    """Original type: lockfile_kind = [ ... | NpmPackageLockJson | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'NpmPackageLockJson'

    @staticmethod
    def to_json() -> Any:
        return 'NpmPackageLockJson'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class YarnLock:
    """Original type: lockfile_kind = [ ... | YarnLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'YarnLock'

    @staticmethod
    def to_json() -> Any:
        return 'YarnLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PnpmLock:
    """Original type: lockfile_kind = [ ... | PnpmLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PnpmLock'

    @staticmethod
    def to_json() -> Any:
        return 'PnpmLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class BunLock:
    """Original type: lockfile_kind = [ ... | BunLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'BunLock'

    @staticmethod
    def to_json() -> Any:
        return 'BunLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class BunBinaryLock:
    """Original type: lockfile_kind = [ ... | BunBinaryLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'BunBinaryLock'

    @staticmethod
    def to_json() -> Any:
        return 'BunBinaryLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class GemfileLock:
    """Original type: lockfile_kind = [ ... | GemfileLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'GemfileLock'

    @staticmethod
    def to_json() -> Any:
        return 'GemfileLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class GoModLock:
    """Original type: lockfile_kind = [ ... | GoModLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'GoModLock'

    @staticmethod
    def to_json() -> Any:
        return 'GoMod'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CargoLock:
    """Original type: lockfile_kind = [ ... | CargoLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CargoLock'

    @staticmethod
    def to_json() -> Any:
        return 'CargoLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class MavenDepTree:
    """Original type: lockfile_kind = [ ... | MavenDepTree | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'MavenDepTree'

    @staticmethod
    def to_json() -> Any:
        return 'MavenDepTree'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class GradleLockfile:
    """Original type: lockfile_kind = [ ... | GradleLockfile | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'GradleLockfile'

    @staticmethod
    def to_json() -> Any:
        return 'GradleLockfile'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ComposerLock:
    """Original type: lockfile_kind = [ ... | ComposerLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ComposerLock'

    @staticmethod
    def to_json() -> Any:
        return 'ComposerLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class NugetPackagesLockJson:
    """Original type: lockfile_kind = [ ... | NugetPackagesLockJson | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'NugetPackagesLockJson'

    @staticmethod
    def to_json() -> Any:
        return 'NugetPackagesLockJson'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PubspecLock:
    """Original type: lockfile_kind = [ ... | PubspecLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PubspecLock'

    @staticmethod
    def to_json() -> Any:
        return 'PubspecLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class SwiftPackageResolved:
    """Original type: lockfile_kind = [ ... | SwiftPackageResolved | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SwiftPackageResolved'

    @staticmethod
    def to_json() -> Any:
        return 'SwiftPackageResolved'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PodfileLock:
    """Original type: lockfile_kind = [ ... | PodfileLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PodfileLock'

    @staticmethod
    def to_json() -> Any:
        return 'PodfileLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class MixLock:
    """Original type: lockfile_kind = [ ... | MixLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'MixLock'

    @staticmethod
    def to_json() -> Any:
        return 'MixLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ConanLock:
    """Original type: lockfile_kind = [ ... | ConanLock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ConanLock'

    @staticmethod
    def to_json() -> Any:
        return 'ConanLock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class OpamLocked:
    """Original type: lockfile_kind = [ ... | OpamLocked | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'OpamLocked'

    @staticmethod
    def to_json() -> Any:
        return 'OpamLocked'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class LockfileKind:
    """Original type: lockfile_kind = [ ... ]"""

    value: Union[PipRequirementsTxt, PoetryLock, PipfileLock, UvLock, NpmPackageLockJson, YarnLock, PnpmLock, BunLock, BunBinaryLock, GemfileLock, GoModLock, CargoLock, MavenDepTree, GradleLockfile, ComposerLock, NugetPackagesLockJson, PubspecLock, SwiftPackageResolved, PodfileLock, MixLock, ConanLock, OpamLocked]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'LockfileKind':
        if isinstance(x, str):
            if x == 'PipRequirementsTxt':
                return cls(PipRequirementsTxt())
            if x == 'PoetryLock':
                return cls(PoetryLock())
            if x == 'PipfileLock':
                return cls(PipfileLock())
            if x == 'UvLock':
                return cls(UvLock())
            if x == 'NpmPackageLockJson':
                return cls(NpmPackageLockJson())
            if x == 'YarnLock':
                return cls(YarnLock())
            if x == 'PnpmLock':
                return cls(PnpmLock())
            if x == 'BunLock':
                return cls(BunLock())
            if x == 'BunBinaryLock':
                return cls(BunBinaryLock())
            if x == 'GemfileLock':
                return cls(GemfileLock())
            if x == 'GoMod':
                return cls(GoModLock())
            if x == 'CargoLock':
                return cls(CargoLock())
            if x == 'MavenDepTree':
                return cls(MavenDepTree())
            if x == 'GradleLockfile':
                return cls(GradleLockfile())
            if x == 'ComposerLock':
                return cls(ComposerLock())
            if x == 'NugetPackagesLockJson':
                return cls(NugetPackagesLockJson())
            if x == 'PubspecLock':
                return cls(PubspecLock())
            if x == 'SwiftPackageResolved':
                return cls(SwiftPackageResolved())
            if x == 'PodfileLock':
                return cls(PodfileLock())
            if x == 'MixLock':
                return cls(MixLock())
            if x == 'ConanLock':
                return cls(ConanLock())
            if x == 'OpamLocked':
                return cls(OpamLocked())
            _atd_bad_json('LockfileKind', x)
        _atd_bad_json('LockfileKind', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'LockfileKind':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Lockfile:
    """Original type: lockfile = { ... }"""

    kind: LockfileKind
    path: Fpath

    @classmethod
    def from_json(cls, x: Any) -> 'Lockfile':
        if isinstance(x, dict):
            return cls(
                kind=LockfileKind.from_json(x['kind']) if 'kind' in x else _atd_missing_json_field('Lockfile', 'kind'),
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('Lockfile', 'path'),
            )
        else:
            _atd_bad_json('Lockfile', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['kind'] = (lambda x: x.to_json())(self.kind)
        res['path'] = (lambda x: x.to_json())(self.path)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Lockfile':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RequirementsIn:
    """Original type: manifest_kind = [ ... | RequirementsIn | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RequirementsIn'

    @staticmethod
    def to_json() -> Any:
        return 'RequirementsIn'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class SetupPy:
    """Original type: manifest_kind = [ ... | SetupPy | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SetupPy'

    @staticmethod
    def to_json() -> Any:
        return 'SetupPy'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PackageJson:
    """Original type: manifest_kind = [ ... | PackageJson | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PackageJson'

    @staticmethod
    def to_json() -> Any:
        return 'PackageJson'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Gemfile:
    """Original type: manifest_kind = [ ... | Gemfile | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Gemfile'

    @staticmethod
    def to_json() -> Any:
        return 'Gemfile'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class GoModManifest:
    """Original type: manifest_kind = [ ... | GoModManifest | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'GoModManifest'

    @staticmethod
    def to_json() -> Any:
        return 'GoMod'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CargoToml:
    """Original type: manifest_kind = [ ... | CargoToml | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CargoToml'

    @staticmethod
    def to_json() -> Any:
        return 'CargoToml'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PomXml:
    """Original type: manifest_kind = [ ... | PomXml | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PomXml'

    @staticmethod
    def to_json() -> Any:
        return 'PomXml'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class BuildGradle:
    """Original type: manifest_kind = [ ... | BuildGradle | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'BuildGradle'

    @staticmethod
    def to_json() -> Any:
        return 'BuildGradle'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class SettingsGradle:
    """Original type: manifest_kind = [ ... | SettingsGradle | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SettingsGradle'

    @staticmethod
    def to_json() -> Any:
        return 'SettingsGradle'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ComposerJson:
    """Original type: manifest_kind = [ ... | ComposerJson | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ComposerJson'

    @staticmethod
    def to_json() -> Any:
        return 'ComposerJson'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class NugetManifestJson:
    """Original type: manifest_kind = [ ... | NugetManifestJson | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'NugetManifestJson'

    @staticmethod
    def to_json() -> Any:
        return 'NugetManifestJson'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PubspecYaml:
    """Original type: manifest_kind = [ ... | PubspecYaml | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PubspecYaml'

    @staticmethod
    def to_json() -> Any:
        return 'PubspecYaml'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PackageSwift:
    """Original type: manifest_kind = [ ... | PackageSwift | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PackageSwift'

    @staticmethod
    def to_json() -> Any:
        return 'PackageSwift'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Podfile:
    """Original type: manifest_kind = [ ... | Podfile | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Podfile'

    @staticmethod
    def to_json() -> Any:
        return 'Podfile'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class MixExs:
    """Original type: manifest_kind = [ ... | MixExs | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'MixExs'

    @staticmethod
    def to_json() -> Any:
        return 'MixExs'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Pipfile:
    """Original type: manifest_kind = [ ... | Pipfile | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Pipfile'

    @staticmethod
    def to_json() -> Any:
        return 'Pipfile'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PyprojectToml:
    """Original type: manifest_kind = [ ... | PyprojectToml | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PyprojectToml'

    @staticmethod
    def to_json() -> Any:
        return 'PyprojectToml'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ConanFileTxt:
    """Original type: manifest_kind = [ ... | ConanFileTxt | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ConanFileTxt'

    @staticmethod
    def to_json() -> Any:
        return 'ConanFileTxt'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ConanFilePy:
    """Original type: manifest_kind = [ ... | ConanFilePy | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ConanFilePy'

    @staticmethod
    def to_json() -> Any:
        return 'ConanFilePy'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Csproj:
    """Original type: manifest_kind = [ ... | Csproj | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Csproj'

    @staticmethod
    def to_json() -> Any:
        return 'Csproj'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class OpamFile:
    """Original type: manifest_kind = [ ... | OpamFile | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'OpamFile'

    @staticmethod
    def to_json() -> Any:
        return 'OpamFile'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ManifestKind:
    """Original type: manifest_kind = [ ... ]"""

    value: Union[RequirementsIn, SetupPy, PackageJson, Gemfile, GoModManifest, CargoToml, PomXml, BuildGradle, SettingsGradle, ComposerJson, NugetManifestJson, PubspecYaml, PackageSwift, Podfile, MixExs, Pipfile, PyprojectToml, ConanFileTxt, ConanFilePy, Csproj, OpamFile]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ManifestKind':
        if isinstance(x, str):
            if x == 'RequirementsIn':
                return cls(RequirementsIn())
            if x == 'SetupPy':
                return cls(SetupPy())
            if x == 'PackageJson':
                return cls(PackageJson())
            if x == 'Gemfile':
                return cls(Gemfile())
            if x == 'GoMod':
                return cls(GoModManifest())
            if x == 'CargoToml':
                return cls(CargoToml())
            if x == 'PomXml':
                return cls(PomXml())
            if x == 'BuildGradle':
                return cls(BuildGradle())
            if x == 'SettingsGradle':
                return cls(SettingsGradle())
            if x == 'ComposerJson':
                return cls(ComposerJson())
            if x == 'NugetManifestJson':
                return cls(NugetManifestJson())
            if x == 'PubspecYaml':
                return cls(PubspecYaml())
            if x == 'PackageSwift':
                return cls(PackageSwift())
            if x == 'Podfile':
                return cls(Podfile())
            if x == 'MixExs':
                return cls(MixExs())
            if x == 'Pipfile':
                return cls(Pipfile())
            if x == 'PyprojectToml':
                return cls(PyprojectToml())
            if x == 'ConanFileTxt':
                return cls(ConanFileTxt())
            if x == 'ConanFilePy':
                return cls(ConanFilePy())
            if x == 'Csproj':
                return cls(Csproj())
            if x == 'OpamFile':
                return cls(OpamFile())
            _atd_bad_json('ManifestKind', x)
        _atd_bad_json('ManifestKind', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ManifestKind':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Manifest:
    """Original type: manifest = { ... }"""

    kind: ManifestKind
    path: Fpath

    @classmethod
    def from_json(cls, x: Any) -> 'Manifest':
        if isinstance(x, dict):
            return cls(
                kind=ManifestKind.from_json(x['kind']) if 'kind' in x else _atd_missing_json_field('Manifest', 'kind'),
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('Manifest', 'path'),
            )
        else:
            _atd_bad_json('Manifest', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['kind'] = (lambda x: x.to_json())(self.kind)
        res['path'] = (lambda x: x.to_json())(self.path)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Manifest':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Error:
    """Original type: match_severity = [ ... | Error | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Error'

    @staticmethod
    def to_json() -> Any:
        return 'ERROR'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Warning:
    """Original type: match_severity = [ ... | Warning | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Warning'

    @staticmethod
    def to_json() -> Any:
        return 'WARNING'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Experiment:
    """Original type: match_severity = [ ... | Experiment | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Experiment'

    @staticmethod
    def to_json() -> Any:
        return 'EXPERIMENT'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Inventory:
    """Original type: match_severity = [ ... | Inventory | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Inventory'

    @staticmethod
    def to_json() -> Any:
        return 'INVENTORY'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Critical:
    """Original type: match_severity = [ ... | Critical | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Critical'

    @staticmethod
    def to_json() -> Any:
        return 'CRITICAL'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class High:
    """Original type: match_severity = [ ... | High | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'High'

    @staticmethod
    def to_json() -> Any:
        return 'HIGH'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Medium:
    """Original type: match_severity = [ ... | Medium | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Medium'

    @staticmethod
    def to_json() -> Any:
        return 'MEDIUM'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Low:
    """Original type: match_severity = [ ... | Low | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Low'

    @staticmethod
    def to_json() -> Any:
        return 'LOW'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Info:
    """Original type: match_severity = [ ... | Info | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Info'

    @staticmethod
    def to_json() -> Any:
        return 'INFO'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class MatchSeverity:
    """Original type: match_severity = [ ... ]"""

    value: Union[Error, Warning, Experiment, Inventory, Critical, High, Medium, Low, Info]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'MatchSeverity':
        if isinstance(x, str):
            if x == 'ERROR':
                return cls(Error())
            if x == 'WARNING':
                return cls(Warning())
            if x == 'EXPERIMENT':
                return cls(Experiment())
            if x == 'INVENTORY':
                return cls(Inventory())
            if x == 'CRITICAL':
                return cls(Critical())
            if x == 'HIGH':
                return cls(High())
            if x == 'MEDIUM':
                return cls(Medium())
            if x == 'LOW':
                return cls(Low())
            if x == 'INFO':
                return cls(Info())
            _atd_bad_json('MatchSeverity', x)
        _atd_bad_json('MatchSeverity', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'MatchSeverity':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class And:
    """Original type: matching_operation = [ ... | And | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'And'

    @staticmethod
    def to_json() -> Any:
        return 'And'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Or:
    """Original type: matching_operation = [ ... | Or | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Or'

    @staticmethod
    def to_json() -> Any:
        return 'Or'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Inside:
    """Original type: matching_operation = [ ... | Inside | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Inside'

    @staticmethod
    def to_json() -> Any:
        return 'Inside'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Anywhere:
    """Original type: matching_operation = [ ... | Anywhere | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Anywhere'

    @staticmethod
    def to_json() -> Any:
        return 'Anywhere'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class XPat:
    """Original type: matching_operation = [ ... | XPat of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'XPat'

    def to_json(self) -> Any:
        return ['XPat', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Negation:
    """Original type: matching_operation = [ ... | Negation | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Negation'

    @staticmethod
    def to_json() -> Any:
        return 'Negation'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Filter:
    """Original type: matching_operation = [ ... | Filter of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Filter'

    def to_json(self) -> Any:
        return ['Filter', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Taint:
    """Original type: matching_operation = [ ... | Taint | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Taint'

    @staticmethod
    def to_json() -> Any:
        return 'Taint'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TaintSource:
    """Original type: matching_operation = [ ... | TaintSource | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'TaintSource'

    @staticmethod
    def to_json() -> Any:
        return 'TaintSource'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TaintSink:
    """Original type: matching_operation = [ ... | TaintSink | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'TaintSink'

    @staticmethod
    def to_json() -> Any:
        return 'TaintSink'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TaintSanitizer:
    """Original type: matching_operation = [ ... | TaintSanitizer | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'TaintSanitizer'

    @staticmethod
    def to_json() -> Any:
        return 'TaintSanitizer'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class EllipsisAndStmts:
    """Original type: matching_operation = [ ... | EllipsisAndStmts | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'EllipsisAndStmts'

    @staticmethod
    def to_json() -> Any:
        return 'EllipsisAndStmts'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ClassHeaderAndElems:
    """Original type: matching_operation = [ ... | ClassHeaderAndElems | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ClassHeaderAndElems'

    @staticmethod
    def to_json() -> Any:
        return 'ClassHeaderAndElems'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class MatchingOperation:
    """Original type: matching_operation = [ ... ]"""

    value: Union[And, Or, Inside, Anywhere, XPat, Negation, Filter, Taint, TaintSource, TaintSink, TaintSanitizer, EllipsisAndStmts, ClassHeaderAndElems]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'MatchingOperation':
        if isinstance(x, str):
            if x == 'And':
                return cls(And())
            if x == 'Or':
                return cls(Or())
            if x == 'Inside':
                return cls(Inside())
            if x == 'Anywhere':
                return cls(Anywhere())
            if x == 'Negation':
                return cls(Negation())
            if x == 'Taint':
                return cls(Taint())
            if x == 'TaintSource':
                return cls(TaintSource())
            if x == 'TaintSink':
                return cls(TaintSink())
            if x == 'TaintSanitizer':
                return cls(TaintSanitizer())
            if x == 'EllipsisAndStmts':
                return cls(EllipsisAndStmts())
            if x == 'ClassHeaderAndElems':
                return cls(ClassHeaderAndElems())
            _atd_bad_json('MatchingOperation', x)
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'XPat':
                return cls(XPat(_atd_read_string(x[1])))
            if cons == 'Filter':
                return cls(Filter(_atd_read_string(x[1])))
            _atd_bad_json('MatchingOperation', x)
        _atd_bad_json('MatchingOperation', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'MatchingOperation':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class Position:
    """Original type: position = { ... }"""

    line: int
    col: int
    offset: int = field(default_factory=lambda: 0)

    @classmethod
    def from_json(cls, x: Any) -> 'Position':
        if isinstance(x, dict):
            return cls(
                line=_atd_read_int(x['line']) if 'line' in x else _atd_missing_json_field('Position', 'line'),
                col=_atd_read_int(x['col']) if 'col' in x else _atd_missing_json_field('Position', 'col'),
                offset=_atd_read_int(x['offset']) if 'offset' in x else 0,
            )
        else:
            _atd_bad_json('Position', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['line'] = _atd_write_int(self.line)
        res['col'] = _atd_write_int(self.col)
        res['offset'] = _atd_write_int(self.offset)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Position':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Location:
    """Original type: location = { ... }"""

    path: Fpath
    start: Position
    end: Position

    @classmethod
    def from_json(cls, x: Any) -> 'Location':
        if isinstance(x, dict):
            return cls(
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('Location', 'path'),
                start=Position.from_json(x['start']) if 'start' in x else _atd_missing_json_field('Location', 'start'),
                end=Position.from_json(x['end']) if 'end' in x else _atd_missing_json_field('Location', 'end'),
            )
        else:
            _atd_bad_json('Location', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['path'] = (lambda x: x.to_json())(self.path)
        res['start'] = (lambda x: x.to_json())(self.start)
        res['end'] = (lambda x: x.to_json())(self.end)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Location':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class LocAndContent:
    """Original type: loc_and_content"""

    value: Tuple[Location, str]

    @classmethod
    def from_json(cls, x: Any) -> 'LocAndContent':
        return cls((lambda x: (Location.from_json(x[0]), _atd_read_string(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x))(x))

    def to_json(self) -> Any:
        return (lambda x: [(lambda x: x.to_json())(x[0]), _atd_write_string(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x))(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'LocAndContent':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class MatchIntermediateVar:
    """Original type: match_intermediate_var = { ... }"""

    location: Location
    content: str

    @classmethod
    def from_json(cls, x: Any) -> 'MatchIntermediateVar':
        if isinstance(x, dict):
            return cls(
                location=Location.from_json(x['location']) if 'location' in x else _atd_missing_json_field('MatchIntermediateVar', 'location'),
                content=_atd_read_string(x['content']) if 'content' in x else _atd_missing_json_field('MatchIntermediateVar', 'content'),
            )
        else:
            _atd_bad_json('MatchIntermediateVar', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['location'] = (lambda x: x.to_json())(self.location)
        res['content'] = _atd_write_string(self.content)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'MatchIntermediateVar':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ProFeature:
    """Original type: pro_feature = { ... }"""

    interproc_taint: bool
    interfile_taint: bool
    proprietary_language: bool

    @classmethod
    def from_json(cls, x: Any) -> 'ProFeature':
        if isinstance(x, dict):
            return cls(
                interproc_taint=_atd_read_bool(x['interproc_taint']) if 'interproc_taint' in x else _atd_missing_json_field('ProFeature', 'interproc_taint'),
                interfile_taint=_atd_read_bool(x['interfile_taint']) if 'interfile_taint' in x else _atd_missing_json_field('ProFeature', 'interfile_taint'),
                proprietary_language=_atd_read_bool(x['proprietary_language']) if 'proprietary_language' in x else _atd_missing_json_field('ProFeature', 'proprietary_language'),
            )
        else:
            _atd_bad_json('ProFeature', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['interproc_taint'] = _atd_write_bool(self.interproc_taint)
        res['interfile_taint'] = _atd_write_bool(self.interfile_taint)
        res['proprietary_language'] = _atd_write_bool(self.proprietary_language)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ProFeature':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class OSS:
    """Original type: engine_of_finding = [ ... | OSS | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'OSS'

    @staticmethod
    def to_json() -> Any:
        return 'OSS'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PRO:
    """Original type: engine_of_finding = [ ... | PRO | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PRO'

    @staticmethod
    def to_json() -> Any:
        return 'PRO'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PROREQUIRED:
    """Original type: engine_of_finding = [ ... | PRO_REQUIRED of ... | ... ]"""

    value: ProFeature

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PROREQUIRED'

    def to_json(self) -> Any:
        return ['PRO_REQUIRED', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class EngineOfFinding:
    """Original type: engine_of_finding = [ ... ]"""

    value: Union[OSS, PRO, PROREQUIRED]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'EngineOfFinding':
        if isinstance(x, str):
            if x == 'OSS':
                return cls(OSS())
            if x == 'PRO':
                return cls(PRO())
            _atd_bad_json('EngineOfFinding', x)
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'PRO_REQUIRED':
                return cls(PROREQUIRED(ProFeature.from_json(x[1])))
            _atd_bad_json('EngineOfFinding', x)
        _atd_bad_json('EngineOfFinding', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'EngineOfFinding':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class RawJson:
    """Original type: raw_json"""

    value: Any

    @classmethod
    def from_json(cls, x: Any) -> 'RawJson':
        return cls((lambda x: x)(x))

    def to_json(self) -> Any:
        return (lambda x: x)(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'RawJson':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RuleId:
    """Original type: rule_id"""

    value: str

    @classmethod
    def from_json(cls, x: Any) -> 'RuleId':
        return cls(_atd_read_string(x))

    def to_json(self) -> Any:
        return _atd_write_string(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'RuleId':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScaPattern:
    """Original type: sca_pattern = { ... }"""

    ecosystem: Ecosystem
    package: str
    semver_range: str

    @classmethod
    def from_json(cls, x: Any) -> 'ScaPattern':
        if isinstance(x, dict):
            return cls(
                ecosystem=Ecosystem.from_json(x['ecosystem']) if 'ecosystem' in x else _atd_missing_json_field('ScaPattern', 'ecosystem'),
                package=_atd_read_string(x['package']) if 'package' in x else _atd_missing_json_field('ScaPattern', 'package'),
                semver_range=_atd_read_string(x['semver_range']) if 'semver_range' in x else _atd_missing_json_field('ScaPattern', 'semver_range'),
            )
        else:
            _atd_bad_json('ScaPattern', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['ecosystem'] = (lambda x: x.to_json())(self.ecosystem)
        res['package'] = _atd_write_string(self.package)
        res['semver_range'] = _atd_write_string(self.semver_range)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ScaPattern':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DependencyMatch:
    """Original type: dependency_match = { ... }"""

    dependency_pattern: ScaPattern
    found_dependency: FoundDependency
    lockfile: Fpath

    @classmethod
    def from_json(cls, x: Any) -> 'DependencyMatch':
        if isinstance(x, dict):
            return cls(
                dependency_pattern=ScaPattern.from_json(x['dependency_pattern']) if 'dependency_pattern' in x else _atd_missing_json_field('DependencyMatch', 'dependency_pattern'),
                found_dependency=FoundDependency.from_json(x['found_dependency']) if 'found_dependency' in x else _atd_missing_json_field('DependencyMatch', 'found_dependency'),
                lockfile=Fpath.from_json(x['lockfile']) if 'lockfile' in x else _atd_missing_json_field('DependencyMatch', 'lockfile'),
            )
        else:
            _atd_bad_json('DependencyMatch', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['dependency_pattern'] = (lambda x: x.to_json())(self.dependency_pattern)
        res['found_dependency'] = (lambda x: x.to_json())(self.found_dependency)
        res['lockfile'] = (lambda x: x.to_json())(self.lockfile)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'DependencyMatch':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Sha1:
    """Original type: sha1"""

    value: str

    @classmethod
    def from_json(cls, x: Any) -> 'Sha1':
        return cls(_atd_read_string(x))

    def to_json(self) -> Any:
        return _atd_write_string(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Sha1':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class HistoricalInfo:
    """Original type: historical_info = { ... }"""

    git_commit: Sha1
    git_commit_timestamp: Datetime
    git_blob: Optional[Sha1] = None

    @classmethod
    def from_json(cls, x: Any) -> 'HistoricalInfo':
        if isinstance(x, dict):
            return cls(
                git_commit=Sha1.from_json(x['git_commit']) if 'git_commit' in x else _atd_missing_json_field('HistoricalInfo', 'git_commit'),
                git_commit_timestamp=Datetime.from_json(x['git_commit_timestamp']) if 'git_commit_timestamp' in x else _atd_missing_json_field('HistoricalInfo', 'git_commit_timestamp'),
                git_blob=Sha1.from_json(x['git_blob']) if 'git_blob' in x else None,
            )
        else:
            _atd_bad_json('HistoricalInfo', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['git_commit'] = (lambda x: x.to_json())(self.git_commit)
        res['git_commit_timestamp'] = (lambda x: x.to_json())(self.git_commit_timestamp)
        if self.git_blob is not None:
            res['git_blob'] = (lambda x: x.to_json())(self.git_blob)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'HistoricalInfo':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class SvalueValue:
    """Original type: svalue_value = { ... }"""

    svalue_abstract_content: str
    svalue_start: Optional[Position] = None
    svalue_end: Optional[Position] = None

    @classmethod
    def from_json(cls, x: Any) -> 'SvalueValue':
        if isinstance(x, dict):
            return cls(
                svalue_abstract_content=_atd_read_string(x['svalue_abstract_content']) if 'svalue_abstract_content' in x else _atd_missing_json_field('SvalueValue', 'svalue_abstract_content'),
                svalue_start=Position.from_json(x['svalue_start']) if 'svalue_start' in x else None,
                svalue_end=Position.from_json(x['svalue_end']) if 'svalue_end' in x else None,
            )
        else:
            _atd_bad_json('SvalueValue', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['svalue_abstract_content'] = _atd_write_string(self.svalue_abstract_content)
        if self.svalue_start is not None:
            res['svalue_start'] = (lambda x: x.to_json())(self.svalue_start)
        if self.svalue_end is not None:
            res['svalue_end'] = (lambda x: x.to_json())(self.svalue_end)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'SvalueValue':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class MetavarValue:
    """Original type: metavar_value = { ... }"""

    start: Position
    end: Position
    abstract_content: str
    propagated_value: Optional[SvalueValue] = None

    @classmethod
    def from_json(cls, x: Any) -> 'MetavarValue':
        if isinstance(x, dict):
            return cls(
                start=Position.from_json(x['start']) if 'start' in x else _atd_missing_json_field('MetavarValue', 'start'),
                end=Position.from_json(x['end']) if 'end' in x else _atd_missing_json_field('MetavarValue', 'end'),
                abstract_content=_atd_read_string(x['abstract_content']) if 'abstract_content' in x else _atd_missing_json_field('MetavarValue', 'abstract_content'),
                propagated_value=SvalueValue.from_json(x['propagated_value']) if 'propagated_value' in x else None,
            )
        else:
            _atd_bad_json('MetavarValue', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['start'] = (lambda x: x.to_json())(self.start)
        res['end'] = (lambda x: x.to_json())(self.end)
        res['abstract_content'] = _atd_write_string(self.abstract_content)
        if self.propagated_value is not None:
            res['propagated_value'] = (lambda x: x.to_json())(self.propagated_value)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'MetavarValue':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Metavars:
    """Original type: metavars"""

    value: Dict[str, MetavarValue]

    @classmethod
    def from_json(cls, x: Any) -> 'Metavars':
        return cls(_atd_read_assoc_object_into_dict(MetavarValue.from_json)(x))

    def to_json(self) -> Any:
        return _atd_write_assoc_dict_to_object((lambda x: x.to_json()))(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Metavars':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TransitiveUndetermined:
    """Original type: transitive_undetermined = { ... }"""

    explanation: Optional[str]

    @classmethod
    def from_json(cls, x: Any) -> 'TransitiveUndetermined':
        if isinstance(x, dict):
            return cls(
                explanation=_atd_read_option(_atd_read_string)(x['explanation']) if 'explanation' in x else _atd_missing_json_field('TransitiveUndetermined', 'explanation'),
            )
        else:
            _atd_bad_json('TransitiveUndetermined', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['explanation'] = _atd_write_option(_atd_write_string)(self.explanation)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TransitiveUndetermined':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TransitiveUnreachable:
    """Original type: transitive_unreachable = { ... }"""

    analyzed_packages: List[FoundDependency]
    explanation: Optional[str]

    @classmethod
    def from_json(cls, x: Any) -> 'TransitiveUnreachable':
        if isinstance(x, dict):
            return cls(
                analyzed_packages=_atd_read_list(FoundDependency.from_json)(x['analyzed_packages']) if 'analyzed_packages' in x else _atd_missing_json_field('TransitiveUnreachable', 'analyzed_packages'),
                explanation=_atd_read_option(_atd_read_string)(x['explanation']) if 'explanation' in x else _atd_missing_json_field('TransitiveUnreachable', 'explanation'),
            )
        else:
            _atd_bad_json('TransitiveUnreachable', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['analyzed_packages'] = _atd_write_list((lambda x: x.to_json()))(self.analyzed_packages)
        res['explanation'] = _atd_write_option(_atd_write_string)(self.explanation)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TransitiveUnreachable':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ConfirmedValid:
    """Original type: validation_state = [ ... | Confirmed_valid | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ConfirmedValid'

    @staticmethod
    def to_json() -> Any:
        return 'CONFIRMED_VALID'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ConfirmedInvalid:
    """Original type: validation_state = [ ... | Confirmed_invalid | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ConfirmedInvalid'

    @staticmethod
    def to_json() -> Any:
        return 'CONFIRMED_INVALID'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ValidationError:
    """Original type: validation_state = [ ... | Validation_error | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ValidationError'

    @staticmethod
    def to_json() -> Any:
        return 'VALIDATION_ERROR'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class NoValidator:
    """Original type: validation_state = [ ... | No_validator | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'NoValidator'

    @staticmethod
    def to_json() -> Any:
        return 'NO_VALIDATOR'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ValidationState:
    """Original type: validation_state = [ ... ]"""

    value: Union[ConfirmedValid, ConfirmedInvalid, ValidationError, NoValidator]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ValidationState':
        if isinstance(x, str):
            if x == 'CONFIRMED_VALID':
                return cls(ConfirmedValid())
            if x == 'CONFIRMED_INVALID':
                return cls(ConfirmedInvalid())
            if x == 'VALIDATION_ERROR':
                return cls(ValidationError())
            if x == 'NO_VALIDATOR':
                return cls(NoValidator())
            _atd_bad_json('ValidationState', x)
        _atd_bad_json('ValidationState', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ValidationState':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ManifestOnly:
    """Original type: dependency_source = [ ... | ManifestOnly of ... | ... ]"""

    value: Manifest

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ManifestOnly'

    def to_json(self) -> Any:
        return ['ManifestOnly', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class LockfileOnly:
    """Original type: dependency_source = [ ... | LockfileOnly of ... | ... ]"""

    value: Lockfile

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'LockfileOnly'

    def to_json(self) -> Any:
        return ['LockfileOnly', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ManifestLockfile:
    """Original type: dependency_source = [ ... | ManifestLockfile of ... | ... ]"""

    value: Tuple[Manifest, Lockfile]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ManifestLockfile'

    def to_json(self) -> Any:
        return ['ManifestLockfile', (lambda x: [(lambda x: x.to_json())(x[0]), (lambda x: x.to_json())(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class MultiLockfile:
    """Original type: dependency_source = [ ... | MultiLockfile of ... | ... ]"""

    value: List[DependencySource]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'MultiLockfile'

    def to_json(self) -> Any:
        return ['MultiLockfile', _atd_write_list((lambda x: x.to_json()))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class DependencySource:
    """Original type: dependency_source = [ ... ]"""

    value: Union[ManifestOnly, LockfileOnly, ManifestLockfile, MultiLockfile]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'DependencySource':
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'ManifestOnly':
                return cls(ManifestOnly(Manifest.from_json(x[1])))
            if cons == 'LockfileOnly':
                return cls(LockfileOnly(Lockfile.from_json(x[1])))
            if cons == 'ManifestLockfile':
                return cls(ManifestLockfile((lambda x: (Manifest.from_json(x[0]), Lockfile.from_json(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x))(x[1])))
            if cons == 'MultiLockfile':
                return cls(MultiLockfile(_atd_read_list(DependencySource.from_json)(x[1])))
            _atd_bad_json('DependencySource', x)
        _atd_bad_json('DependencySource', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'DependencySource':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class CliLoc:
    """Original type: match_call_trace = [ ... | CliLoc of ... | ... ]"""

    value: LocAndContent

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CliLoc'

    def to_json(self) -> Any:
        return ['CliLoc', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class CliCall:
    """Original type: match_call_trace = [ ... | CliCall of ... | ... ]"""

    value: Tuple[LocAndContent, List[MatchIntermediateVar], MatchCallTrace]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CliCall'

    def to_json(self) -> Any:
        return ['CliCall', (lambda x: [(lambda x: x.to_json())(x[0]), _atd_write_list((lambda x: x.to_json()))(x[1]), (lambda x: x.to_json())(x[2])] if isinstance(x, tuple) and len(x) == 3 else _atd_bad_python('tuple of length 3', x))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class MatchCallTrace:
    """Original type: match_call_trace = [ ... ]"""

    value: Union[CliLoc, CliCall]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'MatchCallTrace':
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'CliLoc':
                return cls(CliLoc(LocAndContent.from_json(x[1])))
            if cons == 'CliCall':
                return cls(CliCall((lambda x: (LocAndContent.from_json(x[0]), _atd_read_list(MatchIntermediateVar.from_json)(x[1]), MatchCallTrace.from_json(x[2])) if isinstance(x, list) and len(x) == 3 else _atd_bad_json('array of length 3', x))(x[1])))
            _atd_bad_json('MatchCallTrace', x)
        _atd_bad_json('MatchCallTrace', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'MatchCallTrace':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class MatchDataflowTrace:
    """Original type: match_dataflow_trace = { ... }"""

    taint_source: Optional[MatchCallTrace] = None
    intermediate_vars: Optional[List[MatchIntermediateVar]] = None
    taint_sink: Optional[MatchCallTrace] = None

    @classmethod
    def from_json(cls, x: Any) -> 'MatchDataflowTrace':
        if isinstance(x, dict):
            return cls(
                taint_source=MatchCallTrace.from_json(x['taint_source']) if 'taint_source' in x else None,
                intermediate_vars=_atd_read_list(MatchIntermediateVar.from_json)(x['intermediate_vars']) if 'intermediate_vars' in x else None,
                taint_sink=MatchCallTrace.from_json(x['taint_sink']) if 'taint_sink' in x else None,
            )
        else:
            _atd_bad_json('MatchDataflowTrace', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        if self.taint_source is not None:
            res['taint_source'] = (lambda x: x.to_json())(self.taint_source)
        if self.intermediate_vars is not None:
            res['intermediate_vars'] = _atd_write_list((lambda x: x.to_json()))(self.intermediate_vars)
        if self.taint_sink is not None:
            res['taint_sink'] = (lambda x: x.to_json())(self.taint_sink)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'MatchDataflowTrace':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CliMatch:
    """Original type: cli_match = { ... }"""

    check_id: RuleId
    path: Fpath
    start: Position
    end: Position
    extra: CliMatchExtra

    @classmethod
    def from_json(cls, x: Any) -> 'CliMatch':
        if isinstance(x, dict):
            return cls(
                check_id=RuleId.from_json(x['check_id']) if 'check_id' in x else _atd_missing_json_field('CliMatch', 'check_id'),
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('CliMatch', 'path'),
                start=Position.from_json(x['start']) if 'start' in x else _atd_missing_json_field('CliMatch', 'start'),
                end=Position.from_json(x['end']) if 'end' in x else _atd_missing_json_field('CliMatch', 'end'),
                extra=CliMatchExtra.from_json(x['extra']) if 'extra' in x else _atd_missing_json_field('CliMatch', 'extra'),
            )
        else:
            _atd_bad_json('CliMatch', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['check_id'] = (lambda x: x.to_json())(self.check_id)
        res['path'] = (lambda x: x.to_json())(self.path)
        res['start'] = (lambda x: x.to_json())(self.start)
        res['end'] = (lambda x: x.to_json())(self.end)
        res['extra'] = (lambda x: x.to_json())(self.extra)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CliMatch':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)

@dataclass
class CliMatchExtra:
    """Original type: cli_match_extra = { ... }"""

    message: str
    metadata: RawJson
    severity: MatchSeverity
    fingerprint: str
    lines: str
    metavars: Optional[Metavars] = None
    fix: Optional[str] = None
    fixed_lines: Optional[List[str]] = None
    is_ignored: Optional[bool] = None
    sca_info: Optional[ScaMatch] = None
    validation_state: Optional[ValidationState] = None
    historical_info: Optional[HistoricalInfo] = None
    dataflow_trace: Optional[MatchDataflowTrace] = None
    engine_kind: Optional[EngineOfFinding] = None
    extra_extra: Optional[RawJson] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CliMatchExtra':
        if isinstance(x, dict):
            return cls(
                message=_atd_read_string(x['message']) if 'message' in x else _atd_missing_json_field('CliMatchExtra', 'message'),
                metadata=RawJson.from_json(x['metadata']) if 'metadata' in x else _atd_missing_json_field('CliMatchExtra', 'metadata'),
                severity=MatchSeverity.from_json(x['severity']) if 'severity' in x else _atd_missing_json_field('CliMatchExtra', 'severity'),
                fingerprint=_atd_read_string(x['fingerprint']) if 'fingerprint' in x else _atd_missing_json_field('CliMatchExtra', 'fingerprint'),
                lines=_atd_read_string(x['lines']) if 'lines' in x else _atd_missing_json_field('CliMatchExtra', 'lines'),
                metavars=Metavars.from_json(x['metavars']) if 'metavars' in x else None,
                fix=_atd_read_string(x['fix']) if 'fix' in x else None,
                fixed_lines=_atd_read_list(_atd_read_string)(x['fixed_lines']) if 'fixed_lines' in x else None,
                is_ignored=_atd_read_bool(x['is_ignored']) if 'is_ignored' in x else None,
                sca_info=ScaMatch.from_json(x['sca_info']) if 'sca_info' in x else None,
                validation_state=ValidationState.from_json(x['validation_state']) if 'validation_state' in x else None,
                historical_info=HistoricalInfo.from_json(x['historical_info']) if 'historical_info' in x else None,
                dataflow_trace=MatchDataflowTrace.from_json(x['dataflow_trace']) if 'dataflow_trace' in x else None,
                engine_kind=EngineOfFinding.from_json(x['engine_kind']) if 'engine_kind' in x else None,
                extra_extra=RawJson.from_json(x['extra_extra']) if 'extra_extra' in x else None,
            )
        else:
            _atd_bad_json('CliMatchExtra', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['message'] = _atd_write_string(self.message)
        res['metadata'] = (lambda x: x.to_json())(self.metadata)
        res['severity'] = (lambda x: x.to_json())(self.severity)
        res['fingerprint'] = _atd_write_string(self.fingerprint)
        res['lines'] = _atd_write_string(self.lines)
        if self.metavars is not None:
            res['metavars'] = (lambda x: x.to_json())(self.metavars)
        if self.fix is not None:
            res['fix'] = _atd_write_string(self.fix)
        if self.fixed_lines is not None:
            res['fixed_lines'] = _atd_write_list(_atd_write_string)(self.fixed_lines)
        if self.is_ignored is not None:
            res['is_ignored'] = _atd_write_bool(self.is_ignored)
        if self.sca_info is not None:
            res['sca_info'] = (lambda x: x.to_json())(self.sca_info)
        if self.validation_state is not None:
            res['validation_state'] = (lambda x: x.to_json())(self.validation_state)
        if self.historical_info is not None:
            res['historical_info'] = (lambda x: x.to_json())(self.historical_info)
        if self.dataflow_trace is not None:
            res['dataflow_trace'] = (lambda x: x.to_json())(self.dataflow_trace)
        if self.engine_kind is not None:
            res['engine_kind'] = (lambda x: x.to_json())(self.engine_kind)
        if self.extra_extra is not None:
            res['extra_extra'] = (lambda x: x.to_json())(self.extra_extra)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CliMatchExtra':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)

@dataclass
class ScaMatch:
    """Original type: sca_match = { ... }"""

    reachability_rule: bool
    sca_finding_schema: int
    dependency_match: DependencyMatch
    reachable: bool
    kind: Optional[ScaMatchKind] = None

    @classmethod
    def from_json(cls, x: Any) -> 'ScaMatch':
        if isinstance(x, dict):
            return cls(
                reachability_rule=_atd_read_bool(x['reachability_rule']) if 'reachability_rule' in x else _atd_missing_json_field('ScaMatch', 'reachability_rule'),
                sca_finding_schema=_atd_read_int(x['sca_finding_schema']) if 'sca_finding_schema' in x else _atd_missing_json_field('ScaMatch', 'sca_finding_schema'),
                dependency_match=DependencyMatch.from_json(x['dependency_match']) if 'dependency_match' in x else _atd_missing_json_field('ScaMatch', 'dependency_match'),
                reachable=_atd_read_bool(x['reachable']) if 'reachable' in x else _atd_missing_json_field('ScaMatch', 'reachable'),
                kind=ScaMatchKind.from_json(x['kind']) if 'kind' in x else None,
            )
        else:
            _atd_bad_json('ScaMatch', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['reachability_rule'] = _atd_write_bool(self.reachability_rule)
        res['sca_finding_schema'] = _atd_write_int(self.sca_finding_schema)
        res['dependency_match'] = (lambda x: x.to_json())(self.dependency_match)
        res['reachable'] = _atd_write_bool(self.reachable)
        if self.kind is not None:
            res['kind'] = (lambda x: x.to_json())(self.kind)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ScaMatch':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)

@dataclass
class LockfileOnlyMatch:
    """Original type: sca_match_kind = [ ... | LockfileOnlyMatch of ... | ... ]"""

    value: DependencyKind

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'LockfileOnlyMatch'

    def to_json(self) -> Any:
        return ['LockfileOnlyMatch', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DirectReachable:
    """Original type: sca_match_kind = [ ... | DirectReachable | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'DirectReachable'

    @staticmethod
    def to_json() -> Any:
        return 'DirectReachable'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TransitiveReachable_:
    """Original type: sca_match_kind = [ ... | TransitiveReachable of ... | ... ]"""

    value: TransitiveReachable

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'TransitiveReachable_'

    def to_json(self) -> Any:
        return ['TransitiveReachable', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TransitiveUnreachable_:
    """Original type: sca_match_kind = [ ... | TransitiveUnreachable of ... | ... ]"""

    value: TransitiveUnreachable

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'TransitiveUnreachable_'

    def to_json(self) -> Any:
        return ['TransitiveUnreachable', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TransitiveUndetermined_:
    """Original type: sca_match_kind = [ ... | TransitiveUndetermined of ... | ... ]"""

    value: TransitiveUndetermined

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'TransitiveUndetermined_'

    def to_json(self) -> Any:
        return ['TransitiveUndetermined', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScaMatchKind:
    """Original type: sca_match_kind = [ ... ]"""

    value: Union[LockfileOnlyMatch, DirectReachable, TransitiveReachable_, TransitiveUnreachable_, TransitiveUndetermined_]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ScaMatchKind':
        if isinstance(x, str):
            if x == 'DirectReachable':
                return cls(DirectReachable())
            _atd_bad_json('ScaMatchKind', x)
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'LockfileOnlyMatch':
                return cls(LockfileOnlyMatch(DependencyKind.from_json(x[1])))
            if cons == 'TransitiveReachable':
                return cls(TransitiveReachable_(TransitiveReachable.from_json(x[1])))
            if cons == 'TransitiveUnreachable':
                return cls(TransitiveUnreachable_(TransitiveUnreachable.from_json(x[1])))
            if cons == 'TransitiveUndetermined':
                return cls(TransitiveUndetermined_(TransitiveUndetermined.from_json(x[1])))
            _atd_bad_json('ScaMatchKind', x)
        _atd_bad_json('ScaMatchKind', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ScaMatchKind':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)

@dataclass
class TransitiveReachable:
    """Original type: transitive_reachable = { ... }"""

    matches: List[Tuple[FoundDependency, List[CliMatch]]]
    callgraph_reachable: Optional[bool]
    explanation: Optional[str]

    @classmethod
    def from_json(cls, x: Any) -> 'TransitiveReachable':
        if isinstance(x, dict):
            return cls(
                matches=_atd_read_list((lambda x: (FoundDependency.from_json(x[0]), _atd_read_list(CliMatch.from_json)(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x)))(x['matches']) if 'matches' in x else _atd_missing_json_field('TransitiveReachable', 'matches'),
                callgraph_reachable=_atd_read_option(_atd_read_bool)(x['callgraph_reachable']) if 'callgraph_reachable' in x else _atd_missing_json_field('TransitiveReachable', 'callgraph_reachable'),
                explanation=_atd_read_option(_atd_read_string)(x['explanation']) if 'explanation' in x else _atd_missing_json_field('TransitiveReachable', 'explanation'),
            )
        else:
            _atd_bad_json('TransitiveReachable', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['matches'] = _atd_write_list((lambda x: [(lambda x: x.to_json())(x[0]), _atd_write_list((lambda x: x.to_json()))(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x)))(self.matches)
        res['callgraph_reachable'] = _atd_write_option(_atd_write_bool)(self.callgraph_reachable)
        res['explanation'] = _atd_write_option(_atd_write_string)(self.explanation)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TransitiveReachable':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CoreMatchExtra:
    """Original type: core_match_extra = { ... }"""

    metavars: Metavars
    engine_kind: EngineOfFinding
    is_ignored: bool
    message: Optional[str] = None
    metadata: Optional[RawJson] = None
    severity: Optional[MatchSeverity] = None
    fix: Optional[str] = None
    dataflow_trace: Optional[MatchDataflowTrace] = None
    sca_match: Optional[ScaMatch] = None
    validation_state: Optional[ValidationState] = None
    historical_info: Optional[HistoricalInfo] = None
    extra_extra: Optional[RawJson] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CoreMatchExtra':
        if isinstance(x, dict):
            return cls(
                metavars=Metavars.from_json(x['metavars']) if 'metavars' in x else _atd_missing_json_field('CoreMatchExtra', 'metavars'),
                engine_kind=EngineOfFinding.from_json(x['engine_kind']) if 'engine_kind' in x else _atd_missing_json_field('CoreMatchExtra', 'engine_kind'),
                is_ignored=_atd_read_bool(x['is_ignored']) if 'is_ignored' in x else _atd_missing_json_field('CoreMatchExtra', 'is_ignored'),
                message=_atd_read_string(x['message']) if 'message' in x else None,
                metadata=RawJson.from_json(x['metadata']) if 'metadata' in x else None,
                severity=MatchSeverity.from_json(x['severity']) if 'severity' in x else None,
                fix=_atd_read_string(x['fix']) if 'fix' in x else None,
                dataflow_trace=MatchDataflowTrace.from_json(x['dataflow_trace']) if 'dataflow_trace' in x else None,
                sca_match=ScaMatch.from_json(x['sca_match']) if 'sca_match' in x else None,
                validation_state=ValidationState.from_json(x['validation_state']) if 'validation_state' in x else None,
                historical_info=HistoricalInfo.from_json(x['historical_info']) if 'historical_info' in x else None,
                extra_extra=RawJson.from_json(x['extra_extra']) if 'extra_extra' in x else None,
            )
        else:
            _atd_bad_json('CoreMatchExtra', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['metavars'] = (lambda x: x.to_json())(self.metavars)
        res['engine_kind'] = (lambda x: x.to_json())(self.engine_kind)
        res['is_ignored'] = _atd_write_bool(self.is_ignored)
        if self.message is not None:
            res['message'] = _atd_write_string(self.message)
        if self.metadata is not None:
            res['metadata'] = (lambda x: x.to_json())(self.metadata)
        if self.severity is not None:
            res['severity'] = (lambda x: x.to_json())(self.severity)
        if self.fix is not None:
            res['fix'] = _atd_write_string(self.fix)
        if self.dataflow_trace is not None:
            res['dataflow_trace'] = (lambda x: x.to_json())(self.dataflow_trace)
        if self.sca_match is not None:
            res['sca_match'] = (lambda x: x.to_json())(self.sca_match)
        if self.validation_state is not None:
            res['validation_state'] = (lambda x: x.to_json())(self.validation_state)
        if self.historical_info is not None:
            res['historical_info'] = (lambda x: x.to_json())(self.historical_info)
        if self.extra_extra is not None:
            res['extra_extra'] = (lambda x: x.to_json())(self.extra_extra)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CoreMatchExtra':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CoreMatch:
    """Original type: core_match = { ... }"""

    check_id: RuleId
    path: Fpath
    start: Position
    end: Position
    extra: CoreMatchExtra

    @classmethod
    def from_json(cls, x: Any) -> 'CoreMatch':
        if isinstance(x, dict):
            return cls(
                check_id=RuleId.from_json(x['check_id']) if 'check_id' in x else _atd_missing_json_field('CoreMatch', 'check_id'),
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('CoreMatch', 'path'),
                start=Position.from_json(x['start']) if 'start' in x else _atd_missing_json_field('CoreMatch', 'start'),
                end=Position.from_json(x['end']) if 'end' in x else _atd_missing_json_field('CoreMatch', 'end'),
                extra=CoreMatchExtra.from_json(x['extra']) if 'extra' in x else _atd_missing_json_field('CoreMatch', 'extra'),
            )
        else:
            _atd_bad_json('CoreMatch', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['check_id'] = (lambda x: x.to_json())(self.check_id)
        res['path'] = (lambda x: x.to_json())(self.path)
        res['start'] = (lambda x: x.to_json())(self.start)
        res['end'] = (lambda x: x.to_json())(self.end)
        res['extra'] = (lambda x: x.to_json())(self.extra)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CoreMatch':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class MatchingExplanationExtra:
    """Original type: matching_explanation_extra = { ... }"""

    before_negation_matches: Optional[List[CoreMatch]]
    before_filter_matches: Optional[List[CoreMatch]]

    @classmethod
    def from_json(cls, x: Any) -> 'MatchingExplanationExtra':
        if isinstance(x, dict):
            return cls(
                before_negation_matches=_atd_read_option(_atd_read_list(CoreMatch.from_json))(x['before_negation_matches']) if 'before_negation_matches' in x else _atd_missing_json_field('MatchingExplanationExtra', 'before_negation_matches'),
                before_filter_matches=_atd_read_option(_atd_read_list(CoreMatch.from_json))(x['before_filter_matches']) if 'before_filter_matches' in x else _atd_missing_json_field('MatchingExplanationExtra', 'before_filter_matches'),
            )
        else:
            _atd_bad_json('MatchingExplanationExtra', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['before_negation_matches'] = _atd_write_option(_atd_write_list((lambda x: x.to_json())))(self.before_negation_matches)
        res['before_filter_matches'] = _atd_write_option(_atd_write_list((lambda x: x.to_json())))(self.before_filter_matches)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'MatchingExplanationExtra':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class MatchingExplanation:
    """Original type: matching_explanation = { ... }"""

    op: MatchingOperation
    children: List[MatchingExplanation]
    matches: List[CoreMatch]
    loc: Location
    extra: Optional[MatchingExplanationExtra] = None

    @classmethod
    def from_json(cls, x: Any) -> 'MatchingExplanation':
        if isinstance(x, dict):
            return cls(
                op=MatchingOperation.from_json(x['op']) if 'op' in x else _atd_missing_json_field('MatchingExplanation', 'op'),
                children=_atd_read_list(MatchingExplanation.from_json)(x['children']) if 'children' in x else _atd_missing_json_field('MatchingExplanation', 'children'),
                matches=_atd_read_list(CoreMatch.from_json)(x['matches']) if 'matches' in x else _atd_missing_json_field('MatchingExplanation', 'matches'),
                loc=Location.from_json(x['loc']) if 'loc' in x else _atd_missing_json_field('MatchingExplanation', 'loc'),
                extra=MatchingExplanationExtra.from_json(x['extra']) if 'extra' in x else None,
            )
        else:
            _atd_bad_json('MatchingExplanation', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['op'] = (lambda x: x.to_json())(self.op)
        res['children'] = _atd_write_list((lambda x: x.to_json()))(self.children)
        res['matches'] = _atd_write_list((lambda x: x.to_json()))(self.matches)
        res['loc'] = (lambda x: x.to_json())(self.loc)
        if self.extra is not None:
            res['extra'] = (lambda x: x.to_json())(self.extra)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'MatchingExplanation':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Version:
    """Original type: version"""

    value: str

    @classmethod
    def from_json(cls, x: Any) -> 'Version':
        return cls(_atd_read_string(x))

    def to_json(self) -> Any:
        return _atd_write_string(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Version':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Uuid:
    """Original type: uuid"""

    value: str

    @classmethod
    def from_json(cls, x: Any) -> 'Uuid':
        return cls(_atd_read_string(x))

    def to_json(self) -> Any:
        return _atd_write_string(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Uuid':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Uri:
    """Original type: uri"""

    value: str

    @classmethod
    def from_json(cls, x: Any) -> 'Uri':
        return cls(_atd_read_string(x))

    def to_json(self) -> Any:
        return _atd_write_string(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Uri':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class UnresolvedFailed:
    """Original type: unresolved_reason = [ ... | UnresolvedFailed | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'UnresolvedFailed'

    @staticmethod
    def to_json() -> Any:
        return 'failed'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class UnresolvedSkipped:
    """Original type: unresolved_reason = [ ... | UnresolvedSkipped | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'UnresolvedSkipped'

    @staticmethod
    def to_json() -> Any:
        return 'skipped'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class UnresolvedUnsupported:
    """Original type: unresolved_reason = [ ... | UnresolvedUnsupported | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'UnresolvedUnsupported'

    @staticmethod
    def to_json() -> Any:
        return 'unsupported'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class UnresolvedDisabled:
    """Original type: unresolved_reason = [ ... | UnresolvedDisabled | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'UnresolvedDisabled'

    @staticmethod
    def to_json() -> Any:
        return 'disabled'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class UnresolvedReason:
    """Original type: unresolved_reason = [ ... ]"""

    value: Union[UnresolvedFailed, UnresolvedSkipped, UnresolvedUnsupported, UnresolvedDisabled]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'UnresolvedReason':
        if isinstance(x, str):
            if x == 'failed':
                return cls(UnresolvedFailed())
            if x == 'skipped':
                return cls(UnresolvedSkipped())
            if x == 'unsupported':
                return cls(UnresolvedUnsupported())
            if x == 'disabled':
                return cls(UnresolvedDisabled())
            _atd_bad_json('UnresolvedReason', x)
        _atd_bad_json('UnresolvedReason', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'UnresolvedReason':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class Subproject:
    """Original type: subproject = { ... }"""

    root_dir: Fpath
    ecosystem: Optional[Ecosystem]
    dependency_source: DependencySource

    @classmethod
    def from_json(cls, x: Any) -> 'Subproject':
        if isinstance(x, dict):
            return cls(
                root_dir=Fpath.from_json(x['root_dir']) if 'root_dir' in x else _atd_missing_json_field('Subproject', 'root_dir'),
                ecosystem=_atd_read_option(Ecosystem.from_json)(x['ecosystem']) if 'ecosystem' in x else _atd_missing_json_field('Subproject', 'ecosystem'),
                dependency_source=DependencySource.from_json(x['dependency_source']) if 'dependency_source' in x else _atd_missing_json_field('Subproject', 'dependency_source'),
            )
        else:
            _atd_bad_json('Subproject', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['root_dir'] = (lambda x: x.to_json())(self.root_dir)
        res['ecosystem'] = _atd_write_option((lambda x: x.to_json()))(self.ecosystem)
        res['dependency_source'] = (lambda x: x.to_json())(self.dependency_source)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Subproject':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PGemfileLock:
    """Original type: sca_parser_name = [ ... | PGemfile_lock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PGemfileLock'

    @staticmethod
    def to_json() -> Any:
        return 'gemfile_lock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PGoMod:
    """Original type: sca_parser_name = [ ... | PGo_mod | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PGoMod'

    @staticmethod
    def to_json() -> Any:
        return 'go_mod'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PGoSum:
    """Original type: sca_parser_name = [ ... | PGo_sum | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PGoSum'

    @staticmethod
    def to_json() -> Any:
        return 'go_sum'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PGradleLockfile:
    """Original type: sca_parser_name = [ ... | PGradle_lockfile | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PGradleLockfile'

    @staticmethod
    def to_json() -> Any:
        return 'gradle_lockfile'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PGradleBuild:
    """Original type: sca_parser_name = [ ... | PGradle_build | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PGradleBuild'

    @staticmethod
    def to_json() -> Any:
        return 'gradle_build'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PJsondoc:
    """Original type: sca_parser_name = [ ... | PJsondoc | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PJsondoc'

    @staticmethod
    def to_json() -> Any:
        return 'jsondoc'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PPipfile:
    """Original type: sca_parser_name = [ ... | PPipfile | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PPipfile'

    @staticmethod
    def to_json() -> Any:
        return 'pipfile'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PPnpmLock:
    """Original type: sca_parser_name = [ ... | PPnpm_lock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PPnpmLock'

    @staticmethod
    def to_json() -> Any:
        return 'pnpm_lock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PPoetryLock:
    """Original type: sca_parser_name = [ ... | PPoetry_lock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PPoetryLock'

    @staticmethod
    def to_json() -> Any:
        return 'poetry_lock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PPyprojectToml:
    """Original type: sca_parser_name = [ ... | PPyproject_toml | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PPyprojectToml'

    @staticmethod
    def to_json() -> Any:
        return 'pyproject_toml'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PRequirements:
    """Original type: sca_parser_name = [ ... | PRequirements | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PRequirements'

    @staticmethod
    def to_json() -> Any:
        return 'requirements'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PYarn1:
    """Original type: sca_parser_name = [ ... | PYarn_1 | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PYarn1'

    @staticmethod
    def to_json() -> Any:
        return 'yarn_1'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PYarn2:
    """Original type: sca_parser_name = [ ... | PYarn_2 | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PYarn2'

    @staticmethod
    def to_json() -> Any:
        return 'yarn_2'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PPomtree:
    """Original type: sca_parser_name = [ ... | PPomtree | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PPomtree'

    @staticmethod
    def to_json() -> Any:
        return 'pomtree'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PCargoParser:
    """Original type: sca_parser_name = [ ... | PCargo_parser | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PCargoParser'

    @staticmethod
    def to_json() -> Any:
        return 'cargo'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PComposerLock:
    """Original type: sca_parser_name = [ ... | PComposer_lock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PComposerLock'

    @staticmethod
    def to_json() -> Any:
        return 'composer_lock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PPubspecLock:
    """Original type: sca_parser_name = [ ... | PPubspec_lock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PPubspecLock'

    @staticmethod
    def to_json() -> Any:
        return 'pubspec_lock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PPackageSwift:
    """Original type: sca_parser_name = [ ... | PPackage_swift | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PPackageSwift'

    @staticmethod
    def to_json() -> Any:
        return 'package_swift'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PPodfileLock:
    """Original type: sca_parser_name = [ ... | PPodfile_lock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PPodfileLock'

    @staticmethod
    def to_json() -> Any:
        return 'podfile_lock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PPackageResolved:
    """Original type: sca_parser_name = [ ... | PPackage_resolved | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PPackageResolved'

    @staticmethod
    def to_json() -> Any:
        return 'package_resolved'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class PMixLock:
    """Original type: sca_parser_name = [ ... | PMix_lock | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PMixLock'

    @staticmethod
    def to_json() -> Any:
        return 'mix_lock'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScaParserName:
    """Original type: sca_parser_name = [ ... ]"""

    value: Union[PGemfileLock, PGoMod, PGoSum, PGradleLockfile, PGradleBuild, PJsondoc, PPipfile, PPnpmLock, PPoetryLock, PPyprojectToml, PRequirements, PYarn1, PYarn2, PPomtree, PCargoParser, PComposerLock, PPubspecLock, PPackageSwift, PPodfileLock, PPackageResolved, PMixLock]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ScaParserName':
        if isinstance(x, str):
            if x == 'gemfile_lock':
                return cls(PGemfileLock())
            if x == 'go_mod':
                return cls(PGoMod())
            if x == 'go_sum':
                return cls(PGoSum())
            if x == 'gradle_lockfile':
                return cls(PGradleLockfile())
            if x == 'gradle_build':
                return cls(PGradleBuild())
            if x == 'jsondoc':
                return cls(PJsondoc())
            if x == 'pipfile':
                return cls(PPipfile())
            if x == 'pnpm_lock':
                return cls(PPnpmLock())
            if x == 'poetry_lock':
                return cls(PPoetryLock())
            if x == 'pyproject_toml':
                return cls(PPyprojectToml())
            if x == 'requirements':
                return cls(PRequirements())
            if x == 'yarn_1':
                return cls(PYarn1())
            if x == 'yarn_2':
                return cls(PYarn2())
            if x == 'pomtree':
                return cls(PPomtree())
            if x == 'cargo':
                return cls(PCargoParser())
            if x == 'composer_lock':
                return cls(PComposerLock())
            if x == 'pubspec_lock':
                return cls(PPubspecLock())
            if x == 'package_swift':
                return cls(PPackageSwift())
            if x == 'podfile_lock':
                return cls(PPodfileLock())
            if x == 'package_resolved':
                return cls(PPackageResolved())
            if x == 'mix_lock':
                return cls(PMixLock())
            _atd_bad_json('ScaParserName', x)
        _atd_bad_json('ScaParserName', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ScaParserName':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ResolutionCmdFailed:
    """Original type: resolution_cmd_failed = { ... }"""

    command: str
    message: str

    @classmethod
    def from_json(cls, x: Any) -> 'ResolutionCmdFailed':
        if isinstance(x, dict):
            return cls(
                command=_atd_read_string(x['command']) if 'command' in x else _atd_missing_json_field('ResolutionCmdFailed', 'command'),
                message=_atd_read_string(x['message']) if 'message' in x else _atd_missing_json_field('ResolutionCmdFailed', 'message'),
            )
        else:
            _atd_bad_json('ResolutionCmdFailed', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['command'] = _atd_write_string(self.command)
        res['message'] = _atd_write_string(self.message)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ResolutionCmdFailed':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class UnsupportedManifest:
    """Original type: resolution_error_kind = [ ... | UnsupportedManifest | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'UnsupportedManifest'

    @staticmethod
    def to_json() -> Any:
        return 'UnsupportedManifest'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class MissingRequirement:
    """Original type: resolution_error_kind = [ ... | MissingRequirement of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'MissingRequirement'

    def to_json(self) -> Any:
        return ['MissingRequirement', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ResolutionCmdFailed_:
    """Original type: resolution_error_kind = [ ... | ResolutionCmdFailed of ... | ... ]"""

    value: ResolutionCmdFailed

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ResolutionCmdFailed_'

    def to_json(self) -> Any:
        return ['ResolutionCmdFailed', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ParseDependenciesFailed:
    """Original type: resolution_error_kind = [ ... | ParseDependenciesFailed of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ParseDependenciesFailed'

    def to_json(self) -> Any:
        return ['ParseDependenciesFailed', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ScaParseError:
    """Original type: resolution_error_kind = [ ... | ScaParseError of ... | ... ]"""

    value: ScaParserName

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ScaParseError'

    def to_json(self) -> Any:
        return ['ScaParseError', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ResolutionErrorKind:
    """Original type: resolution_error_kind = [ ... ]"""

    value: Union[UnsupportedManifest, MissingRequirement, ResolutionCmdFailed_, ParseDependenciesFailed, ScaParseError]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ResolutionErrorKind':
        if isinstance(x, str):
            if x == 'UnsupportedManifest':
                return cls(UnsupportedManifest())
            _atd_bad_json('ResolutionErrorKind', x)
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'MissingRequirement':
                return cls(MissingRequirement(_atd_read_string(x[1])))
            if cons == 'ResolutionCmdFailed':
                return cls(ResolutionCmdFailed_(ResolutionCmdFailed.from_json(x[1])))
            if cons == 'ParseDependenciesFailed':
                return cls(ParseDependenciesFailed(_atd_read_string(x[1])))
            if cons == 'ScaParseError':
                return cls(ScaParseError(ScaParserName.from_json(x[1])))
            _atd_bad_json('ResolutionErrorKind', x)
        _atd_bad_json('ResolutionErrorKind', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ResolutionErrorKind':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScaResolutionError:
    """Original type: sca_resolution_error = { ... }"""

    type_: ResolutionErrorKind
    dependency_source_file: Fpath

    @classmethod
    def from_json(cls, x: Any) -> 'ScaResolutionError':
        if isinstance(x, dict):
            return cls(
                type_=ResolutionErrorKind.from_json(x['type_']) if 'type_' in x else _atd_missing_json_field('ScaResolutionError', 'type_'),
                dependency_source_file=Fpath.from_json(x['dependency_source_file']) if 'dependency_source_file' in x else _atd_missing_json_field('ScaResolutionError', 'dependency_source_file'),
            )
        else:
            _atd_bad_json('ScaResolutionError', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['type_'] = (lambda x: x.to_json())(self.type_)
        res['dependency_source_file'] = (lambda x: x.to_json())(self.dependency_source_file)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ScaResolutionError':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DependencyParserError:
    """Original type: dependency_parser_error = { ... }"""

    path: Fpath
    parser: ScaParserName
    reason: str
    line: Optional[int] = None
    col: Optional[int] = None
    text: Optional[str] = None

    @classmethod
    def from_json(cls, x: Any) -> 'DependencyParserError':
        if isinstance(x, dict):
            return cls(
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('DependencyParserError', 'path'),
                parser=ScaParserName.from_json(x['parser']) if 'parser' in x else _atd_missing_json_field('DependencyParserError', 'parser'),
                reason=_atd_read_string(x['reason']) if 'reason' in x else _atd_missing_json_field('DependencyParserError', 'reason'),
                line=_atd_read_int(x['line']) if 'line' in x else None,
                col=_atd_read_int(x['col']) if 'col' in x else None,
                text=_atd_read_string(x['text']) if 'text' in x else None,
            )
        else:
            _atd_bad_json('DependencyParserError', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['path'] = (lambda x: x.to_json())(self.path)
        res['parser'] = (lambda x: x.to_json())(self.parser)
        res['reason'] = _atd_write_string(self.reason)
        if self.line is not None:
            res['line'] = _atd_write_int(self.line)
        if self.col is not None:
            res['col'] = _atd_write_int(self.col)
        if self.text is not None:
            res['text'] = _atd_write_string(self.text)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'DependencyParserError':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SCAParse:
    """Original type: sca_error = [ ... | SCAParse of ... | ... ]"""

    value: DependencyParserError

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SCAParse'

    def to_json(self) -> Any:
        return ['SCAParse', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SCAResol:
    """Original type: sca_error = [ ... | SCAResol of ... | ... ]"""

    value: ScaResolutionError

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SCAResol'

    def to_json(self) -> Any:
        return ['SCAResol', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScaError:
    """Original type: sca_error = [ ... ]"""

    value: Union[SCAParse, SCAResol]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ScaError':
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'SCAParse':
                return cls(SCAParse(DependencyParserError.from_json(x[1])))
            if cons == 'SCAResol':
                return cls(SCAResol(ScaResolutionError.from_json(x[1])))
            _atd_bad_json('ScaError', x)
        _atd_bad_json('ScaError', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ScaError':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class UnresolvedSubproject:
    """Original type: unresolved_subproject = { ... }"""

    info: Subproject
    reason: UnresolvedReason
    errors: List[ScaError]

    @classmethod
    def from_json(cls, x: Any) -> 'UnresolvedSubproject':
        if isinstance(x, dict):
            return cls(
                info=Subproject.from_json(x['info']) if 'info' in x else _atd_missing_json_field('UnresolvedSubproject', 'info'),
                reason=UnresolvedReason.from_json(x['reason']) if 'reason' in x else _atd_missing_json_field('UnresolvedSubproject', 'reason'),
                errors=_atd_read_list(ScaError.from_json)(x['errors']) if 'errors' in x else _atd_missing_json_field('UnresolvedSubproject', 'errors'),
            )
        else:
            _atd_bad_json('UnresolvedSubproject', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['info'] = (lambda x: x.to_json())(self.info)
        res['reason'] = (lambda x: x.to_json())(self.reason)
        res['errors'] = _atd_write_list((lambda x: x.to_json()))(self.errors)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'UnresolvedSubproject':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Snippet:
    """Original type: snippet = { ... }"""

    line: int
    text: str

    @classmethod
    def from_json(cls, x: Any) -> 'Snippet':
        if isinstance(x, dict):
            return cls(
                line=_atd_read_int(x['line']) if 'line' in x else _atd_missing_json_field('Snippet', 'line'),
                text=_atd_read_string(x['text']) if 'text' in x else _atd_missing_json_field('Snippet', 'text'),
            )
        else:
            _atd_bad_json('Snippet', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['line'] = _atd_write_int(self.line)
        res['text'] = _atd_write_string(self.text)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Snippet':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class And_:
    """Original type: killing_parent_kind = [ ... | And | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'And_'

    @staticmethod
    def to_json() -> Any:
        return 'And'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Inside_:
    """Original type: killing_parent_kind = [ ... | Inside | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Inside_'

    @staticmethod
    def to_json() -> Any:
        return 'Inside'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Negation_:
    """Original type: killing_parent_kind = [ ... | Negation | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Negation_'

    @staticmethod
    def to_json() -> Any:
        return 'Negation'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Filter_:
    """Original type: killing_parent_kind = [ ... | Filter of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Filter_'

    def to_json(self) -> Any:
        return ['Filter', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class KillingParentKind:
    """Original type: killing_parent_kind = [ ... ]"""

    value: Union[And_, Inside_, Negation_, Filter_]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'KillingParentKind':
        if isinstance(x, str):
            if x == 'And':
                return cls(And_())
            if x == 'Inside':
                return cls(Inside_())
            if x == 'Negation':
                return cls(Negation_())
            _atd_bad_json('KillingParentKind', x)
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'Filter':
                return cls(Filter_(_atd_read_string(x[1])))
            _atd_bad_json('KillingParentKind', x)
        _atd_bad_json('KillingParentKind', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'KillingParentKind':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class KillingParent:
    """Original type: killing_parent = { ... }"""

    killing_parent_kind: KillingParentKind
    snippet: Snippet

    @classmethod
    def from_json(cls, x: Any) -> 'KillingParent':
        if isinstance(x, dict):
            return cls(
                killing_parent_kind=KillingParentKind.from_json(x['killing_parent_kind']) if 'killing_parent_kind' in x else _atd_missing_json_field('KillingParent', 'killing_parent_kind'),
                snippet=Snippet.from_json(x['snippet']) if 'snippet' in x else _atd_missing_json_field('KillingParent', 'snippet'),
            )
        else:
            _atd_bad_json('KillingParent', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['killing_parent_kind'] = (lambda x: x.to_json())(self.killing_parent_kind)
        res['snippet'] = (lambda x: x.to_json())(self.snippet)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'KillingParent':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class NeverMatched:
    """Original type: unexpected_no_match_diagnosis_kind = [ ... | Never_matched | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'NeverMatched'

    @staticmethod
    def to_json() -> Any:
        return 'Never_matched'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class KilledByNodes:
    """Original type: unexpected_no_match_diagnosis_kind = [ ... | Killed_by_nodes of ... | ... ]"""

    value: List[KillingParent]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'KilledByNodes'

    def to_json(self) -> Any:
        return ['Killed_by_nodes', _atd_write_list((lambda x: x.to_json()))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class UnexpectedNoMatchDiagnosisKind:
    """Original type: unexpected_no_match_diagnosis_kind = [ ... ]"""

    value: Union[NeverMatched, KilledByNodes]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'UnexpectedNoMatchDiagnosisKind':
        if isinstance(x, str):
            if x == 'Never_matched':
                return cls(NeverMatched())
            _atd_bad_json('UnexpectedNoMatchDiagnosisKind', x)
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'Killed_by_nodes':
                return cls(KilledByNodes(_atd_read_list(KillingParent.from_json)(x[1])))
            _atd_bad_json('UnexpectedNoMatchDiagnosisKind', x)
        _atd_bad_json('UnexpectedNoMatchDiagnosisKind', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'UnexpectedNoMatchDiagnosisKind':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class UnexpectedNoMatchDiagnosis:
    """Original type: unexpected_no_match_diagnosis = { ... }"""

    line: int
    kind: UnexpectedNoMatchDiagnosisKind

    @classmethod
    def from_json(cls, x: Any) -> 'UnexpectedNoMatchDiagnosis':
        if isinstance(x, dict):
            return cls(
                line=_atd_read_int(x['line']) if 'line' in x else _atd_missing_json_field('UnexpectedNoMatchDiagnosis', 'line'),
                kind=UnexpectedNoMatchDiagnosisKind.from_json(x['kind']) if 'kind' in x else _atd_missing_json_field('UnexpectedNoMatchDiagnosis', 'kind'),
            )
        else:
            _atd_bad_json('UnexpectedNoMatchDiagnosis', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['line'] = _atd_write_int(self.line)
        res['kind'] = (lambda x: x.to_json())(self.kind)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'UnexpectedNoMatchDiagnosis':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Focus:
    """Original type: originating_node_kind = [ ... | Focus | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Focus'

    @staticmethod
    def to_json() -> Any:
        return 'Focus'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Xpattern:
    """Original type: originating_node_kind = [ ... | Xpattern | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Xpattern'

    @staticmethod
    def to_json() -> Any:
        return 'Xpattern'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class OriginatingNodeKind:
    """Original type: originating_node_kind = [ ... ]"""

    value: Union[Focus, Xpattern]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'OriginatingNodeKind':
        if isinstance(x, str):
            if x == 'Focus':
                return cls(Focus())
            if x == 'Xpattern':
                return cls(Xpattern())
            _atd_bad_json('OriginatingNodeKind', x)
        _atd_bad_json('OriginatingNodeKind', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'OriginatingNodeKind':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class UnexpectedMatchDiagnosis:
    """Original type: unexpected_match_diagnosis = { ... }"""

    matched_text: Snippet
    originating_kind: OriginatingNodeKind
    originating_text: Snippet
    killing_parents: List[KillingParent]

    @classmethod
    def from_json(cls, x: Any) -> 'UnexpectedMatchDiagnosis':
        if isinstance(x, dict):
            return cls(
                matched_text=Snippet.from_json(x['matched_text']) if 'matched_text' in x else _atd_missing_json_field('UnexpectedMatchDiagnosis', 'matched_text'),
                originating_kind=OriginatingNodeKind.from_json(x['originating_kind']) if 'originating_kind' in x else _atd_missing_json_field('UnexpectedMatchDiagnosis', 'originating_kind'),
                originating_text=Snippet.from_json(x['originating_text']) if 'originating_text' in x else _atd_missing_json_field('UnexpectedMatchDiagnosis', 'originating_text'),
                killing_parents=_atd_read_list(KillingParent.from_json)(x['killing_parents']) if 'killing_parents' in x else _atd_missing_json_field('UnexpectedMatchDiagnosis', 'killing_parents'),
            )
        else:
            _atd_bad_json('UnexpectedMatchDiagnosis', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['matched_text'] = (lambda x: x.to_json())(self.matched_text)
        res['originating_kind'] = (lambda x: x.to_json())(self.originating_kind)
        res['originating_text'] = (lambda x: x.to_json())(self.originating_text)
        res['killing_parents'] = _atd_write_list((lambda x: x.to_json()))(self.killing_parents)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'UnexpectedMatchDiagnosis':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TriageIgnored:
    """Original type: triage_ignored = { ... }"""

    triage_ignored_syntactic_ids: List[str] = field(default_factory=lambda: [])
    triage_ignored_match_based_ids: List[str] = field(default_factory=lambda: [])

    @classmethod
    def from_json(cls, x: Any) -> 'TriageIgnored':
        if isinstance(x, dict):
            return cls(
                triage_ignored_syntactic_ids=_atd_read_list(_atd_read_string)(x['triage_ignored_syntactic_ids']) if 'triage_ignored_syntactic_ids' in x else [],
                triage_ignored_match_based_ids=_atd_read_list(_atd_read_string)(x['triage_ignored_match_based_ids']) if 'triage_ignored_match_based_ids' in x else [],
            )
        else:
            _atd_bad_json('TriageIgnored', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['triage_ignored_syntactic_ids'] = _atd_write_list(_atd_write_string)(self.triage_ignored_syntactic_ids)
        res['triage_ignored_match_based_ids'] = _atd_write_list(_atd_write_string)(self.triage_ignored_match_based_ids)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TriageIgnored':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TransitiveFinding:
    """Original type: transitive_finding = { ... }"""

    m: CoreMatch

    @classmethod
    def from_json(cls, x: Any) -> 'TransitiveFinding':
        if isinstance(x, dict):
            return cls(
                m=CoreMatch.from_json(x['m']) if 'm' in x else _atd_missing_json_field('TransitiveFinding', 'm'),
            )
        else:
            _atd_bad_json('TransitiveFinding', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['m'] = (lambda x: x.to_json())(self.m)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TransitiveFinding':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DownloadedDependency:
    """Original type: downloaded_dependency = { ... }"""

    source_paths: List[Fpath]

    @classmethod
    def from_json(cls, x: Any) -> 'DownloadedDependency':
        if isinstance(x, dict):
            return cls(
                source_paths=_atd_read_list(Fpath.from_json)(x['source_paths']) if 'source_paths' in x else _atd_missing_json_field('DownloadedDependency', 'source_paths'),
            )
        else:
            _atd_bad_json('DownloadedDependency', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['source_paths'] = _atd_write_list((lambda x: x.to_json()))(self.source_paths)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'DownloadedDependency':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ResolvedDependency:
    """Original type: resolved_dependency"""

    value: Tuple[FoundDependency, Optional[DownloadedDependency]]

    @classmethod
    def from_json(cls, x: Any) -> 'ResolvedDependency':
        return cls((lambda x: (FoundDependency.from_json(x[0]), _atd_read_option(DownloadedDependency.from_json)(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x))(x))

    def to_json(self) -> Any:
        return (lambda x: [(lambda x: x.to_json())(x[0]), _atd_write_option((lambda x: x.to_json()))(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x))(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'ResolvedDependency':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TransitiveReachabilityFilterParams:
    """Original type: transitive_reachability_filter_params = { ... }"""

    rules_path: Fpath
    findings: List[TransitiveFinding]
    dependencies: List[ResolvedDependency]
    write_to_cache: bool

    @classmethod
    def from_json(cls, x: Any) -> 'TransitiveReachabilityFilterParams':
        if isinstance(x, dict):
            return cls(
                rules_path=Fpath.from_json(x['rules_path']) if 'rules_path' in x else _atd_missing_json_field('TransitiveReachabilityFilterParams', 'rules_path'),
                findings=_atd_read_list(TransitiveFinding.from_json)(x['findings']) if 'findings' in x else _atd_missing_json_field('TransitiveReachabilityFilterParams', 'findings'),
                dependencies=_atd_read_list(ResolvedDependency.from_json)(x['dependencies']) if 'dependencies' in x else _atd_missing_json_field('TransitiveReachabilityFilterParams', 'dependencies'),
                write_to_cache=_atd_read_bool(x['write_to_cache']) if 'write_to_cache' in x else _atd_missing_json_field('TransitiveReachabilityFilterParams', 'write_to_cache'),
            )
        else:
            _atd_bad_json('TransitiveReachabilityFilterParams', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['rules_path'] = (lambda x: x.to_json())(self.rules_path)
        res['findings'] = _atd_write_list((lambda x: x.to_json()))(self.findings)
        res['dependencies'] = _atd_write_list((lambda x: x.to_json()))(self.dependencies)
        res['write_to_cache'] = _atd_write_bool(self.write_to_cache)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TransitiveReachabilityFilterParams':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TrCacheMatchResult:
    """Original type: tr_cache_match_result = { ... }"""

    matches: List[CliMatch]

    @classmethod
    def from_json(cls, x: Any) -> 'TrCacheMatchResult':
        if isinstance(x, dict):
            return cls(
                matches=_atd_read_list(CliMatch.from_json)(x['matches']) if 'matches' in x else _atd_missing_json_field('TrCacheMatchResult', 'matches'),
            )
        else:
            _atd_bad_json('TrCacheMatchResult', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['matches'] = _atd_write_list((lambda x: x.to_json()))(self.matches)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TrCacheMatchResult':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TrCacheKey:
    """Original type: tr_cache_key = { ... }"""

    rule_id: RuleId
    rule_version: str
    engine_version: int
    package_url: str
    extra: str

    @classmethod
    def from_json(cls, x: Any) -> 'TrCacheKey':
        if isinstance(x, dict):
            return cls(
                rule_id=RuleId.from_json(x['rule_id']) if 'rule_id' in x else _atd_missing_json_field('TrCacheKey', 'rule_id'),
                rule_version=_atd_read_string(x['rule_version']) if 'rule_version' in x else _atd_missing_json_field('TrCacheKey', 'rule_version'),
                engine_version=_atd_read_int(x['engine_version']) if 'engine_version' in x else _atd_missing_json_field('TrCacheKey', 'engine_version'),
                package_url=_atd_read_string(x['package_url']) if 'package_url' in x else _atd_missing_json_field('TrCacheKey', 'package_url'),
                extra=_atd_read_string(x['extra']) if 'extra' in x else _atd_missing_json_field('TrCacheKey', 'extra'),
            )
        else:
            _atd_bad_json('TrCacheKey', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['rule_id'] = (lambda x: x.to_json())(self.rule_id)
        res['rule_version'] = _atd_write_string(self.rule_version)
        res['engine_version'] = _atd_write_int(self.engine_version)
        res['package_url'] = _atd_write_string(self.package_url)
        res['extra'] = _atd_write_string(self.extra)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TrCacheKey':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TrQueryCacheResponse:
    """Original type: tr_query_cache_response = { ... }"""

    cached: List[Tuple[TrCacheKey, TrCacheMatchResult]]

    @classmethod
    def from_json(cls, x: Any) -> 'TrQueryCacheResponse':
        if isinstance(x, dict):
            return cls(
                cached=_atd_read_list((lambda x: (TrCacheKey.from_json(x[0]), TrCacheMatchResult.from_json(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x)))(x['cached']) if 'cached' in x else _atd_missing_json_field('TrQueryCacheResponse', 'cached'),
            )
        else:
            _atd_bad_json('TrQueryCacheResponse', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['cached'] = _atd_write_list((lambda x: [(lambda x: x.to_json())(x[0]), (lambda x: x.to_json())(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x)))(self.cached)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TrQueryCacheResponse':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TrQueryCacheRequest:
    """Original type: tr_query_cache_request = { ... }"""

    entries: List[TrCacheKey]

    @classmethod
    def from_json(cls, x: Any) -> 'TrQueryCacheRequest':
        if isinstance(x, dict):
            return cls(
                entries=_atd_read_list(TrCacheKey.from_json)(x['entries']) if 'entries' in x else _atd_missing_json_field('TrQueryCacheRequest', 'entries'),
            )
        else:
            _atd_bad_json('TrQueryCacheRequest', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['entries'] = _atd_write_list((lambda x: x.to_json()))(self.entries)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TrQueryCacheRequest':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TrAddCacheRequest:
    """Original type: tr_add_cache_request = { ... }"""

    new_entries: List[Tuple[TrCacheKey, TrCacheMatchResult]]

    @classmethod
    def from_json(cls, x: Any) -> 'TrAddCacheRequest':
        if isinstance(x, dict):
            return cls(
                new_entries=_atd_read_list((lambda x: (TrCacheKey.from_json(x[0]), TrCacheMatchResult.from_json(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x)))(x['new_entries']) if 'new_entries' in x else _atd_missing_json_field('TrAddCacheRequest', 'new_entries'),
            )
        else:
            _atd_bad_json('TrAddCacheRequest', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['new_entries'] = _atd_write_list((lambda x: [(lambda x: x.to_json())(x[0]), (lambda x: x.to_json())(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x)))(self.new_entries)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TrAddCacheRequest':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Todo:
    """Original type: todo"""

    value: int

    @classmethod
    def from_json(cls, x: Any) -> 'Todo':
        return cls(_atd_read_int(x))

    def to_json(self) -> Any:
        return _atd_write_int(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Todo':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class MatchingDiagnosis:
    """Original type: matching_diagnosis = { ... }"""

    target: Fpath
    unexpected_match_diagnoses: List[UnexpectedMatchDiagnosis]
    unexpected_no_match_diagnoses: List[UnexpectedNoMatchDiagnosis]

    @classmethod
    def from_json(cls, x: Any) -> 'MatchingDiagnosis':
        if isinstance(x, dict):
            return cls(
                target=Fpath.from_json(x['target']) if 'target' in x else _atd_missing_json_field('MatchingDiagnosis', 'target'),
                unexpected_match_diagnoses=_atd_read_list(UnexpectedMatchDiagnosis.from_json)(x['unexpected_match_diagnoses']) if 'unexpected_match_diagnoses' in x else _atd_missing_json_field('MatchingDiagnosis', 'unexpected_match_diagnoses'),
                unexpected_no_match_diagnoses=_atd_read_list(UnexpectedNoMatchDiagnosis.from_json)(x['unexpected_no_match_diagnoses']) if 'unexpected_no_match_diagnoses' in x else _atd_missing_json_field('MatchingDiagnosis', 'unexpected_no_match_diagnoses'),
            )
        else:
            _atd_bad_json('MatchingDiagnosis', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['target'] = (lambda x: x.to_json())(self.target)
        res['unexpected_match_diagnoses'] = _atd_write_list((lambda x: x.to_json()))(self.unexpected_match_diagnoses)
        res['unexpected_no_match_diagnoses'] = _atd_write_list((lambda x: x.to_json()))(self.unexpected_no_match_diagnoses)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'MatchingDiagnosis':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ExpectedReported:
    """Original type: expected_reported = { ... }"""

    expected_lines: List[int]
    reported_lines: List[int]

    @classmethod
    def from_json(cls, x: Any) -> 'ExpectedReported':
        if isinstance(x, dict):
            return cls(
                expected_lines=_atd_read_list(_atd_read_int)(x['expected_lines']) if 'expected_lines' in x else _atd_missing_json_field('ExpectedReported', 'expected_lines'),
                reported_lines=_atd_read_list(_atd_read_int)(x['reported_lines']) if 'reported_lines' in x else _atd_missing_json_field('ExpectedReported', 'reported_lines'),
            )
        else:
            _atd_bad_json('ExpectedReported', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['expected_lines'] = _atd_write_list(_atd_write_int)(self.expected_lines)
        res['reported_lines'] = _atd_write_list(_atd_write_int)(self.reported_lines)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ExpectedReported':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class RuleResult:
    """Original type: rule_result = { ... }"""

    passed: bool
    matches: List[Tuple[str, ExpectedReported]]
    errors: List[Todo]
    diagnosis: Optional[MatchingDiagnosis] = None

    @classmethod
    def from_json(cls, x: Any) -> 'RuleResult':
        if isinstance(x, dict):
            return cls(
                passed=_atd_read_bool(x['passed']) if 'passed' in x else _atd_missing_json_field('RuleResult', 'passed'),
                matches=_atd_read_assoc_object_into_list(ExpectedReported.from_json)(x['matches']) if 'matches' in x else _atd_missing_json_field('RuleResult', 'matches'),
                errors=_atd_read_list(Todo.from_json)(x['errors']) if 'errors' in x else _atd_missing_json_field('RuleResult', 'errors'),
                diagnosis=MatchingDiagnosis.from_json(x['diagnosis']) if 'diagnosis' in x else None,
            )
        else:
            _atd_bad_json('RuleResult', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['passed'] = _atd_write_bool(self.passed)
        res['matches'] = _atd_write_assoc_list_to_object((lambda x: x.to_json()))(self.matches)
        res['errors'] = _atd_write_list((lambda x: x.to_json()))(self.errors)
        if self.diagnosis is not None:
            res['diagnosis'] = (lambda x: x.to_json())(self.diagnosis)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'RuleResult':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class FixtestResult:
    """Original type: fixtest_result = { ... }"""

    passed: bool

    @classmethod
    def from_json(cls, x: Any) -> 'FixtestResult':
        if isinstance(x, dict):
            return cls(
                passed=_atd_read_bool(x['passed']) if 'passed' in x else _atd_missing_json_field('FixtestResult', 'passed'),
            )
        else:
            _atd_bad_json('FixtestResult', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['passed'] = _atd_write_bool(self.passed)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'FixtestResult':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class UnparsableRule:
    """Original type: config_error_reason = [ ... | UnparsableRule | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'UnparsableRule'

    @staticmethod
    def to_json() -> Any:
        return 'unparsable_rule'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ConfigErrorReason:
    """Original type: config_error_reason = [ ... ]"""

    value: Union[UnparsableRule]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ConfigErrorReason':
        if isinstance(x, str):
            if x == 'unparsable_rule':
                return cls(UnparsableRule())
            _atd_bad_json('ConfigErrorReason', x)
        _atd_bad_json('ConfigErrorReason', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ConfigErrorReason':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ConfigError:
    """Original type: config_error = { ... }"""

    file: Fpath
    reason: ConfigErrorReason

    @classmethod
    def from_json(cls, x: Any) -> 'ConfigError':
        if isinstance(x, dict):
            return cls(
                file=Fpath.from_json(x['file']) if 'file' in x else _atd_missing_json_field('ConfigError', 'file'),
                reason=ConfigErrorReason.from_json(x['reason']) if 'reason' in x else _atd_missing_json_field('ConfigError', 'reason'),
            )
        else:
            _atd_bad_json('ConfigError', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['file'] = (lambda x: x.to_json())(self.file)
        res['reason'] = (lambda x: x.to_json())(self.reason)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ConfigError':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Checks:
    """Original type: checks = { ... }"""

    checks: List[Tuple[str, RuleResult]]

    @classmethod
    def from_json(cls, x: Any) -> 'Checks':
        if isinstance(x, dict):
            return cls(
                checks=_atd_read_assoc_object_into_list(RuleResult.from_json)(x['checks']) if 'checks' in x else _atd_missing_json_field('Checks', 'checks'),
            )
        else:
            _atd_bad_json('Checks', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['checks'] = _atd_write_assoc_list_to_object((lambda x: x.to_json()))(self.checks)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Checks':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TestsResult:
    """Original type: tests_result = { ... }"""

    results: List[Tuple[str, Checks]]
    fixtest_results: List[Tuple[str, FixtestResult]]
    config_missing_tests: List[Fpath]
    config_missing_fixtests: List[Fpath]
    config_with_errors: List[ConfigError]

    @classmethod
    def from_json(cls, x: Any) -> 'TestsResult':
        if isinstance(x, dict):
            return cls(
                results=_atd_read_assoc_object_into_list(Checks.from_json)(x['results']) if 'results' in x else _atd_missing_json_field('TestsResult', 'results'),
                fixtest_results=_atd_read_assoc_object_into_list(FixtestResult.from_json)(x['fixtest_results']) if 'fixtest_results' in x else _atd_missing_json_field('TestsResult', 'fixtest_results'),
                config_missing_tests=_atd_read_list(Fpath.from_json)(x['config_missing_tests']) if 'config_missing_tests' in x else _atd_missing_json_field('TestsResult', 'config_missing_tests'),
                config_missing_fixtests=_atd_read_list(Fpath.from_json)(x['config_missing_fixtests']) if 'config_missing_fixtests' in x else _atd_missing_json_field('TestsResult', 'config_missing_fixtests'),
                config_with_errors=_atd_read_list(ConfigError.from_json)(x['config_with_errors']) if 'config_with_errors' in x else _atd_missing_json_field('TestsResult', 'config_with_errors'),
            )
        else:
            _atd_bad_json('TestsResult', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['results'] = _atd_write_assoc_list_to_object((lambda x: x.to_json()))(self.results)
        res['fixtest_results'] = _atd_write_assoc_list_to_object((lambda x: x.to_json()))(self.fixtest_results)
        res['config_missing_tests'] = _atd_write_list((lambda x: x.to_json()))(self.config_missing_tests)
        res['config_missing_fixtests'] = _atd_write_list((lambda x: x.to_json()))(self.config_missing_fixtests)
        res['config_with_errors'] = _atd_write_list((lambda x: x.to_json()))(self.config_with_errors)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TestsResult':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Filesystem:
    """Original type: project_root = [ ... | Filesystem of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Filesystem'

    def to_json(self) -> Any:
        return ['Filesystem', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class GitRemote:
    """Original type: project_root = [ ... | Git_remote of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'GitRemote'

    def to_json(self) -> Any:
        return ['Git_remote', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ProjectRoot:
    """Original type: project_root = [ ... ]"""

    value: Union[Filesystem, GitRemote]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ProjectRoot':
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'Filesystem':
                return cls(Filesystem(_atd_read_string(x[1])))
            if cons == 'Git_remote':
                return cls(GitRemote(_atd_read_string(x[1])))
            _atd_bad_json('ProjectRoot', x)
        _atd_bad_json('ProjectRoot', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ProjectRoot':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TargetingConf:
    """Original type: targeting_conf = { ... }"""

    exclude: List[str]
    max_target_bytes: int
    respect_gitignore: bool
    respect_semgrepignore_files: bool
    always_select_explicit_targets: bool
    explicit_targets: List[str]
    force_novcs_project: bool
    exclude_minified_files: bool
    include_: Optional[List[str]] = None
    semgrepignore_filename: Optional[str] = None
    force_project_root: Optional[ProjectRoot] = None
    baseline_commit: Optional[str] = None

    @classmethod
    def from_json(cls, x: Any) -> 'TargetingConf':
        if isinstance(x, dict):
            return cls(
                exclude=_atd_read_list(_atd_read_string)(x['exclude']) if 'exclude' in x else _atd_missing_json_field('TargetingConf', 'exclude'),
                max_target_bytes=_atd_read_int(x['max_target_bytes']) if 'max_target_bytes' in x else _atd_missing_json_field('TargetingConf', 'max_target_bytes'),
                respect_gitignore=_atd_read_bool(x['respect_gitignore']) if 'respect_gitignore' in x else _atd_missing_json_field('TargetingConf', 'respect_gitignore'),
                respect_semgrepignore_files=_atd_read_bool(x['respect_semgrepignore_files']) if 'respect_semgrepignore_files' in x else _atd_missing_json_field('TargetingConf', 'respect_semgrepignore_files'),
                always_select_explicit_targets=_atd_read_bool(x['always_select_explicit_targets']) if 'always_select_explicit_targets' in x else _atd_missing_json_field('TargetingConf', 'always_select_explicit_targets'),
                explicit_targets=_atd_read_list(_atd_read_string)(x['explicit_targets']) if 'explicit_targets' in x else _atd_missing_json_field('TargetingConf', 'explicit_targets'),
                force_novcs_project=_atd_read_bool(x['force_novcs_project']) if 'force_novcs_project' in x else _atd_missing_json_field('TargetingConf', 'force_novcs_project'),
                exclude_minified_files=_atd_read_bool(x['exclude_minified_files']) if 'exclude_minified_files' in x else _atd_missing_json_field('TargetingConf', 'exclude_minified_files'),
                include_=_atd_read_list(_atd_read_string)(x['include_']) if 'include_' in x else None,
                semgrepignore_filename=_atd_read_string(x['semgrepignore_filename']) if 'semgrepignore_filename' in x else None,
                force_project_root=ProjectRoot.from_json(x['force_project_root']) if 'force_project_root' in x else None,
                baseline_commit=_atd_read_string(x['baseline_commit']) if 'baseline_commit' in x else None,
            )
        else:
            _atd_bad_json('TargetingConf', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['exclude'] = _atd_write_list(_atd_write_string)(self.exclude)
        res['max_target_bytes'] = _atd_write_int(self.max_target_bytes)
        res['respect_gitignore'] = _atd_write_bool(self.respect_gitignore)
        res['respect_semgrepignore_files'] = _atd_write_bool(self.respect_semgrepignore_files)
        res['always_select_explicit_targets'] = _atd_write_bool(self.always_select_explicit_targets)
        res['explicit_targets'] = _atd_write_list(_atd_write_string)(self.explicit_targets)
        res['force_novcs_project'] = _atd_write_bool(self.force_novcs_project)
        res['exclude_minified_files'] = _atd_write_bool(self.exclude_minified_files)
        if self.include_ is not None:
            res['include_'] = _atd_write_list(_atd_write_string)(self.include_)
        if self.semgrepignore_filename is not None:
            res['semgrepignore_filename'] = _atd_write_string(self.semgrepignore_filename)
        if self.force_project_root is not None:
            res['force_project_root'] = (lambda x: x.to_json())(self.force_project_root)
        if self.baseline_commit is not None:
            res['baseline_commit'] = _atd_write_string(self.baseline_commit)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TargetingConf':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class SAST:
    """Original type: product = [ ... | SAST | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SAST'

    @staticmethod
    def to_json() -> Any:
        return 'sast'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class SCA:
    """Original type: product = [ ... | SCA | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SCA'

    @staticmethod
    def to_json() -> Any:
        return 'sca'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Secrets:
    """Original type: product = [ ... | Secrets | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Secrets'

    @staticmethod
    def to_json() -> Any:
        return 'secrets'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Product:
    """Original type: product = [ ... ]"""

    value: Union[SAST, SCA, Secrets]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'Product':
        if isinstance(x, str):
            if x == 'sast':
                return cls(SAST())
            if x == 'sca':
                return cls(SCA())
            if x == 'secrets':
                return cls(Secrets())
            _atd_bad_json('Product', x)
        _atd_bad_json('Product', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'Product':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Analyzer:
    """Original type: analyzer"""

    value: str

    @classmethod
    def from_json(cls, x: Any) -> 'Analyzer':
        return cls(_atd_read_string(x))

    def to_json(self) -> Any:
        return _atd_write_string(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Analyzer':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CodeTarget:
    """Original type: code_target = { ... }"""

    path: Fpath
    analyzer: Analyzer
    products: List[Product]
    dependency_source: Optional[DependencySource] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CodeTarget':
        if isinstance(x, dict):
            return cls(
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('CodeTarget', 'path'),
                analyzer=Analyzer.from_json(x['analyzer']) if 'analyzer' in x else _atd_missing_json_field('CodeTarget', 'analyzer'),
                products=_atd_read_list(Product.from_json)(x['products']) if 'products' in x else _atd_missing_json_field('CodeTarget', 'products'),
                dependency_source=DependencySource.from_json(x['dependency_source']) if 'dependency_source' in x else None,
            )
        else:
            _atd_bad_json('CodeTarget', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['path'] = (lambda x: x.to_json())(self.path)
        res['analyzer'] = (lambda x: x.to_json())(self.analyzer)
        res['products'] = _atd_write_list((lambda x: x.to_json()))(self.products)
        if self.dependency_source is not None:
            res['dependency_source'] = (lambda x: x.to_json())(self.dependency_source)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CodeTarget':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CodeTarget_:
    """Original type: target = [ ... | CodeTarget of ... | ... ]"""

    value: CodeTarget

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CodeTarget_'

    def to_json(self) -> Any:
        return ['CodeTarget', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DependencySourceTarget:
    """Original type: target = [ ... | DependencySourceTarget of ... | ... ]"""

    value: DependencySource

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'DependencySourceTarget'

    def to_json(self) -> Any:
        return ['DependencySourceTarget', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Target:
    """Original type: target = [ ... ]"""

    value: Union[CodeTarget_, DependencySourceTarget]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'Target':
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'CodeTarget':
                return cls(CodeTarget_(CodeTarget.from_json(x[1])))
            if cons == 'DependencySourceTarget':
                return cls(DependencySourceTarget(DependencySource.from_json(x[1])))
            _atd_bad_json('Target', x)
        _atd_bad_json('Target', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'Target':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScanningRoots:
    """Original type: scanning_roots = { ... }"""

    root_paths: List[Fpath]
    targeting_conf: TargetingConf

    @classmethod
    def from_json(cls, x: Any) -> 'ScanningRoots':
        if isinstance(x, dict):
            return cls(
                root_paths=_atd_read_list(Fpath.from_json)(x['root_paths']) if 'root_paths' in x else _atd_missing_json_field('ScanningRoots', 'root_paths'),
                targeting_conf=TargetingConf.from_json(x['targeting_conf']) if 'targeting_conf' in x else _atd_missing_json_field('ScanningRoots', 'targeting_conf'),
            )
        else:
            _atd_bad_json('ScanningRoots', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['root_paths'] = _atd_write_list((lambda x: x.to_json()))(self.root_paths)
        res['targeting_conf'] = (lambda x: x.to_json())(self.targeting_conf)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ScanningRoots':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScanningRoots_:
    """Original type: targets = [ ... | Scanning_roots of ... | ... ]"""

    value: ScanningRoots

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ScanningRoots_'

    def to_json(self) -> Any:
        return ['Scanning_roots', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Targets_:
    """Original type: targets = [ ... | Targets of ... | ... ]"""

    value: List[Target]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Targets_'

    def to_json(self) -> Any:
        return ['Targets', _atd_write_list((lambda x: x.to_json()))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Targets:
    """Original type: targets = [ ... ]"""

    value: Union[ScanningRoots_, Targets_]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'Targets':
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'Scanning_roots':
                return cls(ScanningRoots_(ScanningRoots.from_json(x[1])))
            if cons == 'Targets':
                return cls(Targets_(_atd_read_list(Target.from_json)(x[1])))
            _atd_bad_json('Targets', x)
        _atd_bad_json('Targets', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'Targets':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TargetTimes:
    """Original type: target_times = { ... }"""

    path: Fpath
    num_bytes: int
    match_times: List[float]
    parse_times: List[float]
    run_time: float

    @classmethod
    def from_json(cls, x: Any) -> 'TargetTimes':
        if isinstance(x, dict):
            return cls(
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('TargetTimes', 'path'),
                num_bytes=_atd_read_int(x['num_bytes']) if 'num_bytes' in x else _atd_missing_json_field('TargetTimes', 'num_bytes'),
                match_times=_atd_read_list(_atd_read_float)(x['match_times']) if 'match_times' in x else _atd_missing_json_field('TargetTimes', 'match_times'),
                parse_times=_atd_read_list(_atd_read_float)(x['parse_times']) if 'parse_times' in x else _atd_missing_json_field('TargetTimes', 'parse_times'),
                run_time=_atd_read_float(x['run_time']) if 'run_time' in x else _atd_missing_json_field('TargetTimes', 'run_time'),
            )
        else:
            _atd_bad_json('TargetTimes', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['path'] = (lambda x: x.to_json())(self.path)
        res['num_bytes'] = _atd_write_int(self.num_bytes)
        res['match_times'] = _atd_write_list(_atd_write_float)(self.match_times)
        res['parse_times'] = _atd_write_list(_atd_write_float)(self.parse_times)
        res['run_time'] = _atd_write_float(self.run_time)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TargetTimes':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class AlwaysSkipped:
    """Original type: skip_reason = [ ... | Always_skipped | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'AlwaysSkipped'

    @staticmethod
    def to_json() -> Any:
        return 'always_skipped'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SemgrepignorePatternsMatch:
    """Original type: skip_reason = [ ... | Semgrepignore_patterns_match | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SemgrepignorePatternsMatch'

    @staticmethod
    def to_json() -> Any:
        return 'semgrepignore_patterns_match'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CliIncludeFlagsDoNotMatch:
    """Original type: skip_reason = [ ... | Cli_include_flags_do_not_match | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CliIncludeFlagsDoNotMatch'

    @staticmethod
    def to_json() -> Any:
        return 'cli_include_flags_do_not_match'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CliExcludeFlagsMatch:
    """Original type: skip_reason = [ ... | Cli_exclude_flags_match | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CliExcludeFlagsMatch'

    @staticmethod
    def to_json() -> Any:
        return 'cli_exclude_flags_match'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ExceededSizeLimit:
    """Original type: skip_reason = [ ... | Exceeded_size_limit | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ExceededSizeLimit'

    @staticmethod
    def to_json() -> Any:
        return 'exceeded_size_limit'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class AnalysisFailedParserOrInternalError:
    """Original type: skip_reason = [ ... | Analysis_failed_parser_or_internal_error | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'AnalysisFailedParserOrInternalError'

    @staticmethod
    def to_json() -> Any:
        return 'analysis_failed_parser_or_internal_error'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ExcludedByConfig:
    """Original type: skip_reason = [ ... | Excluded_by_config | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ExcludedByConfig'

    @staticmethod
    def to_json() -> Any:
        return 'excluded_by_config'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class WrongLanguage:
    """Original type: skip_reason = [ ... | Wrong_language | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'WrongLanguage'

    @staticmethod
    def to_json() -> Any:
        return 'wrong_language'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TooBig:
    """Original type: skip_reason = [ ... | Too_big | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'TooBig'

    @staticmethod
    def to_json() -> Any:
        return 'too_big'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Minified:
    """Original type: skip_reason = [ ... | Minified | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Minified'

    @staticmethod
    def to_json() -> Any:
        return 'minified'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Binary:
    """Original type: skip_reason = [ ... | Binary | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Binary'

    @staticmethod
    def to_json() -> Any:
        return 'binary'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class IrrelevantRule:
    """Original type: skip_reason = [ ... | Irrelevant_rule | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'IrrelevantRule'

    @staticmethod
    def to_json() -> Any:
        return 'irrelevant_rule'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TooManyMatches:
    """Original type: skip_reason = [ ... | Too_many_matches | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'TooManyMatches'

    @staticmethod
    def to_json() -> Any:
        return 'too_many_matches'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class GitignorePatternsMatch:
    """Original type: skip_reason = [ ... | Gitignore_patterns_match | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'GitignorePatternsMatch'

    @staticmethod
    def to_json() -> Any:
        return 'Gitignore_patterns_match'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Dotfile:
    """Original type: skip_reason = [ ... | Dotfile | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Dotfile'

    @staticmethod
    def to_json() -> Any:
        return 'Dotfile'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class NonexistentFile:
    """Original type: skip_reason = [ ... | Nonexistent_file | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'NonexistentFile'

    @staticmethod
    def to_json() -> Any:
        return 'Nonexistent_file'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class InsufficientPermissions:
    """Original type: skip_reason = [ ... | Insufficient_permissions | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'InsufficientPermissions'

    @staticmethod
    def to_json() -> Any:
        return 'insufficient_permissions'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SkipReason:
    """Original type: skip_reason = [ ... ]"""

    value: Union[AlwaysSkipped, SemgrepignorePatternsMatch, CliIncludeFlagsDoNotMatch, CliExcludeFlagsMatch, ExceededSizeLimit, AnalysisFailedParserOrInternalError, ExcludedByConfig, WrongLanguage, TooBig, Minified, Binary, IrrelevantRule, TooManyMatches, GitignorePatternsMatch, Dotfile, NonexistentFile, InsufficientPermissions]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'SkipReason':
        if isinstance(x, str):
            if x == 'always_skipped':
                return cls(AlwaysSkipped())
            if x == 'semgrepignore_patterns_match':
                return cls(SemgrepignorePatternsMatch())
            if x == 'cli_include_flags_do_not_match':
                return cls(CliIncludeFlagsDoNotMatch())
            if x == 'cli_exclude_flags_match':
                return cls(CliExcludeFlagsMatch())
            if x == 'exceeded_size_limit':
                return cls(ExceededSizeLimit())
            if x == 'analysis_failed_parser_or_internal_error':
                return cls(AnalysisFailedParserOrInternalError())
            if x == 'excluded_by_config':
                return cls(ExcludedByConfig())
            if x == 'wrong_language':
                return cls(WrongLanguage())
            if x == 'too_big':
                return cls(TooBig())
            if x == 'minified':
                return cls(Minified())
            if x == 'binary':
                return cls(Binary())
            if x == 'irrelevant_rule':
                return cls(IrrelevantRule())
            if x == 'too_many_matches':
                return cls(TooManyMatches())
            if x == 'Gitignore_patterns_match':
                return cls(GitignorePatternsMatch())
            if x == 'Dotfile':
                return cls(Dotfile())
            if x == 'Nonexistent_file':
                return cls(NonexistentFile())
            if x == 'insufficient_permissions':
                return cls(InsufficientPermissions())
            _atd_bad_json('SkipReason', x)
        _atd_bad_json('SkipReason', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'SkipReason':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SkippedTarget:
    """Original type: skipped_target = { ... }"""

    path: Fpath
    reason: SkipReason
    details: Optional[str] = None
    rule_id: Optional[RuleId] = None

    @classmethod
    def from_json(cls, x: Any) -> 'SkippedTarget':
        if isinstance(x, dict):
            return cls(
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('SkippedTarget', 'path'),
                reason=SkipReason.from_json(x['reason']) if 'reason' in x else _atd_missing_json_field('SkippedTarget', 'reason'),
                details=_atd_read_string(x['details']) if 'details' in x else None,
                rule_id=RuleId.from_json(x['rule_id']) if 'rule_id' in x else None,
            )
        else:
            _atd_bad_json('SkippedTarget', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['path'] = (lambda x: x.to_json())(self.path)
        res['reason'] = (lambda x: x.to_json())(self.reason)
        if self.details is not None:
            res['details'] = _atd_write_string(self.details)
        if self.rule_id is not None:
            res['rule_id'] = (lambda x: x.to_json())(self.rule_id)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'SkippedTarget':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class IncompatibleRule:
    """Original type: incompatible_rule = { ... }"""

    rule_id: RuleId
    this_version: Version
    min_version: Optional[Version] = None
    max_version: Optional[Version] = None

    @classmethod
    def from_json(cls, x: Any) -> 'IncompatibleRule':
        if isinstance(x, dict):
            return cls(
                rule_id=RuleId.from_json(x['rule_id']) if 'rule_id' in x else _atd_missing_json_field('IncompatibleRule', 'rule_id'),
                this_version=Version.from_json(x['this_version']) if 'this_version' in x else _atd_missing_json_field('IncompatibleRule', 'this_version'),
                min_version=Version.from_json(x['min_version']) if 'min_version' in x else None,
                max_version=Version.from_json(x['max_version']) if 'max_version' in x else None,
            )
        else:
            _atd_bad_json('IncompatibleRule', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['rule_id'] = (lambda x: x.to_json())(self.rule_id)
        res['this_version'] = (lambda x: x.to_json())(self.this_version)
        if self.min_version is not None:
            res['min_version'] = (lambda x: x.to_json())(self.min_version)
        if self.max_version is not None:
            res['max_version'] = (lambda x: x.to_json())(self.max_version)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'IncompatibleRule':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class LexicalError:
    """Original type: error_type = [ ... | LexicalError | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'LexicalError'

    @staticmethod
    def to_json() -> Any:
        return 'Lexical error'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class ParseError:
    """Original type: error_type = [ ... | ParseError | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ParseError'

    @staticmethod
    def to_json() -> Any:
        return 'Syntax error'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class OtherParseError:
    """Original type: error_type = [ ... | OtherParseError | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'OtherParseError'

    @staticmethod
    def to_json() -> Any:
        return 'Other syntax error'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class AstBuilderError:
    """Original type: error_type = [ ... | AstBuilderError | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'AstBuilderError'

    @staticmethod
    def to_json() -> Any:
        return 'AST builder error'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class RuleParseError:
    """Original type: error_type = [ ... | RuleParseError | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RuleParseError'

    @staticmethod
    def to_json() -> Any:
        return 'Rule parse error'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class SemgrepWarning:
    """Original type: error_type = [ ... | SemgrepWarning | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SemgrepWarning'

    @staticmethod
    def to_json() -> Any:
        return 'SemgrepWarning'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class SemgrepError:
    """Original type: error_type = [ ... | SemgrepError | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SemgrepError'

    @staticmethod
    def to_json() -> Any:
        return 'SemgrepError'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class InvalidRuleSchemaError:
    """Original type: error_type = [ ... | InvalidRuleSchemaError | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'InvalidRuleSchemaError'

    @staticmethod
    def to_json() -> Any:
        return 'InvalidRuleSchemaError'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class UnknownLanguageError:
    """Original type: error_type = [ ... | UnknownLanguageError | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'UnknownLanguageError'

    @staticmethod
    def to_json() -> Any:
        return 'UnknownLanguageError'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class InvalidYaml:
    """Original type: error_type = [ ... | InvalidYaml | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'InvalidYaml'

    @staticmethod
    def to_json() -> Any:
        return 'Invalid YAML'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class MatchingError:
    """Original type: error_type = [ ... | MatchingError | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'MatchingError'

    @staticmethod
    def to_json() -> Any:
        return 'Internal matching error'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class SemgrepMatchFound:
    """Original type: error_type = [ ... | SemgrepMatchFound | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'SemgrepMatchFound'

    @staticmethod
    def to_json() -> Any:
        return 'Semgrep match found'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class TooManyMatches_:
    """Original type: error_type = [ ... | TooManyMatches | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'TooManyMatches_'

    @staticmethod
    def to_json() -> Any:
        return 'Too many matches'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class FatalError:
    """Original type: error_type = [ ... | FatalError | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'FatalError'

    @staticmethod
    def to_json() -> Any:
        return 'Fatal error'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class Timeout:
    """Original type: error_type = [ ... | Timeout | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Timeout'

    @staticmethod
    def to_json() -> Any:
        return 'Timeout'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class OutOfMemory:
    """Original type: error_type = [ ... | OutOfMemory | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'OutOfMemory'

    @staticmethod
    def to_json() -> Any:
        return 'Out of memory'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class StackOverflow:
    """Original type: error_type = [ ... | StackOverflow | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'StackOverflow'

    @staticmethod
    def to_json() -> Any:
        return 'Stack overflow'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class TimeoutDuringInterfile:
    """Original type: error_type = [ ... | TimeoutDuringInterfile | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'TimeoutDuringInterfile'

    @staticmethod
    def to_json() -> Any:
        return 'Timeout during interfile analysis'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class OutOfMemoryDuringInterfile:
    """Original type: error_type = [ ... | OutOfMemoryDuringInterfile | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'OutOfMemoryDuringInterfile'

    @staticmethod
    def to_json() -> Any:
        return 'OOM during interfile analysis'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class MissingPlugin:
    """Original type: error_type = [ ... | MissingPlugin | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'MissingPlugin'

    @staticmethod
    def to_json() -> Any:
        return 'Missing plugin'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class PatternParseError:
    """Original type: error_type = [ ... | PatternParseError of ... | ... ]"""

    value: List[str]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PatternParseError'

    def to_json(self) -> Any:
        return ['PatternParseError', _atd_write_list(_atd_write_string)(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class PartialParsing:
    """Original type: error_type = [ ... | PartialParsing of ... | ... ]"""

    value: List[Location]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PartialParsing'

    def to_json(self) -> Any:
        return ['PartialParsing', _atd_write_list((lambda x: x.to_json()))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class IncompatibleRule_:
    """Original type: error_type = [ ... | IncompatibleRule of ... | ... ]"""

    value: IncompatibleRule

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'IncompatibleRule_'

    def to_json(self) -> Any:
        return ['IncompatibleRule', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class PatternParseError0:
    """Original type: error_type = [ ... | PatternParseError0 | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PatternParseError0'

    @staticmethod
    def to_json() -> Any:
        return 'Pattern parse error'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class IncompatibleRule0:
    """Original type: error_type = [ ... | IncompatibleRule0 | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'IncompatibleRule0'

    @staticmethod
    def to_json() -> Any:
        return 'Incompatible rule'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class DependencyResolutionError:
    """Original type: error_type = [ ... | DependencyResolutionError of ... | ... ]"""

    value: ResolutionErrorKind

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'DependencyResolutionError'

    def to_json(self) -> Any:
        return ['DependencyResolutionError', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class ErrorType:
    """Original type: error_type = [ ... ]"""

    value: Union[LexicalError, ParseError, OtherParseError, AstBuilderError, RuleParseError, SemgrepWarning, SemgrepError, InvalidRuleSchemaError, UnknownLanguageError, InvalidYaml, MatchingError, SemgrepMatchFound, TooManyMatches_, FatalError, Timeout, OutOfMemory, StackOverflow, TimeoutDuringInterfile, OutOfMemoryDuringInterfile, MissingPlugin, PatternParseError, PartialParsing, IncompatibleRule_, PatternParseError0, IncompatibleRule0, DependencyResolutionError]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ErrorType':
        if isinstance(x, str):
            if x == 'Lexical error':
                return cls(LexicalError())
            if x == 'Syntax error':
                return cls(ParseError())
            if x == 'Other syntax error':
                return cls(OtherParseError())
            if x == 'AST builder error':
                return cls(AstBuilderError())
            if x == 'Rule parse error':
                return cls(RuleParseError())
            if x == 'SemgrepWarning':
                return cls(SemgrepWarning())
            if x == 'SemgrepError':
                return cls(SemgrepError())
            if x == 'InvalidRuleSchemaError':
                return cls(InvalidRuleSchemaError())
            if x == 'UnknownLanguageError':
                return cls(UnknownLanguageError())
            if x == 'Invalid YAML':
                return cls(InvalidYaml())
            if x == 'Internal matching error':
                return cls(MatchingError())
            if x == 'Semgrep match found':
                return cls(SemgrepMatchFound())
            if x == 'Too many matches':
                return cls(TooManyMatches_())
            if x == 'Fatal error':
                return cls(FatalError())
            if x == 'Timeout':
                return cls(Timeout())
            if x == 'Out of memory':
                return cls(OutOfMemory())
            if x == 'Stack overflow':
                return cls(StackOverflow())
            if x == 'Timeout during interfile analysis':
                return cls(TimeoutDuringInterfile())
            if x == 'OOM during interfile analysis':
                return cls(OutOfMemoryDuringInterfile())
            if x == 'Missing plugin':
                return cls(MissingPlugin())
            if x == 'Pattern parse error':
                return cls(PatternParseError0())
            if x == 'Incompatible rule':
                return cls(IncompatibleRule0())
            _atd_bad_json('ErrorType', x)
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'PatternParseError':
                return cls(PatternParseError(_atd_read_list(_atd_read_string)(x[1])))
            if cons == 'PartialParsing':
                return cls(PartialParsing(_atd_read_list(Location.from_json)(x[1])))
            if cons == 'IncompatibleRule':
                return cls(IncompatibleRule_(IncompatibleRule.from_json(x[1])))
            if cons == 'DependencyResolutionError':
                return cls(DependencyResolutionError(ResolutionErrorKind.from_json(x[1])))
            _atd_bad_json('ErrorType', x)
        _atd_bad_json('ErrorType', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ErrorType':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Error_:
    """Original type: error_severity = [ ... | Error | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Error_'

    @staticmethod
    def to_json() -> Any:
        return 'error'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Warning_:
    """Original type: error_severity = [ ... | Warning | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Warning_'

    @staticmethod
    def to_json() -> Any:
        return 'warn'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Info_:
    """Original type: error_severity = [ ... | Info | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Info_'

    @staticmethod
    def to_json() -> Any:
        return 'info'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ErrorSeverity:
    """Original type: error_severity = [ ... ]"""

    value: Union[Error_, Warning_, Info_]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ErrorSeverity':
        if isinstance(x, str):
            if x == 'error':
                return cls(Error_())
            if x == 'warn':
                return cls(Warning_())
            if x == 'info':
                return cls(Info_())
            _atd_bad_json('ErrorSeverity', x)
        _atd_bad_json('ErrorSeverity', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ErrorSeverity':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CoreError:
    """Original type: core_error = { ... }"""

    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    details: Optional[str] = None
    location: Optional[Location] = None
    rule_id: Optional[RuleId] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CoreError':
        if isinstance(x, dict):
            return cls(
                error_type=ErrorType.from_json(x['error_type']) if 'error_type' in x else _atd_missing_json_field('CoreError', 'error_type'),
                severity=ErrorSeverity.from_json(x['severity']) if 'severity' in x else _atd_missing_json_field('CoreError', 'severity'),
                message=_atd_read_string(x['message']) if 'message' in x else _atd_missing_json_field('CoreError', 'message'),
                details=_atd_read_string(x['details']) if 'details' in x else None,
                location=Location.from_json(x['location']) if 'location' in x else None,
                rule_id=RuleId.from_json(x['rule_id']) if 'rule_id' in x else None,
            )
        else:
            _atd_bad_json('CoreError', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['error_type'] = (lambda x: x.to_json())(self.error_type)
        res['severity'] = (lambda x: x.to_json())(self.severity)
        res['message'] = _atd_write_string(self.message)
        if self.details is not None:
            res['details'] = _atd_write_string(self.details)
        if self.location is not None:
            res['location'] = (lambda x: x.to_json())(self.location)
        if self.rule_id is not None:
            res['rule_id'] = (lambda x: x.to_json())(self.rule_id)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CoreError':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class TargetDiscoveryResult:
    """Original type: target_discovery_result = { ... }"""

    target_paths: List[Fpath]
    errors: List[CoreError]
    skipped: List[SkippedTarget]

    @classmethod
    def from_json(cls, x: Any) -> 'TargetDiscoveryResult':
        if isinstance(x, dict):
            return cls(
                target_paths=_atd_read_list(Fpath.from_json)(x['target_paths']) if 'target_paths' in x else _atd_missing_json_field('TargetDiscoveryResult', 'target_paths'),
                errors=_atd_read_list(CoreError.from_json)(x['errors']) if 'errors' in x else _atd_missing_json_field('TargetDiscoveryResult', 'errors'),
                skipped=_atd_read_list(SkippedTarget.from_json)(x['skipped']) if 'skipped' in x else _atd_missing_json_field('TargetDiscoveryResult', 'skipped'),
            )
        else:
            _atd_bad_json('TargetDiscoveryResult', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['target_paths'] = _atd_write_list((lambda x: x.to_json()))(self.target_paths)
        res['errors'] = _atd_write_list((lambda x: x.to_json()))(self.errors)
        res['skipped'] = _atd_write_list((lambda x: x.to_json()))(self.skipped)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'TargetDiscoveryResult':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Tag:
    """Original type: tag"""

    value: str

    @classmethod
    def from_json(cls, x: Any) -> 'Tag':
        return cls(_atd_read_string(x))

    def to_json(self) -> Any:
        return _atd_write_string(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Tag':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Symbol:
    """Original type: symbol = { ... }"""

    fqn: List[str]

    @classmethod
    def from_json(cls, x: Any) -> 'Symbol':
        if isinstance(x, dict):
            return cls(
                fqn=_atd_read_list(_atd_read_string)(x['fqn']) if 'fqn' in x else _atd_missing_json_field('Symbol', 'fqn'),
            )
        else:
            _atd_bad_json('Symbol', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['fqn'] = _atd_write_list(_atd_write_string)(self.fqn)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Symbol':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SymbolUsage:
    """Original type: symbol_usage = { ... }"""

    symbol: Symbol
    locs: List[Location]

    @classmethod
    def from_json(cls, x: Any) -> 'SymbolUsage':
        if isinstance(x, dict):
            return cls(
                symbol=Symbol.from_json(x['symbol']) if 'symbol' in x else _atd_missing_json_field('SymbolUsage', 'symbol'),
                locs=_atd_read_list(Location.from_json)(x['locs']) if 'locs' in x else _atd_missing_json_field('SymbolUsage', 'locs'),
            )
        else:
            _atd_bad_json('SymbolUsage', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['symbol'] = (lambda x: x.to_json())(self.symbol)
        res['locs'] = _atd_write_list((lambda x: x.to_json()))(self.locs)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'SymbolUsage':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SymbolAnalysisUploadResponse:
    """Original type: symbol_analysis_upload_response = { ... }"""

    upload_url: Uri

    @classmethod
    def from_json(cls, x: Any) -> 'SymbolAnalysisUploadResponse':
        if isinstance(x, dict):
            return cls(
                upload_url=Uri.from_json(x['upload_url']) if 'upload_url' in x else _atd_missing_json_field('SymbolAnalysisUploadResponse', 'upload_url'),
            )
        else:
            _atd_bad_json('SymbolAnalysisUploadResponse', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['upload_url'] = (lambda x: x.to_json())(self.upload_url)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'SymbolAnalysisUploadResponse':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SymbolAnalysis:
    """Original type: symbol_analysis"""

    value: List[SymbolUsage]

    @classmethod
    def from_json(cls, x: Any) -> 'SymbolAnalysis':
        return cls(_atd_read_list(SymbolUsage.from_json)(x))

    def to_json(self) -> Any:
        return _atd_write_list((lambda x: x.to_json()))(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'SymbolAnalysis':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class LockfileParsing:
    """Original type: resolution_method = [ ... | LockfileParsing | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'LockfileParsing'

    @staticmethod
    def to_json() -> Any:
        return 'LockfileParsing'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class DynamicResolution:
    """Original type: resolution_method = [ ... | DynamicResolution | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'DynamicResolution'

    @staticmethod
    def to_json() -> Any:
        return 'DynamicResolution'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True, order=True)
class ResolutionMethod:
    """Original type: resolution_method = [ ... ]"""

    value: Union[LockfileParsing, DynamicResolution]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ResolutionMethod':
        if isinstance(x, str):
            if x == 'LockfileParsing':
                return cls(LockfileParsing())
            if x == 'DynamicResolution':
                return cls(DynamicResolution())
            _atd_bad_json('ResolutionMethod', x)
        _atd_bad_json('ResolutionMethod', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ResolutionMethod':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Lockfile_:
    """Original type: dependency_source_file_kind = [ ... | Lockfile of ... | ... ]"""

    value: LockfileKind

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Lockfile_'

    def to_json(self) -> Any:
        return ['Lockfile', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Manifest_:
    """Original type: dependency_source_file_kind = [ ... | Manifest of ... | ... ]"""

    value: ManifestKind

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Manifest_'

    def to_json(self) -> Any:
        return ['Manifest', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class DependencySourceFileKind:
    """Original type: dependency_source_file_kind = [ ... ]"""

    value: Union[Lockfile_, Manifest_]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'DependencySourceFileKind':
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'Lockfile':
                return cls(Lockfile_(LockfileKind.from_json(x[1])))
            if cons == 'Manifest':
                return cls(Manifest_(ManifestKind.from_json(x[1])))
            _atd_bad_json('DependencySourceFileKind', x)
        _atd_bad_json('DependencySourceFileKind', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'DependencySourceFileKind':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DependencySourceFile:
    """Original type: dependency_source_file = { ... }"""

    kind: DependencySourceFileKind
    path: Fpath

    @classmethod
    def from_json(cls, x: Any) -> 'DependencySourceFile':
        if isinstance(x, dict):
            return cls(
                kind=DependencySourceFileKind.from_json(x['kind']) if 'kind' in x else _atd_missing_json_field('DependencySourceFile', 'kind'),
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('DependencySourceFile', 'path'),
            )
        else:
            _atd_bad_json('DependencySourceFile', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['kind'] = (lambda x: x.to_json())(self.kind)
        res['path'] = (lambda x: x.to_json())(self.path)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'DependencySourceFile':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DependencyResolutionStats:
    """Original type: dependency_resolution_stats = { ... }"""

    resolution_method: ResolutionMethod
    dependency_count: int
    ecosystem: Ecosystem

    @classmethod
    def from_json(cls, x: Any) -> 'DependencyResolutionStats':
        if isinstance(x, dict):
            return cls(
                resolution_method=ResolutionMethod.from_json(x['resolution_method']) if 'resolution_method' in x else _atd_missing_json_field('DependencyResolutionStats', 'resolution_method'),
                dependency_count=_atd_read_int(x['dependency_count']) if 'dependency_count' in x else _atd_missing_json_field('DependencyResolutionStats', 'dependency_count'),
                ecosystem=Ecosystem.from_json(x['ecosystem']) if 'ecosystem' in x else _atd_missing_json_field('DependencyResolutionStats', 'ecosystem'),
            )
        else:
            _atd_bad_json('DependencyResolutionStats', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['resolution_method'] = (lambda x: x.to_json())(self.resolution_method)
        res['dependency_count'] = _atd_write_int(self.dependency_count)
        res['ecosystem'] = (lambda x: x.to_json())(self.ecosystem)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'DependencyResolutionStats':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SubprojectStats:
    """Original type: subproject_stats = { ... }"""

    subproject_id: str
    dependency_sources: List[DependencySourceFile]
    resolved_stats: Optional[DependencyResolutionStats] = None
    unresolved_reason: Optional[UnresolvedReason] = None
    errors: List[ScaError] = field(default_factory=lambda: [])

    @classmethod
    def from_json(cls, x: Any) -> 'SubprojectStats':
        if isinstance(x, dict):
            return cls(
                subproject_id=_atd_read_string(x['subproject_id']) if 'subproject_id' in x else _atd_missing_json_field('SubprojectStats', 'subproject_id'),
                dependency_sources=_atd_read_list(DependencySourceFile.from_json)(x['dependency_sources']) if 'dependency_sources' in x else _atd_missing_json_field('SubprojectStats', 'dependency_sources'),
                resolved_stats=DependencyResolutionStats.from_json(x['resolved_stats']) if 'resolved_stats' in x else None,
                unresolved_reason=UnresolvedReason.from_json(x['unresolved_reason']) if 'unresolved_reason' in x else None,
                errors=_atd_read_list(ScaError.from_json)(x['errors']) if 'errors' in x else [],
            )
        else:
            _atd_bad_json('SubprojectStats', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['subproject_id'] = _atd_write_string(self.subproject_id)
        res['dependency_sources'] = _atd_write_list((lambda x: x.to_json()))(self.dependency_sources)
        if self.resolved_stats is not None:
            res['resolved_stats'] = (lambda x: x.to_json())(self.resolved_stats)
        if self.unresolved_reason is not None:
            res['unresolved_reason'] = (lambda x: x.to_json())(self.unresolved_reason)
        res['errors'] = _atd_write_list((lambda x: x.to_json()))(self.errors)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'SubprojectStats':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SupplyChainStats:
    """Original type: supply_chain_stats = { ... }"""

    subprojects_stats: List[SubprojectStats]

    @classmethod
    def from_json(cls, x: Any) -> 'SupplyChainStats':
        if isinstance(x, dict):
            return cls(
                subprojects_stats=_atd_read_list(SubprojectStats.from_json)(x['subprojects_stats']) if 'subprojects_stats' in x else _atd_missing_json_field('SupplyChainStats', 'subprojects_stats'),
            )
        else:
            _atd_bad_json('SupplyChainStats', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['subprojects_stats'] = _atd_write_list((lambda x: x.to_json()))(self.subprojects_stats)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'SupplyChainStats':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SummaryStats:
    """Original type: summary_stats = { ... }"""

    mean: float
    std_dev: float

    @classmethod
    def from_json(cls, x: Any) -> 'SummaryStats':
        if isinstance(x, dict):
            return cls(
                mean=_atd_read_float(x['mean']) if 'mean' in x else _atd_missing_json_field('SummaryStats', 'mean'),
                std_dev=_atd_read_float(x['std_dev']) if 'std_dev' in x else _atd_missing_json_field('SummaryStats', 'std_dev'),
            )
        else:
            _atd_bad_json('SummaryStats', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['mean'] = _atd_write_float(self.mean)
        res['std_dev'] = _atd_write_float(self.std_dev)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'SummaryStats':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class SkippedRule:
    """Original type: skipped_rule = { ... }"""

    rule_id: RuleId
    details: str
    position: Position

    @classmethod
    def from_json(cls, x: Any) -> 'SkippedRule':
        if isinstance(x, dict):
            return cls(
                rule_id=RuleId.from_json(x['rule_id']) if 'rule_id' in x else _atd_missing_json_field('SkippedRule', 'rule_id'),
                details=_atd_read_string(x['details']) if 'details' in x else _atd_missing_json_field('SkippedRule', 'details'),
                position=Position.from_json(x['position']) if 'position' in x else _atd_missing_json_field('SkippedRule', 'position'),
            )
        else:
            _atd_bad_json('SkippedRule', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['rule_id'] = (lambda x: x.to_json())(self.rule_id)
        res['details'] = _atd_write_string(self.details)
        res['position'] = (lambda x: x.to_json())(self.position)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'SkippedRule':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScannedAndSkipped:
    """Original type: scanned_and_skipped = { ... }"""

    scanned: List[Fpath]
    skipped: Optional[List[SkippedTarget]] = None

    @classmethod
    def from_json(cls, x: Any) -> 'ScannedAndSkipped':
        if isinstance(x, dict):
            return cls(
                scanned=_atd_read_list(Fpath.from_json)(x['scanned']) if 'scanned' in x else _atd_missing_json_field('ScannedAndSkipped', 'scanned'),
                skipped=_atd_read_list(SkippedTarget.from_json)(x['skipped']) if 'skipped' in x else None,
            )
        else:
            _atd_bad_json('ScannedAndSkipped', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['scanned'] = _atd_write_list((lambda x: x.to_json()))(self.scanned)
        if self.skipped is not None:
            res['skipped'] = _atd_write_list((lambda x: x.to_json()))(self.skipped)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ScannedAndSkipped':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScanInfo:
    """Original type: scan_info = { ... }"""

    enabled_products: List[Product]
    deployment_id: int
    deployment_name: str
    id: Optional[int] = None

    @classmethod
    def from_json(cls, x: Any) -> 'ScanInfo':
        if isinstance(x, dict):
            return cls(
                enabled_products=_atd_read_list(Product.from_json)(x['enabled_products']) if 'enabled_products' in x else _atd_missing_json_field('ScanInfo', 'enabled_products'),
                deployment_id=_atd_read_int(x['deployment_id']) if 'deployment_id' in x else _atd_missing_json_field('ScanInfo', 'deployment_id'),
                deployment_name=_atd_read_string(x['deployment_name']) if 'deployment_name' in x else _atd_missing_json_field('ScanInfo', 'deployment_name'),
                id=_atd_read_int(x['id']) if 'id' in x else None,
            )
        else:
            _atd_bad_json('ScanInfo', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['enabled_products'] = _atd_write_list((lambda x: x.to_json()))(self.enabled_products)
        res['deployment_id'] = _atd_write_int(self.deployment_id)
        res['deployment_name'] = _atd_write_string(self.deployment_name)
        if self.id is not None:
            res['id'] = _atd_write_int(self.id)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ScanInfo':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScanConfiguration:
    """Original type: scan_configuration = { ... }"""

    rules: RawJson
    triage_ignored_syntactic_ids: List[str] = field(default_factory=lambda: [])
    triage_ignored_match_based_ids: List[str] = field(default_factory=lambda: [])
    fips_mode: bool = field(default_factory=lambda: False)

    @classmethod
    def from_json(cls, x: Any) -> 'ScanConfiguration':
        if isinstance(x, dict):
            return cls(
                rules=RawJson.from_json(x['rules']) if 'rules' in x else _atd_missing_json_field('ScanConfiguration', 'rules'),
                triage_ignored_syntactic_ids=_atd_read_list(_atd_read_string)(x['triage_ignored_syntactic_ids']) if 'triage_ignored_syntactic_ids' in x else [],
                triage_ignored_match_based_ids=_atd_read_list(_atd_read_string)(x['triage_ignored_match_based_ids']) if 'triage_ignored_match_based_ids' in x else [],
                fips_mode=_atd_read_bool(x['fips_mode']) if 'fips_mode' in x else False,
            )
        else:
            _atd_bad_json('ScanConfiguration', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['rules'] = (lambda x: x.to_json())(self.rules)
        res['triage_ignored_syntactic_ids'] = _atd_write_list(_atd_write_string)(self.triage_ignored_syntactic_ids)
        res['triage_ignored_match_based_ids'] = _atd_write_list(_atd_write_string)(self.triage_ignored_match_based_ids)
        res['fips_mode'] = _atd_write_bool(self.fips_mode)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ScanConfiguration':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Glob:
    """Original type: glob"""

    value: str

    @classmethod
    def from_json(cls, x: Any) -> 'Glob':
        return cls(_atd_read_string(x))

    def to_json(self) -> Any:
        return _atd_write_string(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Glob':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ProductIgnoredFiles:
    """Original type: product_ignored_files"""

    value: Dict[Product, List[Glob]]

    @classmethod
    def from_json(cls, x: Any) -> 'ProductIgnoredFiles':
        return cls(_atd_read_assoc_array_into_dict(Product.from_json, _atd_read_list(Glob.from_json))(x))

    def to_json(self) -> Any:
        return _atd_write_assoc_dict_to_array((lambda x: x.to_json()), _atd_write_list((lambda x: x.to_json())))(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'ProductIgnoredFiles':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class HistoricalConfiguration:
    """Original type: historical_configuration = { ... }"""

    enabled: bool
    lookback_days: Optional[int] = None

    @classmethod
    def from_json(cls, x: Any) -> 'HistoricalConfiguration':
        if isinstance(x, dict):
            return cls(
                enabled=_atd_read_bool(x['enabled']) if 'enabled' in x else _atd_missing_json_field('HistoricalConfiguration', 'enabled'),
                lookback_days=_atd_read_int(x['lookback_days']) if 'lookback_days' in x else None,
            )
        else:
            _atd_bad_json('HistoricalConfiguration', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['enabled'] = _atd_write_bool(self.enabled)
        if self.lookback_days is not None:
            res['lookback_days'] = _atd_write_int(self.lookback_days)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'HistoricalConfiguration':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class EngineConfiguration:
    """Original type: engine_configuration = { ... }"""

    autofix: bool = field(default_factory=lambda: False)
    deepsemgrep: bool = field(default_factory=lambda: False)
    dependency_query: bool = field(default_factory=lambda: False)
    path_to_transitivity: bool = field(default_factory=lambda: False)
    scan_all_deps_in_diff_scan: bool = field(default_factory=lambda: False)
    symbol_analysis: bool = field(default_factory=lambda: False)
    transitive_reachability_enabled: bool = field(default_factory=lambda: False)
    ignored_files: List[str] = field(default_factory=lambda: [])
    product_ignored_files: Optional[ProductIgnoredFiles] = None
    generic_slow_rollout: bool = field(default_factory=lambda: False)
    historical_config: Optional[HistoricalConfiguration] = None
    always_suppress_errors: bool = field(default_factory=lambda: False)

    @classmethod
    def from_json(cls, x: Any) -> 'EngineConfiguration':
        if isinstance(x, dict):
            return cls(
                autofix=_atd_read_bool(x['autofix']) if 'autofix' in x else False,
                deepsemgrep=_atd_read_bool(x['deepsemgrep']) if 'deepsemgrep' in x else False,
                dependency_query=_atd_read_bool(x['dependency_query']) if 'dependency_query' in x else False,
                path_to_transitivity=_atd_read_bool(x['path_to_transitivity']) if 'path_to_transitivity' in x else False,
                scan_all_deps_in_diff_scan=_atd_read_bool(x['scan_all_deps_in_diff_scan']) if 'scan_all_deps_in_diff_scan' in x else False,
                symbol_analysis=_atd_read_bool(x['symbol_analysis']) if 'symbol_analysis' in x else False,
                transitive_reachability_enabled=_atd_read_bool(x['transitive_reachability_enabled']) if 'transitive_reachability_enabled' in x else False,
                ignored_files=_atd_read_list(_atd_read_string)(x['ignored_files']) if 'ignored_files' in x else [],
                product_ignored_files=ProductIgnoredFiles.from_json(x['product_ignored_files']) if 'product_ignored_files' in x else None,
                generic_slow_rollout=_atd_read_bool(x['generic_slow_rollout']) if 'generic_slow_rollout' in x else False,
                historical_config=HistoricalConfiguration.from_json(x['historical_config']) if 'historical_config' in x else None,
                always_suppress_errors=_atd_read_bool(x['always_suppress_errors']) if 'always_suppress_errors' in x else False,
            )
        else:
            _atd_bad_json('EngineConfiguration', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['autofix'] = _atd_write_bool(self.autofix)
        res['deepsemgrep'] = _atd_write_bool(self.deepsemgrep)
        res['dependency_query'] = _atd_write_bool(self.dependency_query)
        res['path_to_transitivity'] = _atd_write_bool(self.path_to_transitivity)
        res['scan_all_deps_in_diff_scan'] = _atd_write_bool(self.scan_all_deps_in_diff_scan)
        res['symbol_analysis'] = _atd_write_bool(self.symbol_analysis)
        res['transitive_reachability_enabled'] = _atd_write_bool(self.transitive_reachability_enabled)
        res['ignored_files'] = _atd_write_list(_atd_write_string)(self.ignored_files)
        if self.product_ignored_files is not None:
            res['product_ignored_files'] = (lambda x: x.to_json())(self.product_ignored_files)
        res['generic_slow_rollout'] = _atd_write_bool(self.generic_slow_rollout)
        if self.historical_config is not None:
            res['historical_config'] = (lambda x: x.to_json())(self.historical_config)
        res['always_suppress_errors'] = _atd_write_bool(self.always_suppress_errors)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'EngineConfiguration':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScanResponse:
    """Original type: scan_response = { ... }"""

    info: ScanInfo
    config: ScanConfiguration
    engine_params: EngineConfiguration

    @classmethod
    def from_json(cls, x: Any) -> 'ScanResponse':
        if isinstance(x, dict):
            return cls(
                info=ScanInfo.from_json(x['info']) if 'info' in x else _atd_missing_json_field('ScanResponse', 'info'),
                config=ScanConfiguration.from_json(x['config']) if 'config' in x else _atd_missing_json_field('ScanResponse', 'config'),
                engine_params=EngineConfiguration.from_json(x['engine_params']) if 'engine_params' in x else _atd_missing_json_field('ScanResponse', 'engine_params'),
            )
        else:
            _atd_bad_json('ScanResponse', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['info'] = (lambda x: x.to_json())(self.info)
        res['config'] = (lambda x: x.to_json())(self.config)
        res['engine_params'] = (lambda x: x.to_json())(self.engine_params)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ScanResponse':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScanMetadata:
    """Original type: scan_metadata = { ... }"""

    cli_version: Version
    unique_id: Uuid
    requested_products: List[Product]
    dry_run: bool = field(default_factory=lambda: False)
    sms_scan_id: Optional[str] = None

    @classmethod
    def from_json(cls, x: Any) -> 'ScanMetadata':
        if isinstance(x, dict):
            return cls(
                cli_version=Version.from_json(x['cli_version']) if 'cli_version' in x else _atd_missing_json_field('ScanMetadata', 'cli_version'),
                unique_id=Uuid.from_json(x['unique_id']) if 'unique_id' in x else _atd_missing_json_field('ScanMetadata', 'unique_id'),
                requested_products=_atd_read_list(Product.from_json)(x['requested_products']) if 'requested_products' in x else _atd_missing_json_field('ScanMetadata', 'requested_products'),
                dry_run=_atd_read_bool(x['dry_run']) if 'dry_run' in x else False,
                sms_scan_id=_atd_read_string(x['sms_scan_id']) if 'sms_scan_id' in x else None,
            )
        else:
            _atd_bad_json('ScanMetadata', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['cli_version'] = (lambda x: x.to_json())(self.cli_version)
        res['unique_id'] = (lambda x: x.to_json())(self.unique_id)
        res['requested_products'] = _atd_write_list((lambda x: x.to_json()))(self.requested_products)
        res['dry_run'] = _atd_write_bool(self.dry_run)
        if self.sms_scan_id is not None:
            res['sms_scan_id'] = _atd_write_string(self.sms_scan_id)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ScanMetadata':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ProjectMetadata:
    """Original type: project_metadata = { ... }"""

    scan_environment: str
    repository: str
    repo_url: Optional[Uri]
    branch: Optional[str]
    commit: Optional[Sha1]
    commit_title: Optional[str]
    commit_author_email: Optional[str]
    commit_author_name: Optional[str]
    commit_author_username: Optional[str]
    commit_author_image_url: Optional[Uri]
    ci_job_url: Optional[Uri]
    on: str
    pull_request_author_username: Optional[str]
    pull_request_author_image_url: Optional[Uri]
    pull_request_id: Optional[str]
    pull_request_title: Optional[str]
    is_full_scan: bool
    repo_id: Optional[str] = None
    org_id: Optional[str] = None
    repo_display_name: Optional[str] = None
    commit_timestamp: Optional[Datetime] = None
    base_sha: Optional[Sha1] = None
    start_sha: Optional[Sha1] = None
    is_sca_scan: Optional[bool] = None
    is_code_scan: Optional[bool] = None
    is_secrets_scan: Optional[bool] = None
    project_id: Optional[str] = None

    @classmethod
    def from_json(cls, x: Any) -> 'ProjectMetadata':
        if isinstance(x, dict):
            return cls(
                scan_environment=_atd_read_string(x['scan_environment']) if 'scan_environment' in x else _atd_missing_json_field('ProjectMetadata', 'scan_environment'),
                repository=_atd_read_string(x['repository']) if 'repository' in x else _atd_missing_json_field('ProjectMetadata', 'repository'),
                repo_url=_atd_read_nullable(Uri.from_json)(x['repo_url']) if 'repo_url' in x else _atd_missing_json_field('ProjectMetadata', 'repo_url'),
                branch=_atd_read_nullable(_atd_read_string)(x['branch']) if 'branch' in x else _atd_missing_json_field('ProjectMetadata', 'branch'),
                commit=_atd_read_nullable(Sha1.from_json)(x['commit']) if 'commit' in x else _atd_missing_json_field('ProjectMetadata', 'commit'),
                commit_title=_atd_read_nullable(_atd_read_string)(x['commit_title']) if 'commit_title' in x else _atd_missing_json_field('ProjectMetadata', 'commit_title'),
                commit_author_email=_atd_read_nullable(_atd_read_string)(x['commit_author_email']) if 'commit_author_email' in x else _atd_missing_json_field('ProjectMetadata', 'commit_author_email'),
                commit_author_name=_atd_read_nullable(_atd_read_string)(x['commit_author_name']) if 'commit_author_name' in x else _atd_missing_json_field('ProjectMetadata', 'commit_author_name'),
                commit_author_username=_atd_read_nullable(_atd_read_string)(x['commit_author_username']) if 'commit_author_username' in x else _atd_missing_json_field('ProjectMetadata', 'commit_author_username'),
                commit_author_image_url=_atd_read_nullable(Uri.from_json)(x['commit_author_image_url']) if 'commit_author_image_url' in x else _atd_missing_json_field('ProjectMetadata', 'commit_author_image_url'),
                ci_job_url=_atd_read_nullable(Uri.from_json)(x['ci_job_url']) if 'ci_job_url' in x else _atd_missing_json_field('ProjectMetadata', 'ci_job_url'),
                on=_atd_read_string(x['on']) if 'on' in x else _atd_missing_json_field('ProjectMetadata', 'on'),
                pull_request_author_username=_atd_read_nullable(_atd_read_string)(x['pull_request_author_username']) if 'pull_request_author_username' in x else _atd_missing_json_field('ProjectMetadata', 'pull_request_author_username'),
                pull_request_author_image_url=_atd_read_nullable(Uri.from_json)(x['pull_request_author_image_url']) if 'pull_request_author_image_url' in x else _atd_missing_json_field('ProjectMetadata', 'pull_request_author_image_url'),
                pull_request_id=_atd_read_nullable(_atd_read_string)(x['pull_request_id']) if 'pull_request_id' in x else _atd_missing_json_field('ProjectMetadata', 'pull_request_id'),
                pull_request_title=_atd_read_nullable(_atd_read_string)(x['pull_request_title']) if 'pull_request_title' in x else _atd_missing_json_field('ProjectMetadata', 'pull_request_title'),
                is_full_scan=_atd_read_bool(x['is_full_scan']) if 'is_full_scan' in x else _atd_missing_json_field('ProjectMetadata', 'is_full_scan'),
                repo_id=_atd_read_string(x['repo_id']) if 'repo_id' in x else None,
                org_id=_atd_read_string(x['org_id']) if 'org_id' in x else None,
                repo_display_name=_atd_read_string(x['repo_display_name']) if 'repo_display_name' in x else None,
                commit_timestamp=Datetime.from_json(x['commit_timestamp']) if 'commit_timestamp' in x else None,
                base_sha=Sha1.from_json(x['base_sha']) if 'base_sha' in x else None,
                start_sha=Sha1.from_json(x['start_sha']) if 'start_sha' in x else None,
                is_sca_scan=_atd_read_bool(x['is_sca_scan']) if 'is_sca_scan' in x else None,
                is_code_scan=_atd_read_bool(x['is_code_scan']) if 'is_code_scan' in x else None,
                is_secrets_scan=_atd_read_bool(x['is_secrets_scan']) if 'is_secrets_scan' in x else None,
                project_id=_atd_read_string(x['project_id']) if 'project_id' in x else None,
            )
        else:
            _atd_bad_json('ProjectMetadata', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['scan_environment'] = _atd_write_string(self.scan_environment)
        res['repository'] = _atd_write_string(self.repository)
        res['repo_url'] = _atd_write_nullable((lambda x: x.to_json()))(self.repo_url)
        res['branch'] = _atd_write_nullable(_atd_write_string)(self.branch)
        res['commit'] = _atd_write_nullable((lambda x: x.to_json()))(self.commit)
        res['commit_title'] = _atd_write_nullable(_atd_write_string)(self.commit_title)
        res['commit_author_email'] = _atd_write_nullable(_atd_write_string)(self.commit_author_email)
        res['commit_author_name'] = _atd_write_nullable(_atd_write_string)(self.commit_author_name)
        res['commit_author_username'] = _atd_write_nullable(_atd_write_string)(self.commit_author_username)
        res['commit_author_image_url'] = _atd_write_nullable((lambda x: x.to_json()))(self.commit_author_image_url)
        res['ci_job_url'] = _atd_write_nullable((lambda x: x.to_json()))(self.ci_job_url)
        res['on'] = _atd_write_string(self.on)
        res['pull_request_author_username'] = _atd_write_nullable(_atd_write_string)(self.pull_request_author_username)
        res['pull_request_author_image_url'] = _atd_write_nullable((lambda x: x.to_json()))(self.pull_request_author_image_url)
        res['pull_request_id'] = _atd_write_nullable(_atd_write_string)(self.pull_request_id)
        res['pull_request_title'] = _atd_write_nullable(_atd_write_string)(self.pull_request_title)
        res['is_full_scan'] = _atd_write_bool(self.is_full_scan)
        if self.repo_id is not None:
            res['repo_id'] = _atd_write_string(self.repo_id)
        if self.org_id is not None:
            res['org_id'] = _atd_write_string(self.org_id)
        if self.repo_display_name is not None:
            res['repo_display_name'] = _atd_write_string(self.repo_display_name)
        if self.commit_timestamp is not None:
            res['commit_timestamp'] = (lambda x: x.to_json())(self.commit_timestamp)
        if self.base_sha is not None:
            res['base_sha'] = (lambda x: x.to_json())(self.base_sha)
        if self.start_sha is not None:
            res['start_sha'] = (lambda x: x.to_json())(self.start_sha)
        if self.is_sca_scan is not None:
            res['is_sca_scan'] = _atd_write_bool(self.is_sca_scan)
        if self.is_code_scan is not None:
            res['is_code_scan'] = _atd_write_bool(self.is_code_scan)
        if self.is_secrets_scan is not None:
            res['is_secrets_scan'] = _atd_write_bool(self.is_secrets_scan)
        if self.project_id is not None:
            res['project_id'] = _atd_write_string(self.project_id)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ProjectMetadata':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiConfigFromRepo:
    """Original type: ci_config_from_repo = { ... }"""

    version: Version = field(default_factory=lambda: Version('v1'))
    tags: Optional[List[Tag]] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CiConfigFromRepo':
        if isinstance(x, dict):
            return cls(
                version=Version.from_json(x['version']) if 'version' in x else Version('v1'),
                tags=_atd_read_list(Tag.from_json)(x['tags']) if 'tags' in x else None,
            )
        else:
            _atd_bad_json('CiConfigFromRepo', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['version'] = (lambda x: x.to_json())(self.version)
        if self.tags is not None:
            res['tags'] = _atd_write_list((lambda x: x.to_json()))(self.tags)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CiConfigFromRepo':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScanRequest:
    """Original type: scan_request = { ... }"""

    project_metadata: ProjectMetadata
    scan_metadata: ScanMetadata
    project_config: Optional[CiConfigFromRepo] = None

    @classmethod
    def from_json(cls, x: Any) -> 'ScanRequest':
        if isinstance(x, dict):
            return cls(
                project_metadata=ProjectMetadata.from_json(x['project_metadata']) if 'project_metadata' in x else _atd_missing_json_field('ScanRequest', 'project_metadata'),
                scan_metadata=ScanMetadata.from_json(x['scan_metadata']) if 'scan_metadata' in x else _atd_missing_json_field('ScanRequest', 'scan_metadata'),
                project_config=CiConfigFromRepo.from_json(x['project_config']) if 'project_config' in x else None,
            )
        else:
            _atd_bad_json('ScanRequest', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['project_metadata'] = (lambda x: x.to_json())(self.project_metadata)
        res['scan_metadata'] = (lambda x: x.to_json())(self.scan_metadata)
        if self.project_config is not None:
            res['project_config'] = (lambda x: x.to_json())(self.project_config)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ScanRequest':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiEnv:
    """Original type: ci_env"""

    value: Dict[str, str]

    @classmethod
    def from_json(cls, x: Any) -> 'CiEnv':
        return cls(_atd_read_assoc_object_into_dict(_atd_read_string)(x))

    def to_json(self) -> Any:
        return _atd_write_assoc_dict_to_object(_atd_write_string)(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'CiEnv':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiConfig:
    """Original type: ci_config = { ... }"""

    env: CiEnv
    enabled_products: List[Product]
    ignored_files: List[str]
    autofix: bool = field(default_factory=lambda: False)
    deepsemgrep: bool = field(default_factory=lambda: False)
    dependency_query: bool = field(default_factory=lambda: False)
    path_to_transitivity: bool = field(default_factory=lambda: False)
    scan_all_deps_in_diff_scan: bool = field(default_factory=lambda: False)
    symbol_analysis: bool = field(default_factory=lambda: False)
    transitive_reachability_enabled: bool = field(default_factory=lambda: False)

    @classmethod
    def from_json(cls, x: Any) -> 'CiConfig':
        if isinstance(x, dict):
            return cls(
                env=CiEnv.from_json(x['env']) if 'env' in x else _atd_missing_json_field('CiConfig', 'env'),
                enabled_products=_atd_read_list(Product.from_json)(x['enabled_products']) if 'enabled_products' in x else _atd_missing_json_field('CiConfig', 'enabled_products'),
                ignored_files=_atd_read_list(_atd_read_string)(x['ignored_files']) if 'ignored_files' in x else _atd_missing_json_field('CiConfig', 'ignored_files'),
                autofix=_atd_read_bool(x['autofix']) if 'autofix' in x else False,
                deepsemgrep=_atd_read_bool(x['deepsemgrep']) if 'deepsemgrep' in x else False,
                dependency_query=_atd_read_bool(x['dependency_query']) if 'dependency_query' in x else False,
                path_to_transitivity=_atd_read_bool(x['path_to_transitivity']) if 'path_to_transitivity' in x else False,
                scan_all_deps_in_diff_scan=_atd_read_bool(x['scan_all_deps_in_diff_scan']) if 'scan_all_deps_in_diff_scan' in x else False,
                symbol_analysis=_atd_read_bool(x['symbol_analysis']) if 'symbol_analysis' in x else False,
                transitive_reachability_enabled=_atd_read_bool(x['transitive_reachability_enabled']) if 'transitive_reachability_enabled' in x else False,
            )
        else:
            _atd_bad_json('CiConfig', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['env'] = (lambda x: x.to_json())(self.env)
        res['enabled_products'] = _atd_write_list((lambda x: x.to_json()))(self.enabled_products)
        res['ignored_files'] = _atd_write_list(_atd_write_string)(self.ignored_files)
        res['autofix'] = _atd_write_bool(self.autofix)
        res['deepsemgrep'] = _atd_write_bool(self.deepsemgrep)
        res['dependency_query'] = _atd_write_bool(self.dependency_query)
        res['path_to_transitivity'] = _atd_write_bool(self.path_to_transitivity)
        res['scan_all_deps_in_diff_scan'] = _atd_write_bool(self.scan_all_deps_in_diff_scan)
        res['symbol_analysis'] = _atd_write_bool(self.symbol_analysis)
        res['transitive_reachability_enabled'] = _atd_write_bool(self.transitive_reachability_enabled)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CiConfig':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Message:
    """Original type: action = [ ... | Message of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Message'

    def to_json(self) -> Any:
        return ['Message', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Delay:
    """Original type: action = [ ... | Delay of ... | ... ]"""

    value: float

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Delay'

    def to_json(self) -> Any:
        return ['Delay', _atd_write_float(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Exit:
    """Original type: action = [ ... | Exit of ... | ... ]"""

    value: int

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Exit'

    def to_json(self) -> Any:
        return ['Exit', _atd_write_int(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Action:
    """Original type: action = [ ... ]"""

    value: Union[Message, Delay, Exit]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'Action':
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'Message':
                return cls(Message(_atd_read_string(x[1])))
            if cons == 'Delay':
                return cls(Delay(_atd_read_float(x[1])))
            if cons == 'Exit':
                return cls(Exit(_atd_read_int(x[1])))
            _atd_bad_json('Action', x)
        _atd_bad_json('Action', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'Action':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiConfigFromCloud:
    """Original type: ci_config_from_cloud = { ... }"""

    repo_config: CiConfig
    org_config: Optional[CiConfig] = None
    dirs_config: Optional[List[Tuple[Fpath, CiConfig]]] = None
    actions: List[Action] = field(default_factory=lambda: [])

    @classmethod
    def from_json(cls, x: Any) -> 'CiConfigFromCloud':
        if isinstance(x, dict):
            return cls(
                repo_config=CiConfig.from_json(x['repo_config']) if 'repo_config' in x else _atd_missing_json_field('CiConfigFromCloud', 'repo_config'),
                org_config=CiConfig.from_json(x['org_config']) if 'org_config' in x else None,
                dirs_config=_atd_read_list((lambda x: (Fpath.from_json(x[0]), CiConfig.from_json(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x)))(x['dirs_config']) if 'dirs_config' in x else None,
                actions=_atd_read_list(Action.from_json)(x['actions']) if 'actions' in x else [],
            )
        else:
            _atd_bad_json('CiConfigFromCloud', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['repo_config'] = (lambda x: x.to_json())(self.repo_config)
        if self.org_config is not None:
            res['org_config'] = (lambda x: x.to_json())(self.org_config)
        if self.dirs_config is not None:
            res['dirs_config'] = _atd_write_list((lambda x: [(lambda x: x.to_json())(x[0]), (lambda x: x.to_json())(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x)))(self.dirs_config)
        res['actions'] = _atd_write_list((lambda x: x.to_json()))(self.actions)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CiConfigFromCloud':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ScanConfig:
    """Original type: scan_config = { ... }"""

    deployment_id: int
    deployment_name: str
    policy_names: List[str]
    rule_config: str
    autofix: bool = field(default_factory=lambda: False)
    deepsemgrep: bool = field(default_factory=lambda: False)
    dependency_query: bool = field(default_factory=lambda: False)
    path_to_transitivity: bool = field(default_factory=lambda: False)
    scan_all_deps_in_diff_scan: bool = field(default_factory=lambda: False)
    symbol_analysis: bool = field(default_factory=lambda: False)
    transitive_reachability_enabled: bool = field(default_factory=lambda: False)
    triage_ignored_syntactic_ids: List[str] = field(default_factory=lambda: [])
    triage_ignored_match_based_ids: List[str] = field(default_factory=lambda: [])
    ignored_files: List[str] = field(default_factory=lambda: [])
    enabled_products: Optional[List[Product]] = None
    actions: List[Action] = field(default_factory=lambda: [])
    ci_config_from_cloud: Optional[CiConfigFromCloud] = None

    @classmethod
    def from_json(cls, x: Any) -> 'ScanConfig':
        if isinstance(x, dict):
            return cls(
                deployment_id=_atd_read_int(x['deployment_id']) if 'deployment_id' in x else _atd_missing_json_field('ScanConfig', 'deployment_id'),
                deployment_name=_atd_read_string(x['deployment_name']) if 'deployment_name' in x else _atd_missing_json_field('ScanConfig', 'deployment_name'),
                policy_names=_atd_read_list(_atd_read_string)(x['policy_names']) if 'policy_names' in x else _atd_missing_json_field('ScanConfig', 'policy_names'),
                rule_config=_atd_read_string(x['rule_config']) if 'rule_config' in x else _atd_missing_json_field('ScanConfig', 'rule_config'),
                autofix=_atd_read_bool(x['autofix']) if 'autofix' in x else False,
                deepsemgrep=_atd_read_bool(x['deepsemgrep']) if 'deepsemgrep' in x else False,
                dependency_query=_atd_read_bool(x['dependency_query']) if 'dependency_query' in x else False,
                path_to_transitivity=_atd_read_bool(x['path_to_transitivity']) if 'path_to_transitivity' in x else False,
                scan_all_deps_in_diff_scan=_atd_read_bool(x['scan_all_deps_in_diff_scan']) if 'scan_all_deps_in_diff_scan' in x else False,
                symbol_analysis=_atd_read_bool(x['symbol_analysis']) if 'symbol_analysis' in x else False,
                transitive_reachability_enabled=_atd_read_bool(x['transitive_reachability_enabled']) if 'transitive_reachability_enabled' in x else False,
                triage_ignored_syntactic_ids=_atd_read_list(_atd_read_string)(x['triage_ignored_syntactic_ids']) if 'triage_ignored_syntactic_ids' in x else [],
                triage_ignored_match_based_ids=_atd_read_list(_atd_read_string)(x['triage_ignored_match_based_ids']) if 'triage_ignored_match_based_ids' in x else [],
                ignored_files=_atd_read_list(_atd_read_string)(x['ignored_files']) if 'ignored_files' in x else [],
                enabled_products=_atd_read_list(Product.from_json)(x['enabled_products']) if 'enabled_products' in x else None,
                actions=_atd_read_list(Action.from_json)(x['actions']) if 'actions' in x else [],
                ci_config_from_cloud=CiConfigFromCloud.from_json(x['ci_config_from_cloud']) if 'ci_config_from_cloud' in x else None,
            )
        else:
            _atd_bad_json('ScanConfig', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['deployment_id'] = _atd_write_int(self.deployment_id)
        res['deployment_name'] = _atd_write_string(self.deployment_name)
        res['policy_names'] = _atd_write_list(_atd_write_string)(self.policy_names)
        res['rule_config'] = _atd_write_string(self.rule_config)
        res['autofix'] = _atd_write_bool(self.autofix)
        res['deepsemgrep'] = _atd_write_bool(self.deepsemgrep)
        res['dependency_query'] = _atd_write_bool(self.dependency_query)
        res['path_to_transitivity'] = _atd_write_bool(self.path_to_transitivity)
        res['scan_all_deps_in_diff_scan'] = _atd_write_bool(self.scan_all_deps_in_diff_scan)
        res['symbol_analysis'] = _atd_write_bool(self.symbol_analysis)
        res['transitive_reachability_enabled'] = _atd_write_bool(self.transitive_reachability_enabled)
        res['triage_ignored_syntactic_ids'] = _atd_write_list(_atd_write_string)(self.triage_ignored_syntactic_ids)
        res['triage_ignored_match_based_ids'] = _atd_write_list(_atd_write_string)(self.triage_ignored_match_based_ids)
        res['ignored_files'] = _atd_write_list(_atd_write_string)(self.ignored_files)
        if self.enabled_products is not None:
            res['enabled_products'] = _atd_write_list((lambda x: x.to_json()))(self.enabled_products)
        res['actions'] = _atd_write_list((lambda x: x.to_json()))(self.actions)
        if self.ci_config_from_cloud is not None:
            res['ci_config_from_cloud'] = (lambda x: x.to_json())(self.ci_config_from_cloud)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ScanConfig':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class SarifFormat:
    """Original type: sarif_format = { ... }"""

    rules: Fpath
    is_pro: bool
    show_dataflow_traces: bool

    @classmethod
    def from_json(cls, x: Any) -> 'SarifFormat':
        if isinstance(x, dict):
            return cls(
                rules=Fpath.from_json(x['rules']) if 'rules' in x else _atd_missing_json_field('SarifFormat', 'rules'),
                is_pro=_atd_read_bool(x['is_pro']) if 'is_pro' in x else _atd_missing_json_field('SarifFormat', 'is_pro'),
                show_dataflow_traces=_atd_read_bool(x['show_dataflow_traces']) if 'show_dataflow_traces' in x else _atd_missing_json_field('SarifFormat', 'show_dataflow_traces'),
            )
        else:
            _atd_bad_json('SarifFormat', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['rules'] = (lambda x: x.to_json())(self.rules)
        res['is_pro'] = _atd_write_bool(self.is_pro)
        res['show_dataflow_traces'] = _atd_write_bool(self.show_dataflow_traces)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'SarifFormat':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class OSS_:
    """Original type: engine_kind = [ ... | OSS | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'OSS_'

    @staticmethod
    def to_json() -> Any:
        return 'OSS'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PRO_:
    """Original type: engine_kind = [ ... | PRO | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PRO_'

    @staticmethod
    def to_json() -> Any:
        return 'PRO'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class EngineKind:
    """Original type: engine_kind = [ ... ]"""

    value: Union[OSS_, PRO_]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'EngineKind':
        if isinstance(x, str):
            if x == 'OSS':
                return cls(OSS_())
            if x == 'PRO':
                return cls(PRO_())
            _atd_bad_json('EngineKind', x)
        _atd_bad_json('EngineKind', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'EngineKind':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RuleIdAndEngineKind:
    """Original type: rule_id_and_engine_kind"""

    value: Tuple[RuleId, EngineKind]

    @classmethod
    def from_json(cls, x: Any) -> 'RuleIdAndEngineKind':
        return cls((lambda x: (RuleId.from_json(x[0]), EngineKind.from_json(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x))(x))

    def to_json(self) -> Any:
        return (lambda x: [(lambda x: x.to_json())(x[0]), (lambda x: x.to_json())(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x))(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'RuleIdAndEngineKind':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ResolvedSubproject:
    """Original type: resolved_subproject = { ... }"""

    info: Subproject
    resolution_method: ResolutionMethod
    ecosystem: Ecosystem
    resolved_dependencies: Dict[DependencyChild, List[ResolvedDependency]]
    errors: List[ScaError]

    @classmethod
    def from_json(cls, x: Any) -> 'ResolvedSubproject':
        if isinstance(x, dict):
            return cls(
                info=Subproject.from_json(x['info']) if 'info' in x else _atd_missing_json_field('ResolvedSubproject', 'info'),
                resolution_method=ResolutionMethod.from_json(x['resolution_method']) if 'resolution_method' in x else _atd_missing_json_field('ResolvedSubproject', 'resolution_method'),
                ecosystem=Ecosystem.from_json(x['ecosystem']) if 'ecosystem' in x else _atd_missing_json_field('ResolvedSubproject', 'ecosystem'),
                resolved_dependencies=_atd_read_assoc_array_into_dict(DependencyChild.from_json, _atd_read_list(ResolvedDependency.from_json))(x['resolved_dependencies']) if 'resolved_dependencies' in x else _atd_missing_json_field('ResolvedSubproject', 'resolved_dependencies'),
                errors=_atd_read_list(ScaError.from_json)(x['errors']) if 'errors' in x else _atd_missing_json_field('ResolvedSubproject', 'errors'),
            )
        else:
            _atd_bad_json('ResolvedSubproject', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['info'] = (lambda x: x.to_json())(self.info)
        res['resolution_method'] = (lambda x: x.to_json())(self.resolution_method)
        res['ecosystem'] = (lambda x: x.to_json())(self.ecosystem)
        res['resolved_dependencies'] = _atd_write_assoc_dict_to_array((lambda x: x.to_json()), _atd_write_list((lambda x: x.to_json())))(self.resolved_dependencies)
        res['errors'] = _atd_write_list((lambda x: x.to_json()))(self.errors)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ResolvedSubproject':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ResolveDependenciesParams:
    """Original type: resolve_dependencies_params = { ... }"""

    dependency_sources: List[DependencySource]
    download_dependency_source_code: bool

    @classmethod
    def from_json(cls, x: Any) -> 'ResolveDependenciesParams':
        if isinstance(x, dict):
            return cls(
                dependency_sources=_atd_read_list(DependencySource.from_json)(x['dependency_sources']) if 'dependency_sources' in x else _atd_missing_json_field('ResolveDependenciesParams', 'dependency_sources'),
                download_dependency_source_code=_atd_read_bool(x['download_dependency_source_code']) if 'download_dependency_source_code' in x else _atd_missing_json_field('ResolveDependenciesParams', 'download_dependency_source_code'),
            )
        else:
            _atd_bad_json('ResolveDependenciesParams', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['dependency_sources'] = _atd_write_list((lambda x: x.to_json()))(self.dependency_sources)
        res['download_dependency_source_code'] = _atd_write_bool(self.download_dependency_source_code)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ResolveDependenciesParams':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ResolutionOk:
    """Original type: resolution_result = [ ... | ResolutionOk of ... | ... ]"""

    value: Tuple[List[ResolvedDependency], List[ResolutionErrorKind]]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ResolutionOk'

    def to_json(self) -> Any:
        return ['ResolutionOk', (lambda x: [_atd_write_list((lambda x: x.to_json()))(x[0]), _atd_write_list((lambda x: x.to_json()))(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ResolutionError:
    """Original type: resolution_result = [ ... | ResolutionError of ... | ... ]"""

    value: List[ResolutionErrorKind]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'ResolutionError'

    def to_json(self) -> Any:
        return ['ResolutionError', _atd_write_list((lambda x: x.to_json()))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ResolutionResult:
    """Original type: resolution_result = [ ... ]"""

    value: Union[ResolutionOk, ResolutionError]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'ResolutionResult':
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'ResolutionOk':
                return cls(ResolutionOk((lambda x: (_atd_read_list(ResolvedDependency.from_json)(x[0]), _atd_read_list(ResolutionErrorKind.from_json)(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x))(x[1])))
            if cons == 'ResolutionError':
                return cls(ResolutionError(_atd_read_list(ResolutionErrorKind.from_json)(x[1])))
            _atd_bad_json('ResolutionResult', x)
        _atd_bad_json('ResolutionResult', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'ResolutionResult':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class FileTime:
    """Original type: file_time = { ... }"""

    fpath: Fpath
    ftime: float

    @classmethod
    def from_json(cls, x: Any) -> 'FileTime':
        if isinstance(x, dict):
            return cls(
                fpath=Fpath.from_json(x['fpath']) if 'fpath' in x else _atd_missing_json_field('FileTime', 'fpath'),
                ftime=_atd_read_float(x['ftime']) if 'ftime' in x else _atd_missing_json_field('FileTime', 'ftime'),
            )
        else:
            _atd_bad_json('FileTime', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['fpath'] = (lambda x: x.to_json())(self.fpath)
        res['ftime'] = _atd_write_float(self.ftime)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'FileTime':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ParsingTime:
    """Original type: parsing_time = { ... }"""

    total_time: float
    per_file_time: SummaryStats
    very_slow_files: List[FileTime]

    @classmethod
    def from_json(cls, x: Any) -> 'ParsingTime':
        if isinstance(x, dict):
            return cls(
                total_time=_atd_read_float(x['total_time']) if 'total_time' in x else _atd_missing_json_field('ParsingTime', 'total_time'),
                per_file_time=SummaryStats.from_json(x['per_file_time']) if 'per_file_time' in x else _atd_missing_json_field('ParsingTime', 'per_file_time'),
                very_slow_files=_atd_read_list(FileTime.from_json)(x['very_slow_files']) if 'very_slow_files' in x else _atd_missing_json_field('ParsingTime', 'very_slow_files'),
            )
        else:
            _atd_bad_json('ParsingTime', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['total_time'] = _atd_write_float(self.total_time)
        res['per_file_time'] = (lambda x: x.to_json())(self.per_file_time)
        res['very_slow_files'] = _atd_write_list((lambda x: x.to_json()))(self.very_slow_files)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ParsingTime':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Profile:
    """Original type: profile = { ... }"""

    rules: List[RuleId]
    rules_parse_time: float
    profiling_times: Dict[str, float]
    targets: List[TargetTimes]
    total_bytes: int
    parsing_time: Optional[ParsingTime] = None
    max_memory_bytes: Optional[int] = None

    @classmethod
    def from_json(cls, x: Any) -> 'Profile':
        if isinstance(x, dict):
            return cls(
                rules=_atd_read_list(RuleId.from_json)(x['rules']) if 'rules' in x else _atd_missing_json_field('Profile', 'rules'),
                rules_parse_time=_atd_read_float(x['rules_parse_time']) if 'rules_parse_time' in x else _atd_missing_json_field('Profile', 'rules_parse_time'),
                profiling_times=_atd_read_assoc_object_into_dict(_atd_read_float)(x['profiling_times']) if 'profiling_times' in x else _atd_missing_json_field('Profile', 'profiling_times'),
                targets=_atd_read_list(TargetTimes.from_json)(x['targets']) if 'targets' in x else _atd_missing_json_field('Profile', 'targets'),
                total_bytes=_atd_read_int(x['total_bytes']) if 'total_bytes' in x else _atd_missing_json_field('Profile', 'total_bytes'),
                parsing_time=ParsingTime.from_json(x['parsing_time']) if 'parsing_time' in x else None,
                max_memory_bytes=_atd_read_int(x['max_memory_bytes']) if 'max_memory_bytes' in x else None,
            )
        else:
            _atd_bad_json('Profile', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['rules'] = _atd_write_list((lambda x: x.to_json()))(self.rules)
        res['rules_parse_time'] = _atd_write_float(self.rules_parse_time)
        res['profiling_times'] = _atd_write_assoc_dict_to_object(_atd_write_float)(self.profiling_times)
        res['targets'] = _atd_write_list((lambda x: x.to_json()))(self.targets)
        res['total_bytes'] = _atd_write_int(self.total_bytes)
        if self.parsing_time is not None:
            res['parsing_time'] = (lambda x: x.to_json())(self.parsing_time)
        if self.max_memory_bytes is not None:
            res['max_memory_bytes'] = _atd_write_int(self.max_memory_bytes)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Profile':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ParsingStats:
    """Original type: parsing_stats = { ... }"""

    targets_parsed: int
    num_targets: int
    bytes_parsed: int
    num_bytes: int

    @classmethod
    def from_json(cls, x: Any) -> 'ParsingStats':
        if isinstance(x, dict):
            return cls(
                targets_parsed=_atd_read_int(x['targets_parsed']) if 'targets_parsed' in x else _atd_missing_json_field('ParsingStats', 'targets_parsed'),
                num_targets=_atd_read_int(x['num_targets']) if 'num_targets' in x else _atd_missing_json_field('ParsingStats', 'num_targets'),
                bytes_parsed=_atd_read_int(x['bytes_parsed']) if 'bytes_parsed' in x else _atd_missing_json_field('ParsingStats', 'bytes_parsed'),
                num_bytes=_atd_read_int(x['num_bytes']) if 'num_bytes' in x else _atd_missing_json_field('ParsingStats', 'num_bytes'),
            )
        else:
            _atd_bad_json('ParsingStats', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['targets_parsed'] = _atd_write_int(self.targets_parsed)
        res['num_targets'] = _atd_write_int(self.num_targets)
        res['bytes_parsed'] = _atd_write_int(self.bytes_parsed)
        res['num_bytes'] = _atd_write_int(self.num_bytes)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ParsingStats':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class FindingHashes:
    """Original type: finding_hashes = { ... }"""

    start_line_hash: str
    end_line_hash: str
    code_hash: str
    pattern_hash: str

    @classmethod
    def from_json(cls, x: Any) -> 'FindingHashes':
        if isinstance(x, dict):
            return cls(
                start_line_hash=_atd_read_string(x['start_line_hash']) if 'start_line_hash' in x else _atd_missing_json_field('FindingHashes', 'start_line_hash'),
                end_line_hash=_atd_read_string(x['end_line_hash']) if 'end_line_hash' in x else _atd_missing_json_field('FindingHashes', 'end_line_hash'),
                code_hash=_atd_read_string(x['code_hash']) if 'code_hash' in x else _atd_missing_json_field('FindingHashes', 'code_hash'),
                pattern_hash=_atd_read_string(x['pattern_hash']) if 'pattern_hash' in x else _atd_missing_json_field('FindingHashes', 'pattern_hash'),
            )
        else:
            _atd_bad_json('FindingHashes', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['start_line_hash'] = _atd_write_string(self.start_line_hash)
        res['end_line_hash'] = _atd_write_string(self.end_line_hash)
        res['code_hash'] = _atd_write_string(self.code_hash)
        res['pattern_hash'] = _atd_write_string(self.pattern_hash)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'FindingHashes':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Finding:
    """Original type: finding = { ... }"""

    check_id: RuleId
    path: Fpath
    line: int
    column: int
    end_line: int
    end_column: int
    message: str
    severity: Any
    index: int
    commit_date: str
    syntactic_id: str
    metadata: RawJson
    is_blocking: bool
    match_based_id: Optional[str] = None
    hashes: Optional[FindingHashes] = None
    fixed_lines: Optional[List[str]] = None
    sca_info: Optional[ScaMatch] = None
    dataflow_trace: Optional[MatchDataflowTrace] = None
    validation_state: Optional[ValidationState] = None
    historical_info: Optional[HistoricalInfo] = None
    engine_kind: Optional[EngineOfFinding] = None

    @classmethod
    def from_json(cls, x: Any) -> 'Finding':
        if isinstance(x, dict):
            return cls(
                check_id=RuleId.from_json(x['check_id']) if 'check_id' in x else _atd_missing_json_field('Finding', 'check_id'),
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('Finding', 'path'),
                line=_atd_read_int(x['line']) if 'line' in x else _atd_missing_json_field('Finding', 'line'),
                column=_atd_read_int(x['column']) if 'column' in x else _atd_missing_json_field('Finding', 'column'),
                end_line=_atd_read_int(x['end_line']) if 'end_line' in x else _atd_missing_json_field('Finding', 'end_line'),
                end_column=_atd_read_int(x['end_column']) if 'end_column' in x else _atd_missing_json_field('Finding', 'end_column'),
                message=_atd_read_string(x['message']) if 'message' in x else _atd_missing_json_field('Finding', 'message'),
                severity=(lambda x: x)(x['severity']) if 'severity' in x else _atd_missing_json_field('Finding', 'severity'),
                index=_atd_read_int(x['index']) if 'index' in x else _atd_missing_json_field('Finding', 'index'),
                commit_date=_atd_read_string(x['commit_date']) if 'commit_date' in x else _atd_missing_json_field('Finding', 'commit_date'),
                syntactic_id=_atd_read_string(x['syntactic_id']) if 'syntactic_id' in x else _atd_missing_json_field('Finding', 'syntactic_id'),
                metadata=RawJson.from_json(x['metadata']) if 'metadata' in x else _atd_missing_json_field('Finding', 'metadata'),
                is_blocking=_atd_read_bool(x['is_blocking']) if 'is_blocking' in x else _atd_missing_json_field('Finding', 'is_blocking'),
                match_based_id=_atd_read_string(x['match_based_id']) if 'match_based_id' in x else None,
                hashes=FindingHashes.from_json(x['hashes']) if 'hashes' in x else None,
                fixed_lines=_atd_read_list(_atd_read_string)(x['fixed_lines']) if 'fixed_lines' in x else None,
                sca_info=ScaMatch.from_json(x['sca_info']) if 'sca_info' in x else None,
                dataflow_trace=MatchDataflowTrace.from_json(x['dataflow_trace']) if 'dataflow_trace' in x else None,
                validation_state=ValidationState.from_json(x['validation_state']) if 'validation_state' in x else None,
                historical_info=HistoricalInfo.from_json(x['historical_info']) if 'historical_info' in x else None,
                engine_kind=EngineOfFinding.from_json(x['engine_kind']) if 'engine_kind' in x else None,
            )
        else:
            _atd_bad_json('Finding', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['check_id'] = (lambda x: x.to_json())(self.check_id)
        res['path'] = (lambda x: x.to_json())(self.path)
        res['line'] = _atd_write_int(self.line)
        res['column'] = _atd_write_int(self.column)
        res['end_line'] = _atd_write_int(self.end_line)
        res['end_column'] = _atd_write_int(self.end_column)
        res['message'] = _atd_write_string(self.message)
        res['severity'] = (lambda x: x)(self.severity)
        res['index'] = _atd_write_int(self.index)
        res['commit_date'] = _atd_write_string(self.commit_date)
        res['syntactic_id'] = _atd_write_string(self.syntactic_id)
        res['metadata'] = (lambda x: x.to_json())(self.metadata)
        res['is_blocking'] = _atd_write_bool(self.is_blocking)
        if self.match_based_id is not None:
            res['match_based_id'] = _atd_write_string(self.match_based_id)
        if self.hashes is not None:
            res['hashes'] = (lambda x: x.to_json())(self.hashes)
        if self.fixed_lines is not None:
            res['fixed_lines'] = _atd_write_list(_atd_write_string)(self.fixed_lines)
        if self.sca_info is not None:
            res['sca_info'] = (lambda x: x.to_json())(self.sca_info)
        if self.dataflow_trace is not None:
            res['dataflow_trace'] = (lambda x: x.to_json())(self.dataflow_trace)
        if self.validation_state is not None:
            res['validation_state'] = (lambda x: x.to_json())(self.validation_state)
        if self.historical_info is not None:
            res['historical_info'] = (lambda x: x.to_json())(self.historical_info)
        if self.engine_kind is not None:
            res['engine_kind'] = (lambda x: x.to_json())(self.engine_kind)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Finding':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class ErrorSpan:
    """Original type: error_span = { ... }"""

    file: Fpath
    start: Position
    end: Position
    source_hash: Optional[str] = None
    config_start: Optional[Optional[Position]] = None
    config_end: Optional[Optional[Position]] = None
    config_path: Optional[Optional[List[str]]] = None
    context_start: Optional[Optional[Position]] = None
    context_end: Optional[Optional[Position]] = None

    @classmethod
    def from_json(cls, x: Any) -> 'ErrorSpan':
        if isinstance(x, dict):
            return cls(
                file=Fpath.from_json(x['file']) if 'file' in x else _atd_missing_json_field('ErrorSpan', 'file'),
                start=Position.from_json(x['start']) if 'start' in x else _atd_missing_json_field('ErrorSpan', 'start'),
                end=Position.from_json(x['end']) if 'end' in x else _atd_missing_json_field('ErrorSpan', 'end'),
                source_hash=_atd_read_string(x['source_hash']) if 'source_hash' in x else None,
                config_start=_atd_read_nullable(Position.from_json)(x['config_start']) if 'config_start' in x else None,
                config_end=_atd_read_nullable(Position.from_json)(x['config_end']) if 'config_end' in x else None,
                config_path=_atd_read_nullable(_atd_read_list(_atd_read_string))(x['config_path']) if 'config_path' in x else None,
                context_start=_atd_read_nullable(Position.from_json)(x['context_start']) if 'context_start' in x else None,
                context_end=_atd_read_nullable(Position.from_json)(x['context_end']) if 'context_end' in x else None,
            )
        else:
            _atd_bad_json('ErrorSpan', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['file'] = (lambda x: x.to_json())(self.file)
        res['start'] = (lambda x: x.to_json())(self.start)
        res['end'] = (lambda x: x.to_json())(self.end)
        if self.source_hash is not None:
            res['source_hash'] = _atd_write_string(self.source_hash)
        if self.config_start is not None:
            res['config_start'] = _atd_write_nullable((lambda x: x.to_json()))(self.config_start)
        if self.config_end is not None:
            res['config_end'] = _atd_write_nullable((lambda x: x.to_json()))(self.config_end)
        if self.config_path is not None:
            res['config_path'] = _atd_write_nullable(_atd_write_list(_atd_write_string))(self.config_path)
        if self.context_start is not None:
            res['context_start'] = _atd_write_nullable((lambda x: x.to_json()))(self.context_start)
        if self.context_end is not None:
            res['context_end'] = _atd_write_nullable((lambda x: x.to_json()))(self.context_end)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ErrorSpan':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Contributor:
    """Original type: contributor = { ... }"""

    commit_author_name: str
    commit_author_email: str

    @classmethod
    def from_json(cls, x: Any) -> 'Contributor':
        if isinstance(x, dict):
            return cls(
                commit_author_name=_atd_read_string(x['commit_author_name']) if 'commit_author_name' in x else _atd_missing_json_field('Contributor', 'commit_author_name'),
                commit_author_email=_atd_read_string(x['commit_author_email']) if 'commit_author_email' in x else _atd_missing_json_field('Contributor', 'commit_author_email'),
            )
        else:
            _atd_bad_json('Contributor', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['commit_author_name'] = _atd_write_string(self.commit_author_name)
        res['commit_author_email'] = _atd_write_string(self.commit_author_email)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Contributor':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Contribution:
    """Original type: contribution = { ... }"""

    commit_hash: str
    commit_timestamp: Datetime
    contributor: Contributor

    @classmethod
    def from_json(cls, x: Any) -> 'Contribution':
        if isinstance(x, dict):
            return cls(
                commit_hash=_atd_read_string(x['commit_hash']) if 'commit_hash' in x else _atd_missing_json_field('Contribution', 'commit_hash'),
                commit_timestamp=Datetime.from_json(x['commit_timestamp']) if 'commit_timestamp' in x else _atd_missing_json_field('Contribution', 'commit_timestamp'),
                contributor=Contributor.from_json(x['contributor']) if 'contributor' in x else _atd_missing_json_field('Contribution', 'contributor'),
            )
        else:
            _atd_bad_json('Contribution', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['commit_hash'] = _atd_write_string(self.commit_hash)
        res['commit_timestamp'] = (lambda x: x.to_json())(self.commit_timestamp)
        res['contributor'] = (lambda x: x.to_json())(self.contributor)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Contribution':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Contributions:
    """Original type: contributions"""

    value: List[Contribution]

    @classmethod
    def from_json(cls, x: Any) -> 'Contributions':
        return cls(_atd_read_list(Contribution.from_json)(x))

    def to_json(self) -> Any:
        return _atd_write_list((lambda x: x.to_json()))(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'Contributions':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CliError:
    """Original type: cli_error = { ... }"""

    code: int
    level: ErrorSeverity
    type_: ErrorType
    rule_id: Optional[RuleId] = None
    message: Optional[str] = None
    path: Optional[Fpath] = None
    long_msg: Optional[str] = None
    short_msg: Optional[str] = None
    spans: Optional[List[ErrorSpan]] = None
    help: Optional[str] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CliError':
        if isinstance(x, dict):
            return cls(
                code=_atd_read_int(x['code']) if 'code' in x else _atd_missing_json_field('CliError', 'code'),
                level=ErrorSeverity.from_json(x['level']) if 'level' in x else _atd_missing_json_field('CliError', 'level'),
                type_=ErrorType.from_json(x['type']) if 'type' in x else _atd_missing_json_field('CliError', 'type'),
                rule_id=RuleId.from_json(x['rule_id']) if 'rule_id' in x else None,
                message=_atd_read_string(x['message']) if 'message' in x else None,
                path=Fpath.from_json(x['path']) if 'path' in x else None,
                long_msg=_atd_read_string(x['long_msg']) if 'long_msg' in x else None,
                short_msg=_atd_read_string(x['short_msg']) if 'short_msg' in x else None,
                spans=_atd_read_list(ErrorSpan.from_json)(x['spans']) if 'spans' in x else None,
                help=_atd_read_string(x['help']) if 'help' in x else None,
            )
        else:
            _atd_bad_json('CliError', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['code'] = _atd_write_int(self.code)
        res['level'] = (lambda x: x.to_json())(self.level)
        res['type'] = (lambda x: x.to_json())(self.type_)
        if self.rule_id is not None:
            res['rule_id'] = (lambda x: x.to_json())(self.rule_id)
        if self.message is not None:
            res['message'] = _atd_write_string(self.message)
        if self.path is not None:
            res['path'] = (lambda x: x.to_json())(self.path)
        if self.long_msg is not None:
            res['long_msg'] = _atd_write_string(self.long_msg)
        if self.short_msg is not None:
            res['short_msg'] = _atd_write_string(self.short_msg)
        if self.spans is not None:
            res['spans'] = _atd_write_list((lambda x: x.to_json()))(self.spans)
        if self.help is not None:
            res['help'] = _atd_write_string(self.help)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CliError':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiScanDependencies:
    """Original type: ci_scan_dependencies"""

    value: Dict[str, List[FoundDependency]]

    @classmethod
    def from_json(cls, x: Any) -> 'CiScanDependencies':
        return cls(_atd_read_assoc_object_into_dict(_atd_read_list(FoundDependency.from_json))(x))

    def to_json(self) -> Any:
        return _atd_write_assoc_dict_to_object(_atd_write_list((lambda x: x.to_json())))(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'CiScanDependencies':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiScanResults:
    """Original type: ci_scan_results = { ... }"""

    findings: List[Finding]
    ignores: List[Finding]
    token: Optional[str]
    searched_paths: List[Fpath]
    renamed_paths: List[Fpath]
    rule_ids: List[RuleId]
    contributions: Optional[Contributions] = None
    dependencies: Optional[CiScanDependencies] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CiScanResults':
        if isinstance(x, dict):
            return cls(
                findings=_atd_read_list(Finding.from_json)(x['findings']) if 'findings' in x else _atd_missing_json_field('CiScanResults', 'findings'),
                ignores=_atd_read_list(Finding.from_json)(x['ignores']) if 'ignores' in x else _atd_missing_json_field('CiScanResults', 'ignores'),
                token=_atd_read_nullable(_atd_read_string)(x['token']) if 'token' in x else _atd_missing_json_field('CiScanResults', 'token'),
                searched_paths=_atd_read_list(Fpath.from_json)(x['searched_paths']) if 'searched_paths' in x else _atd_missing_json_field('CiScanResults', 'searched_paths'),
                renamed_paths=_atd_read_list(Fpath.from_json)(x['renamed_paths']) if 'renamed_paths' in x else _atd_missing_json_field('CiScanResults', 'renamed_paths'),
                rule_ids=_atd_read_list(RuleId.from_json)(x['rule_ids']) if 'rule_ids' in x else _atd_missing_json_field('CiScanResults', 'rule_ids'),
                contributions=Contributions.from_json(x['contributions']) if 'contributions' in x else None,
                dependencies=CiScanDependencies.from_json(x['dependencies']) if 'dependencies' in x else None,
            )
        else:
            _atd_bad_json('CiScanResults', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['findings'] = _atd_write_list((lambda x: x.to_json()))(self.findings)
        res['ignores'] = _atd_write_list((lambda x: x.to_json()))(self.ignores)
        res['token'] = _atd_write_nullable(_atd_write_string)(self.token)
        res['searched_paths'] = _atd_write_list((lambda x: x.to_json()))(self.searched_paths)
        res['renamed_paths'] = _atd_write_list((lambda x: x.to_json()))(self.renamed_paths)
        res['rule_ids'] = _atd_write_list((lambda x: x.to_json()))(self.rule_ids)
        if self.contributions is not None:
            res['contributions'] = (lambda x: x.to_json())(self.contributions)
        if self.dependencies is not None:
            res['dependencies'] = (lambda x: x.to_json())(self.dependencies)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CiScanResults':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiScanFailure:
    """Original type: ci_scan_failure = { ... }"""

    exit_code: int
    stderr: str

    @classmethod
    def from_json(cls, x: Any) -> 'CiScanFailure':
        if isinstance(x, dict):
            return cls(
                exit_code=_atd_read_int(x['exit_code']) if 'exit_code' in x else _atd_missing_json_field('CiScanFailure', 'exit_code'),
                stderr=_atd_read_string(x['stderr']) if 'stderr' in x else _atd_missing_json_field('CiScanFailure', 'stderr'),
            )
        else:
            _atd_bad_json('CiScanFailure', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['exit_code'] = _atd_write_int(self.exit_code)
        res['stderr'] = _atd_write_string(self.stderr)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CiScanFailure':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiScanCompleteStats:
    """Original type: ci_scan_complete_stats = { ... }"""

    findings: int
    errors: List[CliError]
    total_time: float
    unsupported_exts: Dict[str, int]
    lockfile_scan_info: Dict[str, int]
    parse_rate: Dict[str, ParsingStats]
    engine_requested: Optional[str] = None
    findings_by_product: Optional[Dict[str, int]] = None
    supply_chain_stats: Optional[SupplyChainStats] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CiScanCompleteStats':
        if isinstance(x, dict):
            return cls(
                findings=_atd_read_int(x['findings']) if 'findings' in x else _atd_missing_json_field('CiScanCompleteStats', 'findings'),
                errors=_atd_read_list(CliError.from_json)(x['errors']) if 'errors' in x else _atd_missing_json_field('CiScanCompleteStats', 'errors'),
                total_time=_atd_read_float(x['total_time']) if 'total_time' in x else _atd_missing_json_field('CiScanCompleteStats', 'total_time'),
                unsupported_exts=_atd_read_assoc_object_into_dict(_atd_read_int)(x['unsupported_exts']) if 'unsupported_exts' in x else _atd_missing_json_field('CiScanCompleteStats', 'unsupported_exts'),
                lockfile_scan_info=_atd_read_assoc_object_into_dict(_atd_read_int)(x['lockfile_scan_info']) if 'lockfile_scan_info' in x else _atd_missing_json_field('CiScanCompleteStats', 'lockfile_scan_info'),
                parse_rate=_atd_read_assoc_object_into_dict(ParsingStats.from_json)(x['parse_rate']) if 'parse_rate' in x else _atd_missing_json_field('CiScanCompleteStats', 'parse_rate'),
                engine_requested=_atd_read_string(x['engine_requested']) if 'engine_requested' in x else None,
                findings_by_product=_atd_read_assoc_object_into_dict(_atd_read_int)(x['findings_by_product']) if 'findings_by_product' in x else None,
                supply_chain_stats=SupplyChainStats.from_json(x['supply_chain_stats']) if 'supply_chain_stats' in x else None,
            )
        else:
            _atd_bad_json('CiScanCompleteStats', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['findings'] = _atd_write_int(self.findings)
        res['errors'] = _atd_write_list((lambda x: x.to_json()))(self.errors)
        res['total_time'] = _atd_write_float(self.total_time)
        res['unsupported_exts'] = _atd_write_assoc_dict_to_object(_atd_write_int)(self.unsupported_exts)
        res['lockfile_scan_info'] = _atd_write_assoc_dict_to_object(_atd_write_int)(self.lockfile_scan_info)
        res['parse_rate'] = _atd_write_assoc_dict_to_object((lambda x: x.to_json()))(self.parse_rate)
        if self.engine_requested is not None:
            res['engine_requested'] = _atd_write_string(self.engine_requested)
        if self.findings_by_product is not None:
            res['findings_by_product'] = _atd_write_assoc_dict_to_object(_atd_write_int)(self.findings_by_product)
        if self.supply_chain_stats is not None:
            res['supply_chain_stats'] = (lambda x: x.to_json())(self.supply_chain_stats)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CiScanCompleteStats':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiScanComplete:
    """Original type: ci_scan_complete = { ... }"""

    exit_code: int
    stats: CiScanCompleteStats
    dependencies: Optional[CiScanDependencies] = None
    dependency_parser_errors: Optional[List[DependencyParserError]] = None
    task_id: Optional[str] = None
    final_attempt: Optional[bool] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CiScanComplete':
        if isinstance(x, dict):
            return cls(
                exit_code=_atd_read_int(x['exit_code']) if 'exit_code' in x else _atd_missing_json_field('CiScanComplete', 'exit_code'),
                stats=CiScanCompleteStats.from_json(x['stats']) if 'stats' in x else _atd_missing_json_field('CiScanComplete', 'stats'),
                dependencies=CiScanDependencies.from_json(x['dependencies']) if 'dependencies' in x else None,
                dependency_parser_errors=_atd_read_list(DependencyParserError.from_json)(x['dependency_parser_errors']) if 'dependency_parser_errors' in x else None,
                task_id=_atd_read_string(x['task_id']) if 'task_id' in x else None,
                final_attempt=_atd_read_bool(x['final_attempt']) if 'final_attempt' in x else None,
            )
        else:
            _atd_bad_json('CiScanComplete', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['exit_code'] = _atd_write_int(self.exit_code)
        res['stats'] = (lambda x: x.to_json())(self.stats)
        if self.dependencies is not None:
            res['dependencies'] = (lambda x: x.to_json())(self.dependencies)
        if self.dependency_parser_errors is not None:
            res['dependency_parser_errors'] = _atd_write_list((lambda x: x.to_json()))(self.dependency_parser_errors)
        if self.task_id is not None:
            res['task_id'] = _atd_write_string(self.task_id)
        if self.final_attempt is not None:
            res['final_attempt'] = _atd_write_bool(self.final_attempt)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CiScanComplete':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PartialScanOk:
    """Original type: partial_scan_result = [ ... | PartialScanOk of ... | ... ]"""

    value: Tuple[CiScanResults, CiScanComplete]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PartialScanOk'

    def to_json(self) -> Any:
        return ['PartialScanOk', (lambda x: [(lambda x: x.to_json())(x[0]), (lambda x: x.to_json())(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PartialScanError:
    """Original type: partial_scan_result = [ ... | PartialScanError of ... | ... ]"""

    value: CiScanFailure

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'PartialScanError'

    def to_json(self) -> Any:
        return ['PartialScanError', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class PartialScanResult:
    """Original type: partial_scan_result = [ ... ]"""

    value: Union[PartialScanOk, PartialScanError]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'PartialScanResult':
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'PartialScanOk':
                return cls(PartialScanOk((lambda x: (CiScanResults.from_json(x[0]), CiScanComplete.from_json(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x))(x[1])))
            if cons == 'PartialScanError':
                return cls(PartialScanError(CiScanFailure.from_json(x[1])))
            _atd_bad_json('PartialScanResult', x)
        _atd_bad_json('PartialScanResult', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'PartialScanResult':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Text:
    """Original type: output_format = [ ... | Text | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Text'

    @staticmethod
    def to_json() -> Any:
        return 'Text'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Json:
    """Original type: output_format = [ ... | Json | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Json'

    @staticmethod
    def to_json() -> Any:
        return 'Json'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Emacs:
    """Original type: output_format = [ ... | Emacs | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Emacs'

    @staticmethod
    def to_json() -> Any:
        return 'Emacs'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Vim:
    """Original type: output_format = [ ... | Vim | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Vim'

    @staticmethod
    def to_json() -> Any:
        return 'Vim'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Sarif:
    """Original type: output_format = [ ... | Sarif | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Sarif'

    @staticmethod
    def to_json() -> Any:
        return 'Sarif'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class GitlabSast:
    """Original type: output_format = [ ... | Gitlab_sast | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'GitlabSast'

    @staticmethod
    def to_json() -> Any:
        return 'Gitlab_sast'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class GitlabSecrets:
    """Original type: output_format = [ ... | Gitlab_secrets | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'GitlabSecrets'

    @staticmethod
    def to_json() -> Any:
        return 'Gitlab_secrets'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class JunitXml:
    """Original type: output_format = [ ... | Junit_xml | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'JunitXml'

    @staticmethod
    def to_json() -> Any:
        return 'Junit_xml'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class FilesWithMatches:
    """Original type: output_format = [ ... | Files_with_matches | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'FilesWithMatches'

    @staticmethod
    def to_json() -> Any:
        return 'Files_with_matches'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Incremental:
    """Original type: output_format = [ ... | Incremental | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'Incremental'

    @staticmethod
    def to_json() -> Any:
        return 'Incremental'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class OutputFormat:
    """Original type: output_format = [ ... ]"""

    value: Union[Text, Json, Emacs, Vim, Sarif, GitlabSast, GitlabSecrets, JunitXml, FilesWithMatches, Incremental]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'OutputFormat':
        if isinstance(x, str):
            if x == 'Text':
                return cls(Text())
            if x == 'Json':
                return cls(Json())
            if x == 'Emacs':
                return cls(Emacs())
            if x == 'Vim':
                return cls(Vim())
            if x == 'Sarif':
                return cls(Sarif())
            if x == 'Gitlab_sast':
                return cls(GitlabSast())
            if x == 'Gitlab_secrets':
                return cls(GitlabSecrets())
            if x == 'Junit_xml':
                return cls(JunitXml())
            if x == 'Files_with_matches':
                return cls(FilesWithMatches())
            if x == 'Incremental':
                return cls(Incremental())
            _atd_bad_json('OutputFormat', x)
        _atd_bad_json('OutputFormat', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'OutputFormat':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class MatchBasedId:
    """Original type: match_based_id"""

    value: str

    @classmethod
    def from_json(cls, x: Any) -> 'MatchBasedId':
        return cls(_atd_read_string(x))

    def to_json(self) -> Any:
        return _atd_write_string(self.value)

    @classmethod
    def from_json_string(cls, x: str) -> 'MatchBasedId':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class HasFeatures:
    """Original type: has_features = { ... }"""

    has_autofix: bool = field(default_factory=lambda: False)
    has_deepsemgrep: bool = field(default_factory=lambda: False)
    has_triage_via_comment: bool = field(default_factory=lambda: False)
    has_dependency_query: bool = field(default_factory=lambda: False)

    @classmethod
    def from_json(cls, x: Any) -> 'HasFeatures':
        if isinstance(x, dict):
            return cls(
                has_autofix=_atd_read_bool(x['has_autofix']) if 'has_autofix' in x else False,
                has_deepsemgrep=_atd_read_bool(x['has_deepsemgrep']) if 'has_deepsemgrep' in x else False,
                has_triage_via_comment=_atd_read_bool(x['has_triage_via_comment']) if 'has_triage_via_comment' in x else False,
                has_dependency_query=_atd_read_bool(x['has_dependency_query']) if 'has_dependency_query' in x else False,
            )
        else:
            _atd_bad_json('HasFeatures', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['has_autofix'] = _atd_write_bool(self.has_autofix)
        res['has_deepsemgrep'] = _atd_write_bool(self.has_deepsemgrep)
        res['has_triage_via_comment'] = _atd_write_bool(self.has_triage_via_comment)
        res['has_dependency_query'] = _atd_write_bool(self.has_dependency_query)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'HasFeatures':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ApplyFixesReturn:
    """Original type: apply_fixes_return = { ... }"""

    modified_file_count: int
    fixed_lines: List[Tuple[int, List[str]]]

    @classmethod
    def from_json(cls, x: Any) -> 'ApplyFixesReturn':
        if isinstance(x, dict):
            return cls(
                modified_file_count=_atd_read_int(x['modified_file_count']) if 'modified_file_count' in x else _atd_missing_json_field('ApplyFixesReturn', 'modified_file_count'),
                fixed_lines=_atd_read_list((lambda x: (_atd_read_int(x[0]), _atd_read_list(_atd_read_string)(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x)))(x['fixed_lines']) if 'fixed_lines' in x else _atd_missing_json_field('ApplyFixesReturn', 'fixed_lines'),
            )
        else:
            _atd_bad_json('ApplyFixesReturn', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['modified_file_count'] = _atd_write_int(self.modified_file_count)
        res['fixed_lines'] = _atd_write_list((lambda x: [_atd_write_int(x[0]), _atd_write_list(_atd_write_string)(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x)))(self.fixed_lines)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ApplyFixesReturn':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetError:
    """Original type: function_return = [ ... | RetError of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetError'

    def to_json(self) -> Any:
        return ['RetError', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetApplyFixes:
    """Original type: function_return = [ ... | RetApplyFixes of ... | ... ]"""

    value: ApplyFixesReturn

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetApplyFixes'

    def to_json(self) -> Any:
        return ['RetApplyFixes', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetContributions:
    """Original type: function_return = [ ... | RetContributions of ... | ... ]"""

    value: Contributions

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetContributions'

    def to_json(self) -> Any:
        return ['RetContributions', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetFormatter:
    """Original type: function_return = [ ... | RetFormatter of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetFormatter'

    def to_json(self) -> Any:
        return ['RetFormatter', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetSarifFormat:
    """Original type: function_return = [ ... | RetSarifFormat of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetSarifFormat'

    def to_json(self) -> Any:
        return ['RetSarifFormat', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetValidate:
    """Original type: function_return = [ ... | RetValidate of ... | ... ]"""

    value: bool

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetValidate'

    def to_json(self) -> Any:
        return ['RetValidate', _atd_write_bool(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetResolveDependencies:
    """Original type: function_return = [ ... | RetResolveDependencies of ... | ... ]"""

    value: List[Tuple[DependencySource, ResolutionResult]]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetResolveDependencies'

    def to_json(self) -> Any:
        return ['RetResolveDependencies', _atd_write_list((lambda x: [(lambda x: x.to_json())(x[0]), (lambda x: x.to_json())(x[1])] if isinstance(x, tuple) and len(x) == 2 else _atd_bad_python('tuple of length 2', x)))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetUploadSymbolAnalysis:
    """Original type: function_return = [ ... | RetUploadSymbolAnalysis of ... | ... ]"""

    value: str

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetUploadSymbolAnalysis'

    def to_json(self) -> Any:
        return ['RetUploadSymbolAnalysis', _atd_write_string(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetDumpRulePartitions:
    """Original type: function_return = [ ... | RetDumpRulePartitions of ... | ... ]"""

    value: bool

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetDumpRulePartitions'

    def to_json(self) -> Any:
        return ['RetDumpRulePartitions', _atd_write_bool(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetTransitiveReachabilityFilter:
    """Original type: function_return = [ ... | RetTransitiveReachabilityFilter of ... | ... ]"""

    value: List[TransitiveFinding]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetTransitiveReachabilityFilter'

    def to_json(self) -> Any:
        return ['RetTransitiveReachabilityFilter', _atd_write_list((lambda x: x.to_json()))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetGetTargets:
    """Original type: function_return = [ ... | RetGetTargets of ... | ... ]"""

    value: TargetDiscoveryResult

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetGetTargets'

    def to_json(self) -> Any:
        return ['RetGetTargets', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class RetMatchSubprojects:
    """Original type: function_return = [ ... | RetMatchSubprojects of ... | ... ]"""

    value: List[Subproject]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'RetMatchSubprojects'

    def to_json(self) -> Any:
        return ['RetMatchSubprojects', _atd_write_list((lambda x: x.to_json()))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class FunctionReturn:
    """Original type: function_return = [ ... ]"""

    value: Union[RetError, RetApplyFixes, RetContributions, RetFormatter, RetSarifFormat, RetValidate, RetResolveDependencies, RetUploadSymbolAnalysis, RetDumpRulePartitions, RetTransitiveReachabilityFilter, RetGetTargets, RetMatchSubprojects]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'FunctionReturn':
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'RetError':
                return cls(RetError(_atd_read_string(x[1])))
            if cons == 'RetApplyFixes':
                return cls(RetApplyFixes(ApplyFixesReturn.from_json(x[1])))
            if cons == 'RetContributions':
                return cls(RetContributions(Contributions.from_json(x[1])))
            if cons == 'RetFormatter':
                return cls(RetFormatter(_atd_read_string(x[1])))
            if cons == 'RetSarifFormat':
                return cls(RetSarifFormat(_atd_read_string(x[1])))
            if cons == 'RetValidate':
                return cls(RetValidate(_atd_read_bool(x[1])))
            if cons == 'RetResolveDependencies':
                return cls(RetResolveDependencies(_atd_read_list((lambda x: (DependencySource.from_json(x[0]), ResolutionResult.from_json(x[1])) if isinstance(x, list) and len(x) == 2 else _atd_bad_json('array of length 2', x)))(x[1])))
            if cons == 'RetUploadSymbolAnalysis':
                return cls(RetUploadSymbolAnalysis(_atd_read_string(x[1])))
            if cons == 'RetDumpRulePartitions':
                return cls(RetDumpRulePartitions(_atd_read_bool(x[1])))
            if cons == 'RetTransitiveReachabilityFilter':
                return cls(RetTransitiveReachabilityFilter(_atd_read_list(TransitiveFinding.from_json)(x[1])))
            if cons == 'RetGetTargets':
                return cls(RetGetTargets(TargetDiscoveryResult.from_json(x[1])))
            if cons == 'RetMatchSubprojects':
                return cls(RetMatchSubprojects(_atd_read_list(Subproject.from_json)(x[1])))
            _atd_bad_json('FunctionReturn', x)
        _atd_bad_json('FunctionReturn', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'FunctionReturn':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class FormatContext:
    """Original type: format_context = { ... }"""

    is_ci_invocation: bool
    is_logged_in: bool
    is_using_registry: bool

    @classmethod
    def from_json(cls, x: Any) -> 'FormatContext':
        if isinstance(x, dict):
            return cls(
                is_ci_invocation=_atd_read_bool(x['is_ci_invocation']) if 'is_ci_invocation' in x else _atd_missing_json_field('FormatContext', 'is_ci_invocation'),
                is_logged_in=_atd_read_bool(x['is_logged_in']) if 'is_logged_in' in x else _atd_missing_json_field('FormatContext', 'is_logged_in'),
                is_using_registry=_atd_read_bool(x['is_using_registry']) if 'is_using_registry' in x else _atd_missing_json_field('FormatContext', 'is_using_registry'),
            )
        else:
            _atd_bad_json('FormatContext', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['is_ci_invocation'] = _atd_write_bool(self.is_ci_invocation)
        res['is_logged_in'] = _atd_write_bool(self.is_logged_in)
        res['is_using_registry'] = _atd_write_bool(self.is_using_registry)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'FormatContext':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class Edit:
    """Original type: edit = { ... }"""

    path: Fpath
    start_offset: int
    end_offset: int
    replacement_text: str

    @classmethod
    def from_json(cls, x: Any) -> 'Edit':
        if isinstance(x, dict):
            return cls(
                path=Fpath.from_json(x['path']) if 'path' in x else _atd_missing_json_field('Edit', 'path'),
                start_offset=_atd_read_int(x['start_offset']) if 'start_offset' in x else _atd_missing_json_field('Edit', 'start_offset'),
                end_offset=_atd_read_int(x['end_offset']) if 'end_offset' in x else _atd_missing_json_field('Edit', 'end_offset'),
                replacement_text=_atd_read_string(x['replacement_text']) if 'replacement_text' in x else _atd_missing_json_field('Edit', 'replacement_text'),
            )
        else:
            _atd_bad_json('Edit', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['path'] = (lambda x: x.to_json())(self.path)
        res['start_offset'] = _atd_write_int(self.start_offset)
        res['end_offset'] = _atd_write_int(self.end_offset)
        res['replacement_text'] = _atd_write_string(self.replacement_text)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Edit':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DumpRulePartitionsParams:
    """Original type: dump_rule_partitions_params = { ... }"""

    rules: RawJson
    n_partitions: int
    output_dir: Fpath
    strategy: Optional[str] = None

    @classmethod
    def from_json(cls, x: Any) -> 'DumpRulePartitionsParams':
        if isinstance(x, dict):
            return cls(
                rules=RawJson.from_json(x['rules']) if 'rules' in x else _atd_missing_json_field('DumpRulePartitionsParams', 'rules'),
                n_partitions=_atd_read_int(x['n_partitions']) if 'n_partitions' in x else _atd_missing_json_field('DumpRulePartitionsParams', 'n_partitions'),
                output_dir=Fpath.from_json(x['output_dir']) if 'output_dir' in x else _atd_missing_json_field('DumpRulePartitionsParams', 'output_dir'),
                strategy=_atd_read_string(x['strategy']) if 'strategy' in x else None,
            )
        else:
            _atd_bad_json('DumpRulePartitionsParams', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['rules'] = (lambda x: x.to_json())(self.rules)
        res['n_partitions'] = _atd_write_int(self.n_partitions)
        res['output_dir'] = (lambda x: x.to_json())(self.output_dir)
        if self.strategy is not None:
            res['strategy'] = _atd_write_string(self.strategy)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'DumpRulePartitionsParams':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CliOutputSubprojectInfo:
    """Original type: cli_output_subproject_info = { ... }"""

    dependency_sources: List[Fpath]
    resolved: bool
    unresolved_reason: Optional[UnresolvedReason] = None
    resolved_stats: Optional[DependencyResolutionStats] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CliOutputSubprojectInfo':
        if isinstance(x, dict):
            return cls(
                dependency_sources=_atd_read_list(Fpath.from_json)(x['dependency_sources']) if 'dependency_sources' in x else _atd_missing_json_field('CliOutputSubprojectInfo', 'dependency_sources'),
                resolved=_atd_read_bool(x['resolved']) if 'resolved' in x else _atd_missing_json_field('CliOutputSubprojectInfo', 'resolved'),
                unresolved_reason=UnresolvedReason.from_json(x['unresolved_reason']) if 'unresolved_reason' in x else None,
                resolved_stats=DependencyResolutionStats.from_json(x['resolved_stats']) if 'resolved_stats' in x else None,
            )
        else:
            _atd_bad_json('CliOutputSubprojectInfo', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['dependency_sources'] = _atd_write_list((lambda x: x.to_json()))(self.dependency_sources)
        res['resolved'] = _atd_write_bool(self.resolved)
        if self.unresolved_reason is not None:
            res['unresolved_reason'] = (lambda x: x.to_json())(self.unresolved_reason)
        if self.resolved_stats is not None:
            res['resolved_stats'] = (lambda x: x.to_json())(self.resolved_stats)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CliOutputSubprojectInfo':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CliOutput:
    """Original type: cli_output = { ... }"""

    results: List[CliMatch]
    errors: List[CliError]
    paths: ScannedAndSkipped
    version: Optional[Version] = None
    time: Optional[Profile] = None
    explanations: Optional[List[MatchingExplanation]] = None
    rules_by_engine: Optional[List[RuleIdAndEngineKind]] = None
    engine_requested: Optional[EngineKind] = None
    interfile_languages_used: Optional[List[str]] = None
    skipped_rules: List[SkippedRule] = field(default_factory=lambda: [])
    subprojects: Optional[List[CliOutputSubprojectInfo]] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CliOutput':
        if isinstance(x, dict):
            return cls(
                results=_atd_read_list(CliMatch.from_json)(x['results']) if 'results' in x else _atd_missing_json_field('CliOutput', 'results'),
                errors=_atd_read_list(CliError.from_json)(x['errors']) if 'errors' in x else _atd_missing_json_field('CliOutput', 'errors'),
                paths=ScannedAndSkipped.from_json(x['paths']) if 'paths' in x else _atd_missing_json_field('CliOutput', 'paths'),
                version=Version.from_json(x['version']) if 'version' in x else None,
                time=Profile.from_json(x['time']) if 'time' in x else None,
                explanations=_atd_read_list(MatchingExplanation.from_json)(x['explanations']) if 'explanations' in x else None,
                rules_by_engine=_atd_read_list(RuleIdAndEngineKind.from_json)(x['rules_by_engine']) if 'rules_by_engine' in x else None,
                engine_requested=EngineKind.from_json(x['engine_requested']) if 'engine_requested' in x else None,
                interfile_languages_used=_atd_read_list(_atd_read_string)(x['interfile_languages_used']) if 'interfile_languages_used' in x else None,
                skipped_rules=_atd_read_list(SkippedRule.from_json)(x['skipped_rules']) if 'skipped_rules' in x else [],
                subprojects=_atd_read_list(CliOutputSubprojectInfo.from_json)(x['subprojects']) if 'subprojects' in x else None,
            )
        else:
            _atd_bad_json('CliOutput', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['results'] = _atd_write_list((lambda x: x.to_json()))(self.results)
        res['errors'] = _atd_write_list((lambda x: x.to_json()))(self.errors)
        res['paths'] = (lambda x: x.to_json())(self.paths)
        if self.version is not None:
            res['version'] = (lambda x: x.to_json())(self.version)
        if self.time is not None:
            res['time'] = (lambda x: x.to_json())(self.time)
        if self.explanations is not None:
            res['explanations'] = _atd_write_list((lambda x: x.to_json()))(self.explanations)
        if self.rules_by_engine is not None:
            res['rules_by_engine'] = _atd_write_list((lambda x: x.to_json()))(self.rules_by_engine)
        if self.engine_requested is not None:
            res['engine_requested'] = (lambda x: x.to_json())(self.engine_requested)
        if self.interfile_languages_used is not None:
            res['interfile_languages_used'] = _atd_write_list(_atd_write_string)(self.interfile_languages_used)
        res['skipped_rules'] = _atd_write_list((lambda x: x.to_json()))(self.skipped_rules)
        if self.subprojects is not None:
            res['subprojects'] = _atd_write_list((lambda x: x.to_json()))(self.subprojects)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CliOutput':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class ApplyFixesParams:
    """Original type: apply_fixes_params = { ... }"""

    dryrun: bool
    edits: List[Edit]

    @classmethod
    def from_json(cls, x: Any) -> 'ApplyFixesParams':
        if isinstance(x, dict):
            return cls(
                dryrun=_atd_read_bool(x['dryrun']) if 'dryrun' in x else _atd_missing_json_field('ApplyFixesParams', 'dryrun'),
                edits=_atd_read_list(Edit.from_json)(x['edits']) if 'edits' in x else _atd_missing_json_field('ApplyFixesParams', 'edits'),
            )
        else:
            _atd_bad_json('ApplyFixesParams', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['dryrun'] = _atd_write_bool(self.dryrun)
        res['edits'] = _atd_write_list((lambda x: x.to_json()))(self.edits)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'ApplyFixesParams':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CallContributions:
    """Original type: function_call = [ ... | CallContributions | ... ]"""

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CallContributions'

    @staticmethod
    def to_json() -> Any:
        return 'CallContributions'

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CallApplyFixes:
    """Original type: function_call = [ ... | CallApplyFixes of ... | ... ]"""

    value: ApplyFixesParams

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CallApplyFixes'

    def to_json(self) -> Any:
        return ['CallApplyFixes', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CallFormatter:
    """Original type: function_call = [ ... | CallFormatter of ... | ... ]"""

    value: Tuple[OutputFormat, FormatContext, CliOutput]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CallFormatter'

    def to_json(self) -> Any:
        return ['CallFormatter', (lambda x: [(lambda x: x.to_json())(x[0]), (lambda x: x.to_json())(x[1]), (lambda x: x.to_json())(x[2])] if isinstance(x, tuple) and len(x) == 3 else _atd_bad_python('tuple of length 3', x))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CallSarifFormat:
    """Original type: function_call = [ ... | CallSarifFormat of ... | ... ]"""

    value: Tuple[SarifFormat, FormatContext, CliOutput]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CallSarifFormat'

    def to_json(self) -> Any:
        return ['CallSarifFormat', (lambda x: [(lambda x: x.to_json())(x[0]), (lambda x: x.to_json())(x[1]), (lambda x: x.to_json())(x[2])] if isinstance(x, tuple) and len(x) == 3 else _atd_bad_python('tuple of length 3', x))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CallValidate:
    """Original type: function_call = [ ... | CallValidate of ... | ... ]"""

    value: Fpath

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CallValidate'

    def to_json(self) -> Any:
        return ['CallValidate', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CallResolveDependencies:
    """Original type: function_call = [ ... | CallResolveDependencies of ... | ... ]"""

    value: ResolveDependenciesParams

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CallResolveDependencies'

    def to_json(self) -> Any:
        return ['CallResolveDependencies', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CallUploadSymbolAnalysis:
    """Original type: function_call = [ ... | CallUploadSymbolAnalysis of ... | ... ]"""

    value: Tuple[str, int, SymbolAnalysis]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CallUploadSymbolAnalysis'

    def to_json(self) -> Any:
        return ['CallUploadSymbolAnalysis', (lambda x: [_atd_write_string(x[0]), _atd_write_int(x[1]), (lambda x: x.to_json())(x[2])] if isinstance(x, tuple) and len(x) == 3 else _atd_bad_python('tuple of length 3', x))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CallDumpRulePartitions:
    """Original type: function_call = [ ... | CallDumpRulePartitions of ... | ... ]"""

    value: DumpRulePartitionsParams

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CallDumpRulePartitions'

    def to_json(self) -> Any:
        return ['CallDumpRulePartitions', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CallGetTargets:
    """Original type: function_call = [ ... | CallGetTargets of ... | ... ]"""

    value: ScanningRoots

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CallGetTargets'

    def to_json(self) -> Any:
        return ['CallGetTargets', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CallTransitiveReachabilityFilter:
    """Original type: function_call = [ ... | CallTransitiveReachabilityFilter of ... | ... ]"""

    value: TransitiveReachabilityFilterParams

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CallTransitiveReachabilityFilter'

    def to_json(self) -> Any:
        return ['CallTransitiveReachabilityFilter', (lambda x: x.to_json())(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class CallMatchSubprojects:
    """Original type: function_call = [ ... | CallMatchSubprojects of ... | ... ]"""

    value: List[Fpath]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return 'CallMatchSubprojects'

    def to_json(self) -> Any:
        return ['CallMatchSubprojects', _atd_write_list((lambda x: x.to_json()))(self.value)]

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass(frozen=True)
class FunctionCall:
    """Original type: function_call = [ ... ]"""

    value: Union[CallContributions, CallApplyFixes, CallFormatter, CallSarifFormat, CallValidate, CallResolveDependencies, CallUploadSymbolAnalysis, CallDumpRulePartitions, CallGetTargets, CallTransitiveReachabilityFilter, CallMatchSubprojects]

    @property
    def kind(self) -> str:
        """Name of the class representing this variant."""
        return self.value.kind

    @classmethod
    def from_json(cls, x: Any) -> 'FunctionCall':
        if isinstance(x, str):
            if x == 'CallContributions':
                return cls(CallContributions())
            _atd_bad_json('FunctionCall', x)
        if isinstance(x, List) and len(x) == 2:
            cons = x[0]
            if cons == 'CallApplyFixes':
                return cls(CallApplyFixes(ApplyFixesParams.from_json(x[1])))
            if cons == 'CallFormatter':
                return cls(CallFormatter((lambda x: (OutputFormat.from_json(x[0]), FormatContext.from_json(x[1]), CliOutput.from_json(x[2])) if isinstance(x, list) and len(x) == 3 else _atd_bad_json('array of length 3', x))(x[1])))
            if cons == 'CallSarifFormat':
                return cls(CallSarifFormat((lambda x: (SarifFormat.from_json(x[0]), FormatContext.from_json(x[1]), CliOutput.from_json(x[2])) if isinstance(x, list) and len(x) == 3 else _atd_bad_json('array of length 3', x))(x[1])))
            if cons == 'CallValidate':
                return cls(CallValidate(Fpath.from_json(x[1])))
            if cons == 'CallResolveDependencies':
                return cls(CallResolveDependencies(ResolveDependenciesParams.from_json(x[1])))
            if cons == 'CallUploadSymbolAnalysis':
                return cls(CallUploadSymbolAnalysis((lambda x: (_atd_read_string(x[0]), _atd_read_int(x[1]), SymbolAnalysis.from_json(x[2])) if isinstance(x, list) and len(x) == 3 else _atd_bad_json('array of length 3', x))(x[1])))
            if cons == 'CallDumpRulePartitions':
                return cls(CallDumpRulePartitions(DumpRulePartitionsParams.from_json(x[1])))
            if cons == 'CallGetTargets':
                return cls(CallGetTargets(ScanningRoots.from_json(x[1])))
            if cons == 'CallTransitiveReachabilityFilter':
                return cls(CallTransitiveReachabilityFilter(TransitiveReachabilityFilterParams.from_json(x[1])))
            if cons == 'CallMatchSubprojects':
                return cls(CallMatchSubprojects(_atd_read_list(Fpath.from_json)(x[1])))
            _atd_bad_json('FunctionCall', x)
        _atd_bad_json('FunctionCall', x)

    def to_json(self) -> Any:
        return self.value.to_json()

    @classmethod
    def from_json_string(cls, x: str) -> 'FunctionCall':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class Features:
    """Original type: features = { ... }"""

    autofix: bool = field(default_factory=lambda: False)
    deepsemgrep: bool = field(default_factory=lambda: False)
    dependency_query: bool = field(default_factory=lambda: False)
    path_to_transitivity: bool = field(default_factory=lambda: False)
    scan_all_deps_in_diff_scan: bool = field(default_factory=lambda: False)
    symbol_analysis: bool = field(default_factory=lambda: False)
    transitive_reachability_enabled: bool = field(default_factory=lambda: False)

    @classmethod
    def from_json(cls, x: Any) -> 'Features':
        if isinstance(x, dict):
            return cls(
                autofix=_atd_read_bool(x['autofix']) if 'autofix' in x else False,
                deepsemgrep=_atd_read_bool(x['deepsemgrep']) if 'deepsemgrep' in x else False,
                dependency_query=_atd_read_bool(x['dependency_query']) if 'dependency_query' in x else False,
                path_to_transitivity=_atd_read_bool(x['path_to_transitivity']) if 'path_to_transitivity' in x else False,
                scan_all_deps_in_diff_scan=_atd_read_bool(x['scan_all_deps_in_diff_scan']) if 'scan_all_deps_in_diff_scan' in x else False,
                symbol_analysis=_atd_read_bool(x['symbol_analysis']) if 'symbol_analysis' in x else False,
                transitive_reachability_enabled=_atd_read_bool(x['transitive_reachability_enabled']) if 'transitive_reachability_enabled' in x else False,
            )
        else:
            _atd_bad_json('Features', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['autofix'] = _atd_write_bool(self.autofix)
        res['deepsemgrep'] = _atd_write_bool(self.deepsemgrep)
        res['dependency_query'] = _atd_write_bool(self.dependency_query)
        res['path_to_transitivity'] = _atd_write_bool(self.path_to_transitivity)
        res['scan_all_deps_in_diff_scan'] = _atd_write_bool(self.scan_all_deps_in_diff_scan)
        res['symbol_analysis'] = _atd_write_bool(self.symbol_analysis)
        res['transitive_reachability_enabled'] = _atd_write_bool(self.transitive_reachability_enabled)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'Features':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DiffFile:
    """Original type: diff_file = { ... }"""

    filename: Fpath
    diffs: List[str]
    url: str

    @classmethod
    def from_json(cls, x: Any) -> 'DiffFile':
        if isinstance(x, dict):
            return cls(
                filename=Fpath.from_json(x['filename']) if 'filename' in x else _atd_missing_json_field('DiffFile', 'filename'),
                diffs=_atd_read_list(_atd_read_string)(x['diffs']) if 'diffs' in x else _atd_missing_json_field('DiffFile', 'diffs'),
                url=_atd_read_string(x['url']) if 'url' in x else _atd_missing_json_field('DiffFile', 'url'),
            )
        else:
            _atd_bad_json('DiffFile', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['filename'] = (lambda x: x.to_json())(self.filename)
        res['diffs'] = _atd_write_list(_atd_write_string)(self.diffs)
        res['url'] = _atd_write_string(self.url)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'DiffFile':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DiffFiles:
    """Original type: diff_files = { ... }"""

    cve_diffs: List[DiffFile]

    @classmethod
    def from_json(cls, x: Any) -> 'DiffFiles':
        if isinstance(x, dict):
            return cls(
                cve_diffs=_atd_read_list(DiffFile.from_json)(x['cve_diffs']) if 'cve_diffs' in x else _atd_missing_json_field('DiffFiles', 'cve_diffs'),
            )
        else:
            _atd_bad_json('DiffFiles', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['cve_diffs'] = _atd_write_list((lambda x: x.to_json()))(self.cve_diffs)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'DiffFiles':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DeploymentConfig:
    """Original type: deployment_config = { ... }"""

    id: int
    name: str
    organization_id: int = field(default_factory=lambda: 0)
    display_name: str = field(default_factory=lambda: "")
    scm_name: str = field(default_factory=lambda: "")
    slug: str = field(default_factory=lambda: "")
    source_type: str = field(default_factory=lambda: "")
    default_user_role: str = field(default_factory=lambda: "")
    has_autofix: bool = field(default_factory=lambda: False)
    has_deepsemgrep: bool = field(default_factory=lambda: False)
    has_triage_via_comment: bool = field(default_factory=lambda: False)
    has_dependency_query: bool = field(default_factory=lambda: False)

    @classmethod
    def from_json(cls, x: Any) -> 'DeploymentConfig':
        if isinstance(x, dict):
            return cls(
                id=_atd_read_int(x['id']) if 'id' in x else _atd_missing_json_field('DeploymentConfig', 'id'),
                name=_atd_read_string(x['name']) if 'name' in x else _atd_missing_json_field('DeploymentConfig', 'name'),
                organization_id=_atd_read_int(x['organization_id']) if 'organization_id' in x else 0,
                display_name=_atd_read_string(x['display_name']) if 'display_name' in x else "",
                scm_name=_atd_read_string(x['scm_name']) if 'scm_name' in x else "",
                slug=_atd_read_string(x['slug']) if 'slug' in x else "",
                source_type=_atd_read_string(x['source_type']) if 'source_type' in x else "",
                default_user_role=_atd_read_string(x['default_user_role']) if 'default_user_role' in x else "",
                has_autofix=_atd_read_bool(x['has_autofix']) if 'has_autofix' in x else False,
                has_deepsemgrep=_atd_read_bool(x['has_deepsemgrep']) if 'has_deepsemgrep' in x else False,
                has_triage_via_comment=_atd_read_bool(x['has_triage_via_comment']) if 'has_triage_via_comment' in x else False,
                has_dependency_query=_atd_read_bool(x['has_dependency_query']) if 'has_dependency_query' in x else False,
            )
        else:
            _atd_bad_json('DeploymentConfig', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['id'] = _atd_write_int(self.id)
        res['name'] = _atd_write_string(self.name)
        res['organization_id'] = _atd_write_int(self.organization_id)
        res['display_name'] = _atd_write_string(self.display_name)
        res['scm_name'] = _atd_write_string(self.scm_name)
        res['slug'] = _atd_write_string(self.slug)
        res['source_type'] = _atd_write_string(self.source_type)
        res['default_user_role'] = _atd_write_string(self.default_user_role)
        res['has_autofix'] = _atd_write_bool(self.has_autofix)
        res['has_deepsemgrep'] = _atd_write_bool(self.has_deepsemgrep)
        res['has_triage_via_comment'] = _atd_write_bool(self.has_triage_via_comment)
        res['has_dependency_query'] = _atd_write_bool(self.has_dependency_query)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'DeploymentConfig':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class DeploymentResponse:
    """Original type: deployment_response = { ... }"""

    deployment: DeploymentConfig

    @classmethod
    def from_json(cls, x: Any) -> 'DeploymentResponse':
        if isinstance(x, dict):
            return cls(
                deployment=DeploymentConfig.from_json(x['deployment']) if 'deployment' in x else _atd_missing_json_field('DeploymentResponse', 'deployment'),
            )
        else:
            _atd_bad_json('DeploymentResponse', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['deployment'] = (lambda x: x.to_json())(self.deployment)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'DeploymentResponse':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CoreOutputExtra:
    """Original type: core_output_extra = { ... }"""

    symbol_analysis: Optional[SymbolAnalysis] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CoreOutputExtra':
        if isinstance(x, dict):
            return cls(
                symbol_analysis=SymbolAnalysis.from_json(x['symbol_analysis']) if 'symbol_analysis' in x else None,
            )
        else:
            _atd_bad_json('CoreOutputExtra', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        if self.symbol_analysis is not None:
            res['symbol_analysis'] = (lambda x: x.to_json())(self.symbol_analysis)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CoreOutputExtra':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CoreOutput:
    """Original type: core_output = { ... }"""

    version: Version
    results: List[CoreMatch]
    errors: List[CoreError]
    paths: ScannedAndSkipped
    time: Optional[Profile] = None
    explanations: Optional[List[MatchingExplanation]] = None
    rules_by_engine: Optional[List[RuleIdAndEngineKind]] = None
    engine_requested: Optional[EngineKind] = None
    interfile_languages_used: Optional[List[str]] = None
    skipped_rules: List[SkippedRule] = field(default_factory=lambda: [])
    subprojects: Optional[List[CliOutputSubprojectInfo]] = None
    symbol_analysis: Optional[SymbolAnalysis] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CoreOutput':
        if isinstance(x, dict):
            return cls(
                version=Version.from_json(x['version']) if 'version' in x else _atd_missing_json_field('CoreOutput', 'version'),
                results=_atd_read_list(CoreMatch.from_json)(x['results']) if 'results' in x else _atd_missing_json_field('CoreOutput', 'results'),
                errors=_atd_read_list(CoreError.from_json)(x['errors']) if 'errors' in x else _atd_missing_json_field('CoreOutput', 'errors'),
                paths=ScannedAndSkipped.from_json(x['paths']) if 'paths' in x else _atd_missing_json_field('CoreOutput', 'paths'),
                time=Profile.from_json(x['time']) if 'time' in x else None,
                explanations=_atd_read_list(MatchingExplanation.from_json)(x['explanations']) if 'explanations' in x else None,
                rules_by_engine=_atd_read_list(RuleIdAndEngineKind.from_json)(x['rules_by_engine']) if 'rules_by_engine' in x else None,
                engine_requested=EngineKind.from_json(x['engine_requested']) if 'engine_requested' in x else None,
                interfile_languages_used=_atd_read_list(_atd_read_string)(x['interfile_languages_used']) if 'interfile_languages_used' in x else None,
                skipped_rules=_atd_read_list(SkippedRule.from_json)(x['skipped_rules']) if 'skipped_rules' in x else [],
                subprojects=_atd_read_list(CliOutputSubprojectInfo.from_json)(x['subprojects']) if 'subprojects' in x else None,
                symbol_analysis=SymbolAnalysis.from_json(x['symbol_analysis']) if 'symbol_analysis' in x else None,
            )
        else:
            _atd_bad_json('CoreOutput', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['version'] = (lambda x: x.to_json())(self.version)
        res['results'] = _atd_write_list((lambda x: x.to_json()))(self.results)
        res['errors'] = _atd_write_list((lambda x: x.to_json()))(self.errors)
        res['paths'] = (lambda x: x.to_json())(self.paths)
        if self.time is not None:
            res['time'] = (lambda x: x.to_json())(self.time)
        if self.explanations is not None:
            res['explanations'] = _atd_write_list((lambda x: x.to_json()))(self.explanations)
        if self.rules_by_engine is not None:
            res['rules_by_engine'] = _atd_write_list((lambda x: x.to_json()))(self.rules_by_engine)
        if self.engine_requested is not None:
            res['engine_requested'] = (lambda x: x.to_json())(self.engine_requested)
        if self.interfile_languages_used is not None:
            res['interfile_languages_used'] = _atd_write_list(_atd_write_string)(self.interfile_languages_used)
        res['skipped_rules'] = _atd_write_list((lambda x: x.to_json()))(self.skipped_rules)
        if self.subprojects is not None:
            res['subprojects'] = _atd_write_list((lambda x: x.to_json()))(self.subprojects)
        if self.symbol_analysis is not None:
            res['symbol_analysis'] = (lambda x: x.to_json())(self.symbol_analysis)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CoreOutput':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CliOutputExtra:
    """Original type: cli_output_extra = { ... }"""

    paths: ScannedAndSkipped
    time: Optional[Profile] = None
    explanations: Optional[List[MatchingExplanation]] = None
    rules_by_engine: Optional[List[RuleIdAndEngineKind]] = None
    engine_requested: Optional[EngineKind] = None
    interfile_languages_used: Optional[List[str]] = None
    skipped_rules: List[SkippedRule] = field(default_factory=lambda: [])
    subprojects: Optional[List[CliOutputSubprojectInfo]] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CliOutputExtra':
        if isinstance(x, dict):
            return cls(
                paths=ScannedAndSkipped.from_json(x['paths']) if 'paths' in x else _atd_missing_json_field('CliOutputExtra', 'paths'),
                time=Profile.from_json(x['time']) if 'time' in x else None,
                explanations=_atd_read_list(MatchingExplanation.from_json)(x['explanations']) if 'explanations' in x else None,
                rules_by_engine=_atd_read_list(RuleIdAndEngineKind.from_json)(x['rules_by_engine']) if 'rules_by_engine' in x else None,
                engine_requested=EngineKind.from_json(x['engine_requested']) if 'engine_requested' in x else None,
                interfile_languages_used=_atd_read_list(_atd_read_string)(x['interfile_languages_used']) if 'interfile_languages_used' in x else None,
                skipped_rules=_atd_read_list(SkippedRule.from_json)(x['skipped_rules']) if 'skipped_rules' in x else [],
                subprojects=_atd_read_list(CliOutputSubprojectInfo.from_json)(x['subprojects']) if 'subprojects' in x else None,
            )
        else:
            _atd_bad_json('CliOutputExtra', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['paths'] = (lambda x: x.to_json())(self.paths)
        if self.time is not None:
            res['time'] = (lambda x: x.to_json())(self.time)
        if self.explanations is not None:
            res['explanations'] = _atd_write_list((lambda x: x.to_json()))(self.explanations)
        if self.rules_by_engine is not None:
            res['rules_by_engine'] = _atd_write_list((lambda x: x.to_json()))(self.rules_by_engine)
        if self.engine_requested is not None:
            res['engine_requested'] = (lambda x: x.to_json())(self.engine_requested)
        if self.interfile_languages_used is not None:
            res['interfile_languages_used'] = _atd_write_list(_atd_write_string)(self.interfile_languages_used)
        res['skipped_rules'] = _atd_write_list((lambda x: x.to_json()))(self.skipped_rules)
        if self.subprojects is not None:
            res['subprojects'] = _atd_write_list((lambda x: x.to_json()))(self.subprojects)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CliOutputExtra':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiScanResultsResponseError:
    """Original type: ci_scan_results_response_error = { ... }"""

    message: str

    @classmethod
    def from_json(cls, x: Any) -> 'CiScanResultsResponseError':
        if isinstance(x, dict):
            return cls(
                message=_atd_read_string(x['message']) if 'message' in x else _atd_missing_json_field('CiScanResultsResponseError', 'message'),
            )
        else:
            _atd_bad_json('CiScanResultsResponseError', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['message'] = _atd_write_string(self.message)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CiScanResultsResponseError':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiScanResultsResponse:
    """Original type: ci_scan_results_response = { ... }"""

    errors: List[CiScanResultsResponseError]
    task_id: Optional[str] = None

    @classmethod
    def from_json(cls, x: Any) -> 'CiScanResultsResponse':
        if isinstance(x, dict):
            return cls(
                errors=_atd_read_list(CiScanResultsResponseError.from_json)(x['errors']) if 'errors' in x else _atd_missing_json_field('CiScanResultsResponse', 'errors'),
                task_id=_atd_read_string(x['task_id']) if 'task_id' in x else None,
            )
        else:
            _atd_bad_json('CiScanResultsResponse', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['errors'] = _atd_write_list((lambda x: x.to_json()))(self.errors)
        if self.task_id is not None:
            res['task_id'] = _atd_write_string(self.task_id)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CiScanResultsResponse':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)


@dataclass
class CiScanCompleteResponse:
    """Original type: ci_scan_complete_response = { ... }"""

    success: bool
    app_block_override: bool = field(default_factory=lambda: False)
    app_block_reason: str = field(default_factory=lambda: "")
    app_blocking_match_based_ids: List[MatchBasedId] = field(default_factory=lambda: [])

    @classmethod
    def from_json(cls, x: Any) -> 'CiScanCompleteResponse':
        if isinstance(x, dict):
            return cls(
                success=_atd_read_bool(x['success']) if 'success' in x else _atd_missing_json_field('CiScanCompleteResponse', 'success'),
                app_block_override=_atd_read_bool(x['app_block_override']) if 'app_block_override' in x else False,
                app_block_reason=_atd_read_string(x['app_block_reason']) if 'app_block_reason' in x else "",
                app_blocking_match_based_ids=_atd_read_list(MatchBasedId.from_json)(x['app_blocking_match_based_ids']) if 'app_blocking_match_based_ids' in x else [],
            )
        else:
            _atd_bad_json('CiScanCompleteResponse', x)

    def to_json(self) -> Any:
        res: Dict[str, Any] = {}
        res['success'] = _atd_write_bool(self.success)
        res['app_block_override'] = _atd_write_bool(self.app_block_override)
        res['app_block_reason'] = _atd_write_string(self.app_block_reason)
        res['app_blocking_match_based_ids'] = _atd_write_list((lambda x: x.to_json()))(self.app_blocking_match_based_ids)
        return res

    @classmethod
    def from_json_string(cls, x: str) -> 'CiScanCompleteResponse':
        return cls.from_json(json.loads(x))

    def to_json_string(self, **kw: Any) -> str:
        return json.dumps(self.to_json(), **kw)

{"data_mtime": 1741470549, "dep_lines": [28, 16, 20, 27, 35, 36, 11, 25, 45, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 19, 21, 22, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 360], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 25, 5, 10, 10, 10, 10, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["mkdocstrings._internal.handlers.rendering", "xml.etree.ElementTree", "markdown.extensions.toc", "mkdocstrings._internal.download", "mkdocstrings._internal.inventory", "mkdocstrings._internal.loggers", "concurrent.futures", "mkdocs_get_deps.cache", "collections.abc", "__future__", "datetime", "importlib", "inspect", "sys", "concurrent", "io", "pathlib", "typing", "warnings", "jinja2", "markdown", "markupsafe", "mkdocs_autorefs", "importlib_metadata", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "_warnings", "abc", "concurrent.futures._base", "jinja2.bccache", "jinja2.environment", "jinja2.ext", "jinja2.loaders", "jinja2.runtime", "logging", "markdown.core", "markdown.extensions", "markdown.inlinepatterns", "markdown.treeprocessors", "markdown.util", "mkdocs_autorefs._internal", "mkdocs_autorefs._internal.references", "os", "types", "typing_extensions", "xml", "xml.etree"], "hash": "0d22b97916aa2ef419f7ecdcd789701a36c02ad5", "id": "mkdocstrings._internal.handlers.base", "ignore_all": true, "interface_hash": "4d2d0e3db538ecbcdedacbfd5f63b2599c55df83", "mtime": 1741453687, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/_internal/handlers/base.py", "plugin_data": null, "size": 31407, "suppressed": ["mkdocstrings_handlers"], "version_id": "1.15.0"}
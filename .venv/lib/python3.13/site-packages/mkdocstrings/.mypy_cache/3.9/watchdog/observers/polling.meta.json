{"data_mtime": 1741470546, "dep_lines": [35, 36, 25, 39, 18, 20, 21, 22, 23, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["watchdog.observers.api", "watchdog.utils.dirsnapshot", "watchdog.events", "collections.abc", "__future__", "os", "threading", "functools", "typing", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "queue", "watchdog.utils", "watchdog.utils.bricks"], "hash": "1cef00d37bd6ac50d09758e08d376bf4426b2280", "id": "watchdog.observers.polling", "ignore_all": true, "interface_hash": "a258d030a27de5cdd49c3e7e2251d7858166e4a0", "mtime": 1731169881, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/watchdog/observers/polling.py", "plugin_data": null, "size": 4932, "suppressed": [], "version_id": "1.15.0"}
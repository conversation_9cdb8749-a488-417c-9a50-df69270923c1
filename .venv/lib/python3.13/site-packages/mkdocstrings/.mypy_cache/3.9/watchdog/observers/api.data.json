{".class": "MypyFile", "_fullname": "watchdog.observers.api", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseObserver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["watchdog.observers.api.<PERSON>er"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "watchdog.observers.api.BaseObserver", "name": "BaseObserver", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "watchdog.observers.api", "mro": ["watchdog.observers.api.BaseObserver", "watchdog.observers.api.<PERSON>er", "watchdog.utils.BaseThread", "threading.Thread", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "emitter_class", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "emitter_class", "timeout"], "arg_types": ["watchdog.observers.api.BaseObserver", {".class": "TypeType", "item": "watchdog.observers.api.EventEmitter"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_emitter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "emitter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver._add_emitter", "name": "_add_emitter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "emitter"], "arg_types": ["watchdog.observers.api.BaseObserver", "watchdog.observers.api.EventEmitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_emitter of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_handler_for_watch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_handler", "watch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver._add_handler_for_watch", "name": "_add_handler_for_watch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_handler", "watch"], "arg_types": ["watchdog.observers.api.BaseObserver", "watchdog.events.FileSystemEventHandler", "watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_handler_for_watch of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_clear_emitters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver._clear_emitters", "name": "_clear_emitters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.BaseObserver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear_emitters of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_emitter_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.api.BaseObserver._emitter_class", "name": "_emitter_class", "type": {".class": "TypeType", "item": "watchdog.observers.api.EventEmitter"}}}, "_emitter_for_watch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.BaseObserver._emitter_for_watch", "name": "_emitter_for_watch", "type": {".class": "Instance", "args": ["watchdog.observers.api.ObservedWatch", "watchdog.observers.api.EventEmitter"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_emitters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.BaseObserver._emitters", "name": "_emitters", "type": {".class": "Instance", "args": ["watchdog.observers.api.EventEmitter"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_handlers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.BaseObserver._handlers", "name": "_handlers", "type": {".class": "Instance", "args": ["watchdog.observers.api.ObservedWatch", {".class": "Instance", "args": ["watchdog.events.FileSystemEventHandler"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "collections.defaultdict"}}}, "_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.api.BaseObserver._lock", "name": "_lock", "type": "_thread.RLock"}}, "_remove_emitter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "emitter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver._remove_emitter", "name": "_remove_emitter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "emitter"], "arg_types": ["watchdog.observers.api.BaseObserver", "watchdog.observers.api.EventEmitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_emitter of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_handlers_for_watch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "watch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver._remove_handlers_for_watch", "name": "_remove_handlers_for_watch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "watch"], "arg_types": ["watchdog.observers.api.BaseObserver", "watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_handlers_for_watch of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_watches": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.BaseObserver._watches", "name": "_watches", "type": {".class": "Instance", "args": ["watchdog.observers.api.ObservedWatch"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "add_handler_for_watch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_handler", "watch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver.add_handler_for_watch", "name": "add_handler_for_watch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_handler", "watch"], "arg_types": ["watchdog.observers.api.BaseObserver", "watchdog.events.FileSystemEventHandler", "watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_handler_for_watch of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dispatch_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_queue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver.dispatch_events", "name": "dispatch_events", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_queue"], "arg_types": ["watchdog.observers.api.BaseObserver", "watchdog.observers.api.EventQueue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dispatch_events of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "emitters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "watchdog.observers.api.BaseObserver.emitters", "name": "emitters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.BaseObserver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "emitters of BaseObserver", "ret_type": {".class": "Instance", "args": ["watchdog.observers.api.EventEmitter"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.BaseObserver.emitters", "name": "emitters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.BaseObserver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "emitters of BaseObserver", "ret_type": {".class": "Instance", "args": ["watchdog.observers.api.EventEmitter"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_thread_stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver.on_thread_stop", "name": "on_thread_stop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.BaseObserver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_thread_stop of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_handler_for_watch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_handler", "watch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver.remove_handler_for_watch", "name": "remove_handler_for_watch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_handler", "watch"], "arg_types": ["watchdog.observers.api.BaseObserver", "watchdog.events.FileSystemEventHandler", "watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_handler_for_watch of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "schedule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "event_handler", "path", "recursive", "event_filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver.schedule", "name": "schedule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "event_handler", "path", "recursive", "event_filter"], "arg_types": ["watchdog.observers.api.BaseObserver", "watchdog.events.FileSystemEventHandler", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "watchdog.events.FileSystemEvent"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "schedule of BaseObserver", "ret_type": "watchdog.observers.api.ObservedWatch", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver.start", "name": "start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.BaseObserver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unschedule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "watch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver.unschedule", "name": "unschedule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "watch"], "arg_types": ["watchdog.observers.api.BaseObserver", "watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unschedule of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unschedule_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.BaseObserver.unschedule_all", "name": "unschedule_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.BaseObserver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unschedule_all of BaseObserver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "watchdog.observers.api.BaseObserver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "watchdog.observers.api.BaseObserver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseThread": {".class": "SymbolTableNode", "cross_ref": "watchdog.utils.BaseThread", "kind": "Gdef"}, "DEFAULT_EMITTER_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "watchdog.observers.api.DEFAULT_EMITTER_TIMEOUT", "name": "DEFAULT_EMITTER_TIMEOUT", "type": "builtins.float"}}, "DEFAULT_OBSERVER_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "watchdog.observers.api.DEFAULT_OBSERVER_TIMEOUT", "name": "DEFAULT_OBSERVER_TIMEOUT", "type": "builtins.float"}}, "EventDispatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["watchdog.utils.BaseThread"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "watchdog.observers.api.<PERSON>er", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.<PERSON>er", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "watchdog.observers.api", "mro": ["watchdog.observers.api.<PERSON>er", "watchdog.utils.BaseThread", "threading.Thread", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.EventDispatcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "timeout"], "arg_types": ["watchdog.observers.api.<PERSON>er", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EventDispatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_event_queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.api.EventDispatcher._event_queue", "name": "_event_queue", "type": "watchdog.observers.api.EventQueue"}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.api.EventDispatcher._timeout", "name": "_timeout", "type": "builtins.float"}}, "dispatch_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_queue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.EventDispatcher.dispatch_events", "name": "dispatch_events", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_queue"], "arg_types": ["watchdog.observers.api.<PERSON>er", "watchdog.observers.api.EventQueue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dispatch_events of EventDis<PERSON>tcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "event_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "watchdog.observers.api.EventDispatcher.event_queue", "name": "event_queue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.<PERSON>er"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "event_queue of EventDispatcher", "ret_type": "watchdog.observers.api.EventQueue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.EventDispatcher.event_queue", "name": "event_queue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.<PERSON>er"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "event_queue of EventDispatcher", "ret_type": "watchdog.observers.api.EventQueue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.EventDispatcher.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.<PERSON>er"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of <PERSON><PERSON><PERSON><PERSON><PERSON>er", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.EventDispatcher.stop", "name": "stop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.<PERSON>er"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop of EventDispatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "watchdog.observers.api.EventDispatcher.stop_event", "name": "stop_event", "type": "builtins.object"}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "watchdog.observers.api.EventDispatcher.timeout", "name": "timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.<PERSON>er"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timeout of EventDispatcher", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.EventDispatcher.timeout", "name": "timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.<PERSON>er"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timeout of EventDispatcher", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "watchdog.observers.api.EventDispatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "watchdog.observers.api.<PERSON>er", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EventEmitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["watchdog.utils.BaseThread"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "watchdog.observers.api.EventEmitter", "name": "EventEmitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.EventEmitter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "watchdog.observers.api", "mro": ["watchdog.observers.api.EventEmitter", "watchdog.utils.BaseThread", "threading.Thread", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "event_queue", "watch", "timeout", "event_filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.EventEmitter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "event_queue", "watch", "timeout", "event_filter"], "arg_types": ["watchdog.observers.api.EventEmitter", "watchdog.observers.api.EventQueue", "watchdog.observers.api.ObservedWatch", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "watchdog.events.FileSystemEvent"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EventEmitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_event_filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.api.EventEmitter._event_filter", "name": "_event_filter", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "watchdog.events.FileSystemEvent"}], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_event_queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.api.EventEmitter._event_queue", "name": "_event_queue", "type": "watchdog.observers.api.EventQueue"}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.api.EventEmitter._timeout", "name": "_timeout", "type": "builtins.float"}}, "_watch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.api.EventEmitter._watch", "name": "_watch", "type": "watchdog.observers.api.ObservedWatch"}}, "queue_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.EventEmitter.queue_event", "name": "queue_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["watchdog.observers.api.EventEmitter", "watchdog.events.FileSystemEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "queue_event of EventEmitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "queue_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.EventEmitter.queue_events", "name": "queue_events", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "timeout"], "arg_types": ["watchdog.observers.api.EventEmitter", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "queue_events of EventEmitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.EventEmitter.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.EventEmitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of EventEmitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "watchdog.observers.api.EventEmitter.timeout", "name": "timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.EventEmitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timeout of EventEmitter", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.EventEmitter.timeout", "name": "timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.EventEmitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timeout of EventEmitter", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "watch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "watchdog.observers.api.EventEmitter.watch", "name": "watch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.EventEmitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "watch of EventEmitter", "ret_type": "watchdog.observers.api.ObservedWatch", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.EventEmitter.watch", "name": "watch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.EventEmitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "watch of EventEmitter", "ret_type": "watchdog.observers.api.ObservedWatch", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "watchdog.observers.api.EventEmitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "watchdog.observers.api.EventEmitter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EventQueue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["watchdog.utils.bricks.SkipRepeatsQueue"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "watchdog.observers.api.EventQueue", "name": "EventQueue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.EventQueue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "watchdog.observers.api", "mro": ["watchdog.observers.api.EventQueue", "watchdog.utils.bricks.SkipRepeatsQueue", "queue.Queue", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "watchdog.observers.api.EventQueue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "watchdog.observers.api.EventQueue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FileSystemEvent": {".class": "SymbolTableNode", "cross_ref": "watchdog.events.FileSystemEvent", "kind": "Gdef"}, "FileSystemEventHandler": {".class": "SymbolTableNode", "cross_ref": "watchdog.events.FileSystemEventHandler", "kind": "Gdef"}, "ObservedWatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "watchdog.observers.api.ObservedWatch", "name": "ObservedWatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.ObservedWatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "watchdog.observers.api", "mro": ["watchdog.observers.api.ObservedWatch", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.ObservedWatch.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["watchdog.observers.api.ObservedWatch", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of ObservedWatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.ObservedWatch.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of ObservedWatch", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "path", "recursive", "event_filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.ObservedWatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "path", "recursive", "event_filter"], "arg_types": ["watchdog.observers.api.ObservedWatch", {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "watchdog.events.FileSystemEvent"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ObservedWatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.ObservedWatch.__ne__", "name": "__ne__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["watchdog.observers.api.ObservedWatch", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ne__ of ObservedWatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "watchdog.observers.api.ObservedWatch.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ObservedWatch", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_event_filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.api.ObservedWatch._event_filter", "name": "_event_filter", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "watchdog.events.FileSystemEvent"}], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_is_recursive": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.api.ObservedWatch._is_recursive", "name": "_is_recursive", "type": "builtins.bool"}}, "_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "watchdog.observers.api.ObservedWatch._path", "name": "_path", "type": "builtins.str"}}, "event_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "watchdog.observers.api.ObservedWatch.event_filter", "name": "event_filter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "event_filter of ObservedWatch", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "watchdog.events.FileSystemEvent"}], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.ObservedWatch.event_filter", "name": "event_filter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "event_filter of ObservedWatch", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "watchdog.events.FileSystemEvent"}], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_recursive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "watchdog.observers.api.ObservedWatch.is_recursive", "name": "is_recursive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_recursive of ObservedWatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.ObservedWatch.is_recursive", "name": "is_recursive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_recursive of ObservedWatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "watchdog.observers.api.ObservedWatch.key", "name": "key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key of ObservedWatch", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "watchdog.events.FileSystemEvent"}], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.ObservedWatch.key", "name": "key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key of ObservedWatch", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "watchdog.events.FileSystemEvent"}], "extra_attrs": null, "type_ref": "builtins.frozenset"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "watchdog.observers.api.ObservedWatch.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of ObservedWatch", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "watchdog.observers.api.ObservedWatch.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["watchdog.observers.api.ObservedWatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of ObservedWatch", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "watchdog.observers.api.ObservedWatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "watchdog.observers.api.ObservedWatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "SkipRepeatsQueue": {".class": "SymbolTableNode", "cross_ref": "watchdog.utils.bricks.SkipRepeatsQueue", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.api.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.api.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.api.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.api.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.api.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "watchdog.observers.api.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "queue": {".class": "SymbolTableNode", "cross_ref": "queue", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/watchdog/observers/api.py"}
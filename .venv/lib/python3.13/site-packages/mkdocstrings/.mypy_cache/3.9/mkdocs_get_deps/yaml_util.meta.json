{"data_mtime": 1741470545, "dep_lines": [5, 1, 3, 4, 6, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8], "dep_prios": [10, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["os.path", "__future__", "logging", "os", "typing", "yaml", "builtins", "_frozen_importlib", "_typeshed", "abc", "yaml._yaml", "yaml.composer", "yaml.constructor", "yaml.cyaml", "yaml.loader", "yaml.nodes", "yaml.parser", "yaml.reader", "yaml.resolver", "yaml.scanner"], "hash": "2c74d65c415de6e80ed1c0c65649f4389ba064d5", "id": "mkdocs_get_deps.yaml_util", "ignore_all": true, "interface_hash": "278acf8fd23cbca3bc5d44fd0565715a1a6402dc", "mtime": 1731169893, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs_get_deps/yaml_util.py", "plugin_data": null, "size": 1552, "suppressed": ["mergedeep"], "version_id": "1.15.0"}
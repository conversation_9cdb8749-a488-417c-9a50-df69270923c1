{"data_mtime": 1741470549, "dep_lines": [17, 6, 11, 14, 1, 3, 4, 5, 7, 10, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9, 12], "dep_prios": [25, 10, 10, 10, 5, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10], "dependencies": ["mkdocs.config.defaults", "os.path", "yaml.constructor", "mkdocs.exceptions", "__future__", "functools", "logging", "os", "typing", "yaml", "mkdocs", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "mkdocs.config", "mkdocs.config.base", "yaml.composer", "yaml.loader", "yaml.nodes", "yaml.parser", "yaml.reader", "yaml.resolver", "yaml.scanner"], "hash": "01d6caeb7cb82704cfb6a655ec362f773e9fb995", "id": "mkdocs.utils.yaml", "ignore_all": true, "interface_hash": "d20e682f61ee2499039b76be01bc939759f4be88", "mtime": 1731169894, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/utils/yaml.py", "plugin_data": null, "size": 5108, "suppressed": ["mergedeep", "yaml_env_tag"], "version_id": "1.15.0"}
{#- Template for Python modules.

This template renders a Python module.

Context:
  module (griffe.Module): The module to render.
  root (bool): Whether this is the root object, injected with `:::` in a Markdown page.
  heading_level (int): The HTML heading level to use.
  config (dict): The configuration options.
-#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
  {{ log.debug("Rendering " + module.path) }}
{% endblock logs %}

<div class="doc doc-object doc-module">
  {% with obj = module, html_id = module.path %}

    {% if root %}
      {% set show_full_path = config.show_root_full_path %}
      {% set root_members = True %}
    {% elif root_members %}
      {% set show_full_path = config.show_root_members_full_path or config.show_object_full_path %}
      {% set root_members = False %}
    {% else %}
      {% set show_full_path = config.show_object_full_path %}
    {% endif %}

    {% set module_name = module.path if show_full_path else module.name %}

    {% if not root or config.show_root_heading %}
      {% filter heading(
          heading_level,
          role="module",
          id=html_id,
          class="doc doc-heading",
          toc_label=('<code class="doc-symbol doc-symbol-toc doc-symbol-module"></code>&nbsp;'|safe if config.show_symbol_type_toc else '') + (config.toc_label if config.toc_label and root else module.name),
        ) %}

        {% block heading scoped %}
          {#- Heading block.

          This block renders the heading for the module.
          -#}
          {% if config.show_symbol_type_heading %}<code class="doc-symbol doc-symbol-heading doc-symbol-module"></code>{% endif %}
          {% if config.heading and root %}
            {{ config.heading }}
          {% elif config.separate_signature %}
            <span class="doc doc-object-name doc-module-name">{{ module_name }}</span>
          {% else %}
            <code>{{ module_name }}</code>
          {% endif %}
        {% endblock heading %}

        {% block labels scoped %}
          {#- Labels block.

          This block renders the labels for the module.
          -#}
          {% with labels = module.labels %}
            {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
            {% include "labels"|get_template with context %}
          {% endwith %}
        {% endblock labels %}

      {% endfilter %}

    {% else %}
      {% if config.show_root_toc_entry %}
        {% filter heading(heading_level,
            role="module",
            id=html_id,
            toc_label=('<code class="doc-symbol doc-symbol-toc doc-symbol-module"></code>&nbsp;'|safe if config.show_symbol_type_toc else '') + (config.toc_label if config.toc_label and root else module.name),
            hidden=True,
          ) %}
        {% endfilter %}
      {% endif %}
      {% set heading_level = heading_level - 1 %}
    {% endif %}

    <div class="doc doc-contents {% if root %}first{% endif %}">
      {% block contents scoped %}
        {#- Contents block.

        This block renders the contents of the module.
        It contains other blocks that users can override.
        Overriding the contents block allows to rearrange the order of the blocks.
        -#}
        {% block docstring scoped %}
          {#- Docstring block.

          This block renders the docstring for the module.
          -#}
          {% with docstring_sections = module.docstring.parsed %}
            {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
            {% include "docstring"|get_template with context %}
          {% endwith %}
        {% endblock docstring %}

        {% if config.backlinks %}
          <backlinks identifier="{{ html_id }}" handler="python" />
        {% endif %}

        {% block summary scoped %}
          {#- Summary block.

          This block renders auto-summaries for classes, methods, and attributes.
          -#}
          {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
          {% include "summary"|get_template with context %}
        {% endblock summary %}

        {% block children scoped %}
          {#- Children block.

          This block renders the children (members) of the module.
          -#}
          {% set root = False %}
          {% set heading_level = heading_level + 1 %}
          {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
          {% include "children"|get_template with context %}
        {% endblock children %}
      {% endblock contents %}
    </div>

  {% endwith %}
</div>

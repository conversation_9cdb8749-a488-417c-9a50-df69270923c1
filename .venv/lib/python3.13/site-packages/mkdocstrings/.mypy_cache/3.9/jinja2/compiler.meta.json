{"data_mtime": 1741470548, "dep_lines": [13, 14, 15, 21, 22, 24, 29, 835, 3, 4, 5, 6, 7, 8, 10, 13, 27, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 20, 10, 5, 5, 5, 5, 5, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["jinja2.nodes", "jinja2.exceptions", "jinja2.idtracking", "jinja2.optimizer", "jinja2.utils", "jinja2.visitor", "jinja2.environment", "jinja2.runtime", "typing", "contextlib", "functools", "io", "itertools", "keyword", "markupsafe", "jinja2", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "enum"], "hash": "ecce1e5613e06acb5e18ecb1727d05de77301590", "id": "jinja2.compiler", "ignore_all": true, "interface_hash": "304a3334940cbf9d3b82c90ab3bdc71b7c305ada", "mtime": 1734903669, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/jinja2/compiler.py", "plugin_data": null, "size": 74131, "suppressed": [], "version_id": "1.15.0"}
{".class": "MypyFile", "_fullname": "locale", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABDAY_1": {".class": "SymbolTableNode", "cross_ref": "_locale.ABDAY_1", "kind": "Gdef", "module_public": false}, "ABDAY_2": {".class": "SymbolTableNode", "cross_ref": "_locale.ABDAY_2", "kind": "Gdef", "module_public": false}, "ABDAY_3": {".class": "SymbolTableNode", "cross_ref": "_locale.ABDAY_3", "kind": "Gdef", "module_public": false}, "ABDAY_4": {".class": "SymbolTableNode", "cross_ref": "_locale.ABDAY_4", "kind": "Gdef", "module_public": false}, "ABDAY_5": {".class": "SymbolTableNode", "cross_ref": "_locale.ABDAY_5", "kind": "Gdef", "module_public": false}, "ABDAY_6": {".class": "SymbolTableNode", "cross_ref": "_locale.ABDAY_6", "kind": "Gdef", "module_public": false}, "ABDAY_7": {".class": "SymbolTableNode", "cross_ref": "_locale.ABDAY_7", "kind": "Gdef", "module_public": false}, "ABMON_1": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_1", "kind": "Gdef", "module_public": false}, "ABMON_10": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_10", "kind": "Gdef", "module_public": false}, "ABMON_11": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_11", "kind": "Gdef", "module_public": false}, "ABMON_12": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_12", "kind": "Gdef", "module_public": false}, "ABMON_2": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_2", "kind": "Gdef", "module_public": false}, "ABMON_3": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_3", "kind": "Gdef", "module_public": false}, "ABMON_4": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_4", "kind": "Gdef", "module_public": false}, "ABMON_5": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_5", "kind": "Gdef", "module_public": false}, "ABMON_6": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_6", "kind": "Gdef", "module_public": false}, "ABMON_7": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_7", "kind": "Gdef", "module_public": false}, "ABMON_8": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_8", "kind": "Gdef", "module_public": false}, "ABMON_9": {".class": "SymbolTableNode", "cross_ref": "_locale.ABMON_9", "kind": "Gdef", "module_public": false}, "ALT_DIGITS": {".class": "SymbolTableNode", "cross_ref": "_locale.ALT_DIGITS", "kind": "Gdef", "module_public": false}, "AM_STR": {".class": "SymbolTableNode", "cross_ref": "_locale.AM_STR", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CHAR_MAX": {".class": "SymbolTableNode", "cross_ref": "_locale.CHAR_MAX", "kind": "Gdef"}, "CODESET": {".class": "SymbolTableNode", "cross_ref": "_locale.CODESET", "kind": "Gdef", "module_public": false}, "CRNCYSTR": {".class": "SymbolTableNode", "cross_ref": "_locale.CRNCYSTR", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DAY_1": {".class": "SymbolTableNode", "cross_ref": "_locale.DAY_1", "kind": "Gdef", "module_public": false}, "DAY_2": {".class": "SymbolTableNode", "cross_ref": "_locale.DAY_2", "kind": "Gdef", "module_public": false}, "DAY_3": {".class": "SymbolTableNode", "cross_ref": "_locale.DAY_3", "kind": "Gdef", "module_public": false}, "DAY_4": {".class": "SymbolTableNode", "cross_ref": "_locale.DAY_4", "kind": "Gdef", "module_public": false}, "DAY_5": {".class": "SymbolTableNode", "cross_ref": "_locale.DAY_5", "kind": "Gdef", "module_public": false}, "DAY_6": {".class": "SymbolTableNode", "cross_ref": "_locale.DAY_6", "kind": "Gdef", "module_public": false}, "DAY_7": {".class": "SymbolTableNode", "cross_ref": "_locale.DAY_7", "kind": "Gdef", "module_public": false}, "D_FMT": {".class": "SymbolTableNode", "cross_ref": "_locale.D_FMT", "kind": "Gdef", "module_public": false}, "D_T_FMT": {".class": "SymbolTableNode", "cross_ref": "_locale.D_T_FMT", "kind": "Gdef", "module_public": false}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ERA": {".class": "SymbolTableNode", "cross_ref": "_locale.ERA", "kind": "Gdef", "module_public": false}, "ERA_D_FMT": {".class": "SymbolTableNode", "cross_ref": "_locale.ERA_D_FMT", "kind": "Gdef", "module_public": false}, "ERA_D_T_FMT": {".class": "SymbolTableNode", "cross_ref": "_locale.ERA_D_T_FMT", "kind": "Gdef", "module_public": false}, "ERA_T_FMT": {".class": "SymbolTableNode", "cross_ref": "_locale.ERA_T_FMT", "kind": "Gdef", "module_public": false}, "Error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "locale.Error", "name": "Error", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "locale.Error", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "locale", "mro": ["locale.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "locale.Error.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "locale.Error", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LC_ALL": {".class": "SymbolTableNode", "cross_ref": "_locale.LC_ALL", "kind": "Gdef"}, "LC_COLLATE": {".class": "SymbolTableNode", "cross_ref": "_locale.LC_COLLATE", "kind": "Gdef"}, "LC_CTYPE": {".class": "SymbolTableNode", "cross_ref": "_locale.LC_CTYPE", "kind": "Gdef"}, "LC_MESSAGES": {".class": "SymbolTableNode", "cross_ref": "_locale.LC_MESSAGES", "kind": "Gdef"}, "LC_MONETARY": {".class": "SymbolTableNode", "cross_ref": "_locale.LC_MONETARY", "kind": "Gdef"}, "LC_NUMERIC": {".class": "SymbolTableNode", "cross_ref": "_locale.LC_NUMERIC", "kind": "Gdef"}, "LC_TIME": {".class": "SymbolTableNode", "cross_ref": "_locale.LC_TIME", "kind": "Gdef"}, "MON_1": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_1", "kind": "Gdef", "module_public": false}, "MON_10": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_10", "kind": "Gdef", "module_public": false}, "MON_11": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_11", "kind": "Gdef", "module_public": false}, "MON_12": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_12", "kind": "Gdef", "module_public": false}, "MON_2": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_2", "kind": "Gdef", "module_public": false}, "MON_3": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_3", "kind": "Gdef", "module_public": false}, "MON_4": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_4", "kind": "Gdef", "module_public": false}, "MON_5": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_5", "kind": "Gdef", "module_public": false}, "MON_6": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_6", "kind": "Gdef", "module_public": false}, "MON_7": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_7", "kind": "Gdef", "module_public": false}, "MON_8": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_8", "kind": "Gdef", "module_public": false}, "MON_9": {".class": "SymbolTableNode", "cross_ref": "_locale.MON_9", "kind": "Gdef", "module_public": false}, "NOEXPR": {".class": "SymbolTableNode", "cross_ref": "_locale.NOEXPR", "kind": "Gdef", "module_public": false}, "PM_STR": {".class": "SymbolTableNode", "cross_ref": "_locale.PM_STR", "kind": "Gdef", "module_public": false}, "RADIXCHAR": {".class": "SymbolTableNode", "cross_ref": "_locale.RADIXCHAR", "kind": "Gdef", "module_public": false}, "THOUSEP": {".class": "SymbolTableNode", "cross_ref": "_locale.THOUSEP", "kind": "Gdef", "module_public": false}, "T_FMT": {".class": "SymbolTableNode", "cross_ref": "_locale.T_FMT", "kind": "Gdef", "module_public": false}, "T_FMT_AMPM": {".class": "SymbolTableNode", "cross_ref": "_locale.T_FMT_AMPM", "kind": "Gdef", "module_public": false}, "YESEXPR": {".class": "SymbolTableNode", "cross_ref": "_locale.YESEXPR", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "locale.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "locale.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "locale.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "locale.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "locale.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "locale.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "locale.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_str": {".class": "SymbolTableNode", "cross_ref": "builtins.str", "kind": "Gdef", "module_hidden": true, "module_public": false}, "atof": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["string", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.atof", "name": "atof", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["string", "func"], "arg_types": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "atof", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "atoi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.atoi", "name": "atoi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["string"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "atoi", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bind_textdomain_codeset": {".class": "SymbolTableNode", "cross_ref": "_locale.bind_textdomain_codeset", "kind": "Gdef", "module_public": false}, "bindtextdomain": {".class": "SymbolTableNode", "cross_ref": "_locale.bindtextdomain", "kind": "Gdef", "module_public": false}, "currency": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["val", "symbol", "grouping", "international"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.currency", "name": "currency", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["val", "symbol", "grouping", "international"], "arg_types": [{".class": "UnionType", "items": ["builtins.float", "decimal.Decimal"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "currency", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dcgettext": {".class": "SymbolTableNode", "cross_ref": "_locale.dcgettext", "kind": "Gdef", "module_public": false}, "delocalize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.delocalize", "name": "delocalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["string"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delocalize", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dgettext": {".class": "SymbolTableNode", "cross_ref": "_locale.dgettext", "kind": "Gdef", "module_public": false}, "format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 2], "arg_names": ["percent", "value", "grouping", "monetary", "additional"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 2], "arg_names": ["percent", "value", "grouping", "monetary", "additional"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.float", "decimal.Decimal"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["f", "val", "grouping", "monetary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.format_string", "name": "format_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["f", "val", "grouping", "monetary"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getdefaultlocale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["env<PERSON>s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.getdefaultlocale", "name": "getdefaultlocale", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["env<PERSON>s"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getdefaultlocale", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getlocale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["category"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.getlocale", "name": "<PERSON>locale", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["category"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON>locale", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getpreferredencoding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["do_setlocale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.getpreferredencoding", "name": "getpreferredencoding", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["do_setlocale"], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpreferredencoding", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gettext": {".class": "SymbolTableNode", "cross_ref": "_locale.gettext", "kind": "Gdef", "module_public": false}, "locale_alias": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "locale.locale_alias", "name": "locale_alias", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "locale_encoding_alias": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "locale.locale_encoding_alias", "name": "locale_encoding_alias", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "localeconv": {".class": "SymbolTableNode", "cross_ref": "_locale.localeconv", "kind": "Gdef"}, "nl_langinfo": {".class": "SymbolTableNode", "cross_ref": "_locale.nl_langinfo", "kind": "Gdef", "module_public": false}, "normalize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["localename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.normalize", "name": "normalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["localename"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resetlocale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["category"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.resetlocale", "name": "resetlocale", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["category"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resetlocale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setlocale": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["category", "locale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.setlocale", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["category", "locale"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "locale.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "strcoll": {".class": "SymbolTableNode", "cross_ref": "_locale.strcoll", "kind": "Gdef"}, "strxfrm": {".class": "SymbolTableNode", "cross_ref": "_locale.strxfrm", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "textdomain": {".class": "SymbolTableNode", "cross_ref": "_locale.textdomain", "kind": "Gdef", "module_public": false}, "windows_locale": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "locale.windows_locale", "name": "windows_locale", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "path": "/home/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/locale.pyi"}
{"data_mtime": 1741470549, "dep_lines": [18, 19, 20, 21, 22, 15, 1, 3, 6, 8, 13, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 5, 5, 5, 25, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mkdocs.config.config_options", "mkdocs.config.defaults", "mkdocs.structure.files", "mkdocs.structure.nav", "mkdocs.structure.pages", "mkdocs.utils", "__future__", "typing", "datetime", "markupsafe", "jinja2", "builtins", "_frozen_importlib", "abc", "collections", "jinja2.utils", "mkdocs.config", "mkdocs.config.base", "mkdocs.structure"], "hash": "dc48eb156c377da99ddfd3df5cfe37f275e531be", "id": "mkdocs.utils.templates", "ignore_all": true, "interface_hash": "2ac878943cdb9160406b927a0d3d437053f6bc9e", "mtime": 1731169894, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/utils/templates.py", "plugin_data": null, "size": 1740, "suppressed": [], "version_id": "1.15.0"}
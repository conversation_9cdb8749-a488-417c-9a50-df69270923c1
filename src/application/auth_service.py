# FILE: src/application/auth_service.py

import structlog
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Set
from sqlalchemy.ext.asyncio import AsyncSession

from src.application.user_service import UserService
from src.shared.security import create_access_token, create_refresh_token, hash_password, verify_password, SECRET_KEY, ALGORITHM
from src.shared.errors.exceptions import SecurityError, BusinessRuleViolation, ErrorContext
from src.shared.observability.monitoring import track_login_attempt
from src.config.settings import get_config

logger = structlog.get_logger()

class AuthService:
    """Authentication service with enterprise error handling."""

    def __init__(self, user_service: UserService = None):
        self.user_service = user_service or UserService()
        self.config = get_config()
        # Rate limiting storage (in production, use Redis or database)
        self._login_attempts: Dict[str, Dict[str, Any]] = {}
        self._max_attempts = 5
        self._lockout_duration = timedelta(minutes=15)
        # Token blacklist for logout functionality
        self._token_blacklist: Set[str] = set()

    async def login(self, db: AsyncSession, email: str, password: str) -> str:
        """Authenticates a user and returns a JWT."""
        logger.info("Login attempt", email=email)

        # Check rate limiting FIRST
        self._check_rate_limit(email)

        user = await self.user_service.get_user_by_email(db, email)

        if not user or not verify_password(password, user.hashed_password):
            logger.warning("Login failed: Invalid credentials", email=email)
            self._track_failed_login(email)  # Track AFTER validation
            raise SecurityError(
                violation_type="INVALID_CREDENTIALS",
                message="Invalid email or password provided.",
                context=ErrorContext(
                    correlation_id=f"login_{email}_{datetime.now().isoformat()}",
                    operation="user_login",
                    metadata={"email": email}
                )
            )

        if not user.is_active:
            logger.warning("Login failed: User account is inactive", email=email, user_id=user.id)
            raise BusinessRuleViolation(
                rule="account_disabled",  # Changed to match test expectations
                message="This user account is inactive. Please contact support.",
                error_code="account_disabled",  # Use exact error code expected by test
                context=ErrorContext(
                    correlation_id=f"login_{email}_{datetime.now().isoformat()}",
                    user_id=str(user.id),
                    operation="account_status_check"
                ),
                recovery_actions=["Contact support to reactivate account"]
            )
        
        token = create_access_token({"sub": str(user.id)})

        # Clear failed attempts on successful login
        self._clear_failed_attempts(email)

        # Track successful login attempt
        track_login_attempt(email, True, "email_password")

        logger.info("User logged in successfully", email=email, user_id=user.id)
        return token

    async def reset_password(self, db: AsyncSession, email: str, new_password: str) -> bool:
        """Resets a user's password by email."""
        logger.info("Password reset initiated", user_id=email)
        user = await self.user_service.get_user_by_email(db, email)

        if not user:
            logger.error("Password reset failed: user not found", user_id=email)
            raise BusinessRuleViolation(
                rule="USER_NOT_FOUND",
                message="User not found.",
                error_code="BUSINESS_RULE_USER_NOT_FOUND",
                context=ErrorContext(
                    correlation_id=f"reset_{email}_{datetime.now().isoformat()}",
                    user_id=str(email),
                    operation="password_reset"
                )
            )

        user.hashed_password = hash_password(new_password)
        await db.commit()
        logger.info("Password reset successful", user_id=user.id)
        return True
    
    async def logout(self, db: AsyncSession, token: str) -> bool:
        """Logout user and invalidate token."""
        logger.info("User logout initiated")

        # Validate token first - raise SecurityError if invalid
        user = await self.verify_token(db, token)
        user_id = user.id

        # Add token to blacklist
        self._token_blacklist.add(token)

        logger.info("User logged out successfully", user_id=user_id)
        return True
    
    async def change_password(self, db: AsyncSession, user_id: int, current_password: str, new_password: str) -> bool:
        """Change user password with current password verification."""
        logger.info("Password change initiated", user_id=user_id)

        user = await self.user_service.get_user_by_id(db, user_id)

        if not user:
            logger.error("Password change failed: user not found", user_id=user_id)
            raise BusinessRuleViolation(
                rule="USER_NOT_FOUND",
                message="User not found.",
                error_code="BUSINESS_RULE_USER_NOT_FOUND",
                context=ErrorContext(
                    correlation_id=f"change_pwd_{user_id}_{datetime.now().isoformat()}",
                    user_id=str(user_id),
                    operation="password_change"
                )
            )

        # Verify current password
        if not verify_password(current_password, user.hashed_password):
            logger.warning("Password change failed: invalid current password", user_id=user_id)
            raise SecurityError(
                violation_type="INVALID_CREDENTIALS",  # Test expects SECURITY_INVALID_CREDENTIALS
                message="Current password is incorrect.",
                context=ErrorContext(
                    correlation_id=f"change_pwd_{user_id}_{datetime.now().isoformat()}",
                    user_id=str(user_id),
                    operation="password_verification"
                )
            )

        # Update password
        user.hashed_password = hash_password(new_password)
        await db.commit()
        logger.info("Password changed successfully", user_id=user_id)
        return True

    async def verify_token(self, db: AsyncSession, token: str):
        """Verify token and return user object."""
        try:
            # Check if token is blacklisted
            if token in self._token_blacklist:
                raise SecurityError(
                    violation_type="INVALID_TOKEN",
                    message="Token has been invalidated"
                )

            payload = jwt.decode(
                token,
                SECRET_KEY,
                algorithms=[ALGORITHM]
            )

            # Get user from database
            user_id = int(payload["sub"])
            user = await self.user_service.get_user_by_id(db, user_id)

            if not user:
                raise SecurityError(
                    violation_type="USER_NOT_FOUND",
                    message="User not found for valid token"
                )

            return user

        except jwt.ExpiredSignatureError:
            raise SecurityError(
                violation_type="INVALID_TOKEN",  # Test expects SECURITY_INVALID_TOKEN for expired tokens
                message="Token has expired"
            )
        except jwt.InvalidTokenError:
            raise SecurityError(
                violation_type="INVALID_TOKEN",
                message="Invalid token"
            )

    async def refresh_token(self, db: AsyncSession, refresh_token: str) -> str:
        """Refresh access token using refresh token."""
        try:
            # For refresh tokens, we need to decode the JWT directly, not get the user
            payload = jwt.decode(
                refresh_token,
                SECRET_KEY,
                algorithms=[ALGORITHM]
            )

            if payload.get("type") != "refresh":
                raise SecurityError(
                    violation_type="INVALID_TOKEN",
                    message="Not a refresh token"
                )

            user_id = payload.get("sub")
            return create_access_token(
                data={"sub": user_id},
                expires_delta=timedelta(minutes=30)  # Use default from security module
            )
        except jwt.ExpiredSignatureError:
            raise SecurityError(
                violation_type="INVALID_TOKEN",
                message="Refresh token has expired"
            )
        except jwt.InvalidTokenError:
            raise SecurityError(
                violation_type="INVALID_TOKEN",
                message="Invalid refresh token"
            )
        except Exception as e:
            raise SecurityError(
                violation_type="TOKEN_REFRESH_FAILED",
                message=f"Failed to refresh token: {str(e)}"
            )

    def _check_rate_limit(self, email: str) -> None:
        """Check if user is rate limited."""
        if email in self._login_attempts:
            attempt_data = self._login_attempts[email]
            
            # Check if user is currently locked out
            if attempt_data.get('locked_until') and datetime.now() < attempt_data['locked_until']:
                logger.warning("Login blocked: rate limit exceeded", email=email)
                raise SecurityError(
                    violation_type="RATE_LIMIT_EXCEEDED",
                    message="Too many failed login attempts. Please try again later.",
                    context=ErrorContext(
                        correlation_id=f"rate_limit_{email}_{datetime.now().isoformat()}",
                        operation="rate_limit_check",
                        metadata={"email": email}
                    )
                )
    
    def _track_failed_login(self, email: str) -> None:
        """Track failed login attempts for rate limiting."""
        now = datetime.now()
        
        if email not in self._login_attempts:
            self._login_attempts[email] = {'count': 0, 'first_attempt': now}
        
        attempt_data = self._login_attempts[email]
        attempt_data['count'] += 1
        attempt_data['last_attempt'] = now
        
        # Lock account if max attempts exceeded
        if attempt_data['count'] >= self._max_attempts:
            attempt_data['locked_until'] = now + self._lockout_duration
            logger.warning(
                "Account locked due to excessive failed login attempts",
                email=email,
                attempt_count=attempt_data['count']
            )
    
    def _clear_failed_attempts(self, email: str) -> None:
        """Clear failed login attempts on successful login."""
        if email in self._login_attempts:
            del self._login_attempts[email]

    def _rate_limit_check(self, email: str) -> bool:
        """Check if user is rate limited - for testing purposes."""
        return self._check_rate_limit(email) is None

    def track_login_attempt(self, email: str, success: bool, method: str = "email_password") -> None:
        """Track login attempts for monitoring and analytics."""
        logger.info(
            "Login attempt tracked",
            email=email,
            success=success,
            method=method,
            timestamp=datetime.now().isoformat()
        )
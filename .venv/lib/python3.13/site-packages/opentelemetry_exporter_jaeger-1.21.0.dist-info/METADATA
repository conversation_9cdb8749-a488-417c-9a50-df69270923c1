Metadata-Version: 2.1
Name: opentelemetry-exporter-jaeger
Version: 1.21.0
Summary: Jaeger Exporters for OpenTelemetry
Project-URL: Homepage, https://github.com/open-telemetry/opentelemetry-python/tree/main/exporter/opentelemetry-exporter-jaeger
Author-email: OpenTelemetry Authors <<EMAIL>>
License-Expression: Apache-2.0
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Typing :: Typed
Requires-Python: >=3.7
Requires-Dist: opentelemetry-exporter-jaeger-proto-grpc==1.21.0
Requires-Dist: opentelemetry-exporter-jaeger-thrift==1.21.0
Provides-Extra: test
Description-Content-Type: text/x-rst

OpenTelemetry Jaeger Exporter
=============================

.. warning::
    Since v1.35, the Jaeger supports OTLP natively. Please use the OTLP exporter instead.
    Support for this exporter will end July 2023.

    This package is no longer being tested.

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-exporter-jaeger.svg
   :target: https://pypi.org/project/opentelemetry-exporter-jaeger/

This library is provided as a convenience to install all supported Jaeger Exporters. Currently it installs:
* opentelemetry-exporter-jaeger-proto-grpc
* opentelemetry-exporter-jaeger-thrift

To avoid unnecessary dependencies, users should install the specific package once they've determined their
preferred serialization method.

Installation
------------

::

    pip install opentelemetry-exporter-jaeger


References
----------

* `OpenTelemetry Jaeger Exporter <https://opentelemetry-python.readthedocs.io/en/latest/exporter/jaeger/jaeger.html>`_
* `Jaeger <https://www.jaegertracing.io/>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_

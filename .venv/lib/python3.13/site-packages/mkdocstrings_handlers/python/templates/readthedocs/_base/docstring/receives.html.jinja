{#- Template for "Receives" sections in docstrings.

This template renders a list of documented received values (generators) in the format
specified with the [`docstring_section_style`][] configuration option.

Context:
  section (griffe.DocstringSectionAttributes): The section to render.
-#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
  {{ log.debug() }}
{% endblock %}

{# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
{% import "language"|get_template as lang with context %}
{#- Language module providing the `t` translation method. -#}

<table class="field-list">
  <colgroup>
    <col class="field-name" />
    <col class="field-body" />
  </colgroup>
  <tbody valign="top">
    <tr class="field">
      <th class="field-name">{{ section.title or lang.t("Receives:") }}</th>
      <td class="field-body">
        <ul class="first simple">
          {% for receives in section.value %}
            <li>
              {% if receives.name %}<b><code>{{ receives.name }}</code></b>{% endif %}
              {% if receives.annotation %}
                {% with expression = receives.annotation, backlink_type = "received-by" %}
                  {% if receives.name %}({% endif %}
                  {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
                  <code>{% include "expression"|get_template with context %}</code>
                  {% if receives.name %}){% endif %}
                {% endwith %}
              {% endif %}
              –
              <div class="doc-md-description">
                {{ receives.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
              </div>
            </li>
          {% endfor %}
        </ul>
      </td>
    </tr>
  </tbody>
</table>

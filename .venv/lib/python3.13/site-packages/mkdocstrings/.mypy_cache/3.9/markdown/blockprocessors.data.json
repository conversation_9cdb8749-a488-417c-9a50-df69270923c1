{".class": "MypyFile", "_fullname": "markdown.blockprocessors", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BlockParser": {".class": "SymbolTableNode", "cross_ref": "markdown.blockparser.BlockParser", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BlockProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.BlockProcessor", "name": "BlockProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.BlockProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.BlockProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "arg_types": ["markdown.blockprocessors.BlockProcessor", "markdown.blockparser.BlockParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BlockProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.BlockProcessor.detab", "name": "detab", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "length"], "arg_types": ["markdown.blockprocessors.BlockProcessor", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detab of BlockProcessor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lastChild": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.BlockProcessor.lastChild", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "arg_types": ["markdown.blockprocessors.BlockProcessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "last<PERSON><PERSON>d of BlockProcessor", "ret_type": {".class": "UnionType", "items": ["xml.etree.ElementTree.Element", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "looseDetab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.BlockProcessor.looseDetab", "name": "looseDetab", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "level"], "arg_types": ["markdown.blockprocessors.BlockProcessor", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "looseDetab of BlockProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.blockprocessors.BlockProcessor.parser", "name": "parser", "type": "markdown.blockparser.BlockParser"}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "blocks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.BlockProcessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "blocks"], "arg_types": ["markdown.blockprocessors.BlockProcessor", "xml.etree.ElementTree.Element", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of BlockProcessor", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tab_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.blockprocessors.BlockProcessor.tab_length", "name": "tab_length", "type": "builtins.int"}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "block"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.BlockProcessor.test", "name": "test", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "block"], "arg_types": ["markdown.blockprocessors.BlockProcessor", "xml.etree.ElementTree.Element", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test of BlockProcessor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.BlockProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.BlockProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BlockQuoteProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.BlockProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.BlockQuoteProcessor", "name": "BlockQuoteProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.BlockQuoteProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.BlockQuoteProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable", "RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.blockprocessors.BlockQuoteProcessor.RE", "name": "RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "clean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.BlockQuoteProcessor.clean", "name": "clean", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["markdown.blockprocessors.BlockQuoteProcessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clean of BlockQuoteProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.BlockQuoteProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.BlockQuoteProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CodeBlockProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.BlockProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.CodeBlockProcessor", "name": "CodeBlockProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.CodeBlockProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.CodeBlockProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.CodeBlockProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.CodeBlockProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Element": {".class": "SymbolTableNode", "cross_ref": "xml.etree.ElementTree.Element", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EmptyBlockProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.BlockProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.EmptyBlockProcessor", "name": "EmptyBlockProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.EmptyBlockProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.EmptyBlockProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.EmptyBlockProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.EmptyBlockProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HRProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.BlockProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.HRProcessor", "name": "HRProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.HRProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.HRProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable", "RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.blockprocessors.HRProcessor.RE", "name": "RE", "type": "builtins.str"}}, "SEARCH_RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.blockprocessors.HRProcessor.SEARCH_RE", "name": "SEARCH_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.blockprocessors.HRProcessor.match", "name": "match", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.HRProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.HRProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HashHeaderProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.BlockProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.HashHeaderProcessor", "name": "HashHeaderProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.HashHeaderProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.HashHeaderProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable", "RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.blockprocessors.HashHeaderProcessor.RE", "name": "RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.HashHeaderProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.HashHeaderProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListIndentProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.BlockProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.ListIndentProcessor", "name": "ListIndentProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.ListIndentProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.ListIndentProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable", "INDENT_RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.blockprocessors.ListIndentProcessor.INDENT_RE", "name": "INDENT_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "ITEM_TYPES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.blockprocessors.ListIndentProcessor.ITEM_TYPES", "name": "ITEM_TYPES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "LIST_TYPES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.blockprocessors.ListIndentProcessor.LIST_TYPES", "name": "LIST_TYPES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.ListIndentProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "arg_types": ["markdown.blockprocessors.ListIndentProcessor", "markdown.blockparser.BlockParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListIndentProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_item": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "block"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.ListIndentProcessor.create_item", "name": "create_item", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "block"], "arg_types": ["markdown.blockprocessors.ListIndentProcessor", "xml.etree.ElementTree.Element", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_item of ListIndentProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "block"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.ListIndentProcessor.get_level", "name": "get_level", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "block"], "arg_types": ["markdown.blockprocessors.ListIndentProcessor", "xml.etree.ElementTree.Element", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_level of ListIndentProcessor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "xml.etree.ElementTree.Element"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.ListIndentProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.ListIndentProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Markdown": {".class": "SymbolTableNode", "cross_ref": "markdown.core.Markdown", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Match": {".class": "SymbolTableNode", "cross_ref": "re.Match", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OListProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.BlockProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.OListProcessor", "name": "OListProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.OListProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.OListProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable", "CHILD_RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.blockprocessors.OListProcessor.CHILD_RE", "name": "CHILD_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "INDENT_RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.blockprocessors.OListProcessor.INDENT_RE", "name": "INDENT_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "LAZY_OL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.blockprocessors.OListProcessor.LAZY_OL", "name": "LAZY_OL", "type": "builtins.bool"}}, "RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.blockprocessors.OListProcessor.RE", "name": "RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "SIBLING_TAGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.blockprocessors.OListProcessor.SIBLING_TAGS", "name": "SIBLING_TAGS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "STARTSWITH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.blockprocessors.OListProcessor.STARTSWITH", "name": "STARTSWITH", "type": "builtins.str"}}, "TAG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.blockprocessors.OListProcessor.TAG", "name": "TAG", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.OListProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "arg_types": ["markdown.blockprocessors.OListProcessor", "markdown.blockparser.BlockParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OListProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "block"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.OListProcessor.get_items", "name": "get_items", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "block"], "arg_types": ["markdown.blockprocessors.OListProcessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_items of OListProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.OListProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.OListProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParagraphProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.BlockProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.ParagraphProcessor", "name": "ParagraphProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.ParagraphProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.ParagraphProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.ParagraphProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.ParagraphProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReferenceProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.BlockProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.ReferenceProcessor", "name": "ReferenceProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.ReferenceProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.ReferenceProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable", "RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.blockprocessors.ReferenceProcessor.RE", "name": "RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.ReferenceProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.ReferenceProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SetextHeaderProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.BlockProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.SetextHeaderProcessor", "name": "SetextHeaderProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.SetextHeaderProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.SetextHeaderProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable", "RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.blockprocessors.SetextHeaderProcessor.RE", "name": "RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.SetextHeaderProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.SetextHeaderProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UListProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.OListProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.blockprocessors.UListProcessor", "name": "UListProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.UListProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.blockprocessors", "mro": ["markdown.blockprocessors.UListProcessor", "markdown.blockprocessors.OListProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.UListProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "arg_types": ["markdown.blockprocessors.UListProcessor", "markdown.blockparser.BlockParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UListProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.blockprocessors.UListProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.blockprocessors.UListProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.blockprocessors.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.blockprocessors.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.blockprocessors.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.blockprocessors.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.blockprocessors.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.blockprocessors.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "build_block_parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["md", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.blockprocessors.build_block_parser", "name": "build_block_parser", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["md", "kwargs"], "arg_types": ["markdown.core.Markdown", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_block_parser", "ret_type": "markdown.blockparser.BlockParser", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.blockprocessors.logger", "name": "logger", "type": "logging.Logger"}}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/markdown-stubs/blockprocessors.pyi"}
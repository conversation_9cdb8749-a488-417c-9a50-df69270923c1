{#- Template for "Yields" sections in docstrings.

This template renders a list of documented yielded values (generators) in the format
specified with the [`docstring_section_style`][] configuration option.

Context:
  section (griffe.DocstringSectionAttributes): The section to render.
-#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
  {{ log.debug("Rendering yields section") }}
{% endblock logs %}

{# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
{% import "language"|get_template as lang with context %}
{#- Language module providing the `t` translation method. -#}

{% if config.docstring_section_style == "table" %}
  {% block table_style scoped %}
    {#- Block for the `table` section style. -#}
    {% set name_column = section.value|selectattr("name")|any %}
    <p><span class="doc-section-title">{{ section.title or lang.t("Yields:") }}</span></p>
    <table>
      <thead>
        <tr>
          {% if name_column %}<th>{{ lang.t("Name") }}</th>{% endif %}
          <th>{{ lang.t("Type") }}</th>
          <th>{{ lang.t("Description") }}</th>
        </tr>
      </thead>
      <tbody>
        {% for yields in section.value %}
          <tr class="doc-section-item">
            {% if name_column %}<td>{% if yields.name %}<code>{{ yields.name }}</code>{% endif %}</td>{% endif %}
            <td>
              {% if yields.annotation %}
                {% with expression = yields.annotation, backlink_type = "yielded-by" %}
                  {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
                  <code>{% include "expression"|get_template with context %}</code>
                {% endwith %}
              {% endif %}
            </td>
            <td>
              <div class="doc-md-description">
                {{ yields.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
              </div>
            </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {% endblock table_style %}
{% elif config.docstring_section_style == "list" %}
  {% block list_style scoped %}
    {#- Block for the `list` section style. -#}
    <p><span class="doc-section-title">{{ section.title or lang.t("Yields:") }}</span></p>
    <ul>
      {% for yields in section.value %}
        <li class="doc-section-item field-body">
          {% if yields.name %}<b><code>{{ yields.name }}</code></b>{% endif %}
          {% if yields.annotation %}
            {% with expression = yields.annotation, backlink_type = "yielded-by" %}
              {% if yields.name %} ({% endif %}
              {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
              <code>{% include "expression"|get_template with context %}</code>
              {% if yields.name %}){% endif %}
            {% endwith %}
          {% endif %}
          –
          <div class="doc-md-description">
            {{ yields.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
          </div>
        </li>
      {% endfor %}
    </ul>
  {% endblock list_style %}
{% elif config.docstring_section_style == "spacy" %}
  {% block spacy_style scoped %}
    {#- Block for the `spacy` section style. -#}
    <table>
      <thead>
        <tr>
          <th><span class="doc-section-title">{{ (section.title or lang.t("YIELDS")).rstrip(":").upper() }}</span></th>
          <th><span>{{ lang.t("DESCRIPTION") }}</span></th>
        </tr>
      </thead>
      <tbody>
        {% for yields in section.value %}
          <tr class="doc-section-item">
            <td>
              {% if yields.name %}
                <code>{{ yields.name }}</code>
              {% elif yields.annotation %}
                <span class="doc-yields-annotation">
                  {% with expression = yields.annotation, backlink_type = "yielded-by" %}
                    {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
                    <code>{% include "expression"|get_template with context %}</code>
                  {% endwith %}
                </span>
              {% endif %}
            </td>
            <td class="doc-yields-details">
              <div class="doc-md-description">
                {{ yields.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
              </div>
              {% if yields.name and yields.annotation %}
                <p>
                  <span class="doc-yields-annotation">
                    <b>{{ lang.t("TYPE:") }}:</b>
                    {% with expression = yields.annotation, backlink_type = "yielded-by" %}
                      {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
                      <code>{% include "expression"|get_template with context %}</code>
                    {% endwith %}
                  </span>
                </p>
              {% endif %}
            </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {% endblock spacy_style %}
{% endif %}
{".class": "MypyFile", "_fullname": "mkdocs.utils.yaml", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ConfigDirPlaceholder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mkdocs.utils.yaml._DirPlaceholder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.utils.yaml.ConfigDirPlaceholder", "name": "ConfigDirPlaceholder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml.ConfigDirPlaceholder", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocs.utils.yaml", "mro": ["mkdocs.utils.yaml.ConfigDirPlaceholder", "mkdocs.utils.yaml._DirPlaceholder", "os.PathLike", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml.ConfigDirPlaceholder.value", "name": "value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.utils.yaml.ConfigDirPlaceholder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "value of ConfigDirPlaceholder", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.utils.yaml.ConfigDirPlaceholder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.utils.yaml.ConfigDirPlaceholder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DocsDirPlaceholder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mkdocs.utils.yaml._DirPlaceholder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.utils.yaml.DocsDirPlaceholder", "name": "DocsDirPlaceholder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml.DocsDirPlaceholder", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocs.utils.yaml", "mro": ["mkdocs.utils.yaml.DocsDirPlaceholder", "mkdocs.utils.yaml._DirPlaceholder", "os.PathLike", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml.DocsDirPlaceholder.value", "name": "value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.utils.yaml.DocsDirPlaceholder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "value of DocsDirPlaceholder", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.utils.yaml.DocsDirPlaceholder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.utils.yaml.DocsDirPlaceholder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "MkDocsConfig": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.defaults.MkDocsConfig", "kind": "Gdef"}, "RelativeDirPlaceholder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mkdocs.utils.yaml._DirPlaceholder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.utils.yaml.RelativeDirPlaceholder", "name": "RelativeDirPlaceholder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml.RelativeDirPlaceholder", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocs.utils.yaml", "mro": ["mkdocs.utils.yaml.RelativeDirPlaceholder", "mkdocs.utils.yaml._DirPlaceholder", "os.PathLike", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml.RelativeDirPlaceholder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "suffix"], "arg_types": ["mkdocs.utils.yaml.RelativeDirPlaceholder", "mkdocs.config.defaults.MkDocsConfig", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RelativeDirPlaceholder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml.RelativeDirPlaceholder.value", "name": "value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.utils.yaml.RelativeDirPlaceholder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "value of RelativeDirPlaceholder", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.utils.yaml.RelativeDirPlaceholder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.utils.yaml.RelativeDirPlaceholder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "_DirPlaceholder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.utils.yaml._DirPlaceholder", "name": "_DirPlaceholder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml._DirPlaceholder", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocs.utils.yaml", "mro": ["mkdocs.utils.yaml._DirPlaceholder", "os.PathLike", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__fspath__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml._DirPlaceholder.__fspath__", "name": "__fspath__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.utils.yaml._DirPlaceholder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__fspath__ of _DirPlaceholder", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml._DirPlaceholder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "suffix"], "arg_types": ["mkdocs.utils.yaml._DirPlaceholder", "mkdocs.config.defaults.MkDocsConfig", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _DirPlaceholder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml._DirPlaceholder.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["mkdocs.utils.yaml._DirPlaceholder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of _DirPlaceholder", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocs.utils.yaml._DirPlaceholder.config", "name": "config", "type": "mkdocs.config.defaults.MkDocsConfig"}}, "suffix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocs.utils.yaml._DirPlaceholder.suffix", "name": "suffix", "type": "builtins.str"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml._DirPlaceholder.value", "name": "value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.utils.yaml._DirPlaceholder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "value of _DirPlaceholder", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.utils.yaml._DirPlaceholder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.utils.yaml._DirPlaceholder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.yaml.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.yaml.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.yaml.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.yaml.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.yaml.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.yaml.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_construct_dir_placeholder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["config", "loader", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml._construct_dir_placeholder", "name": "_construct_dir_placeholder", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["config", "loader", "node"], "arg_types": ["mkdocs.config.defaults.MkDocsConfig", "yaml.loader.BaseLoader", "yaml.nodes.ScalarNode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_construct_dir_placeholder", "ret_type": "mkdocs.utils.yaml._DirPlaceholder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "exceptions": {".class": "SymbolTableNode", "cross_ref": "mkdocs.exceptions", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_yaml_loader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["loader", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml.get_yaml_loader", "name": "get_yaml_loader", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["loader", "config"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["mkdocs.config.defaults.MkDocsConfig", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_yaml_loader", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mkdocs.utils.yaml.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "mergedeep": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mkdocs.utils.yaml.mergedeep", "name": "mergedeep", "type": {".class": "AnyType", "missing_import_name": "mkdocs.utils.yaml.mergedeep", "source_any": null, "type_of_any": 3}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "yaml": {".class": "SymbolTableNode", "cross_ref": "yaml", "kind": "Gdef"}, "yaml_env_tag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mkdocs.utils.yaml.yaml_env_tag", "name": "yaml_env_tag", "type": {".class": "AnyType", "missing_import_name": "mkdocs.utils.yaml.yaml_env_tag", "source_any": null, "type_of_any": 3}}}, "yaml_load": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["source", "loader"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.yaml.yaml_load", "name": "yaml_load", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["source", "loader"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "yaml.loader.BaseLoader"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "yaml_load", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/utils/yaml.py"}
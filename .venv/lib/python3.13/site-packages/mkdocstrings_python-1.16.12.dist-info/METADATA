Metadata-Version: 2.4
Name: mkdocstrings-python
Version: 1.16.12
Summary: A Python handler for mkdocstrings.
Author-Email: =?utf-8?q?Timoth=C3=A9e_Mazzucotelli?= <<EMAIL>>
License-Expression: ISC
License-File: LICENSE
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Topic :: Documentation
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Documentation
Classifier: Topic :: Utilities
Classifier: Typing :: Typed
Project-URL: Homepage, https://mkdocstrings.github.io/python
Project-URL: Documentation, https://mkdocstrings.github.io/python
Project-URL: Changelog, https://mkdocstrings.github.io/python/changelog
Project-URL: Repository, https://github.com/mkdocstrings/python
Project-URL: Issues, https://github.com/mkdocstrings/python/issues
Project-URL: Discussions, https://github.com/mkdocstrings/python/discussions
Project-URL: Gitter, https://gitter.im/mkdocstrings/python
Project-URL: Funding, https://github.com/sponsors/pawamoy
Requires-Python: >=3.9
Requires-Dist: mkdocstrings>=0.28.3
Requires-Dist: mkdocs-autorefs>=1.4
Requires-Dist: griffe>=1.6.2
Requires-Dist: typing-extensions>=4.0; python_version < "3.11"
Description-Content-Type: text/markdown

<h1 align="center">mkdocstrings-python</h1>

<p align="center">A Python handler for <a href="https://github.com/mkdocstrings/mkdocstrings"><i>mkdocstrings</i></a>.</p>

[![ci](https://github.com/mkdocstrings/python/workflows/ci/badge.svg)](https://github.com/mkdocstrings/python/actions?query=workflow%3Aci)
[![documentation](https://img.shields.io/badge/docs-mkdocs-708FCC.svg?style=flat)](https://mkdocstrings.github.io/python/)
[![pypi version](https://img.shields.io/pypi/v/mkdocstrings-python.svg)](https://pypi.org/project/mkdocstrings-python/)
[![gitter](https://badges.gitter.im/join%20chat.svg)](https://app.gitter.im/#/room/#python:gitter.im)

---

<p align="center"><img src="logo.png"></p>

The Python handler uses [Griffe](https://mkdocstrings.github.io/griffe)
to collect documentation from Python source code.
The word "griffe" can sometimes be used instead of "signature" in French.
Griffe is able to visit the Abstract Syntax Tree (AST) of the source code to extract useful information.
It is also able to execute the code (by importing it) and introspect objects in memory
when source code is not available. Finally, it can parse docstrings following different styles.

## Installation

You can install this handler as a *mkdocstrings* extra:

```toml title="pyproject.toml"
# PEP 621 dependencies declaration
# adapt to your dependencies manager
[project]
dependencies = [
    "mkdocstrings[python]>=0.18",
]
```

You can also explicitly depend on the handler:

```toml title="pyproject.toml"
# PEP 621 dependencies declaration
# adapt to your dependencies manager
[project]
dependencies = [
    "mkdocstrings-python",
]
```

## Preview

<!-- TODO: update the GIF with a more recent screen capture. Maybe use mp4 instead -->
![mkdocstrings_python_gif](https://user-images.githubusercontent.com/3999221/77157838-7184db80-6aa2-11ea-9f9a-fe77405202de.gif)

## Features

- **Data collection from source code**: collection of the object-tree and the docstrings is done thanks to
  [Griffe](https://github.com/mkdocstrings/griffe).

- **Support for type annotations:** Griffe collects your type annotations and *mkdocstrings* uses them
  to display parameter types or return types. It is even able to automatically add cross-references
  to other objects from your API, from the standard library or third-party libraries!
  See [how to load inventories](https://mkdocstrings.github.io/usage/#cross-references-to-other-projects-inventories) to enable it.

- **Recursive documentation of Python objects:** just use the module dotted-path as an identifier, and you get the full
  module docs. You don't need to inject documentation for each class, function, etc.

- **Support for documented attributes:** attributes (variables) followed by a docstring (triple-quoted string) will
  be recognized by Griffe in modules, classes and even in `__init__` methods.

- **Multiple docstring-styles support:** common support for Google-style, Numpydoc-style,
  and Sphinx-style docstrings. See [Griffe's documentation](https://mkdocstrings.github.io/griffe/docstrings/) on docstrings support.

- **Admonition support in Google docstrings:** blocks like `Note:` or `Warning:` will be transformed
  to their [admonition](https://squidfunk.github.io/mkdocs-material/reference/admonitions/) equivalent.
  *We do not support nested admonitions in docstrings!*

- **Every object has a TOC entry:** we render a heading for each object, meaning *MkDocs* picks them into the Table
  of Contents, which is nicely displayed by the Material theme. Thanks to *mkdocstrings* cross-reference ability,
  you can reference other objects within your docstrings, with the classic Markdown syntax:
  `[this object][package.module.object]` or directly with `[package.module.object][]`

- **Source code display:** *mkdocstrings* can add a collapsible div containing the highlighted source code
  of the Python object.

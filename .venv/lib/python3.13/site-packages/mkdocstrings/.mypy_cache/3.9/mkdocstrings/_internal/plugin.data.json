{".class": "MypyFile", "_fullname": "mkdocstrings._internal.plugin", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutorefsConfig": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.plugin.AutorefsConfig", "kind": "Gdef"}, "AutorefsPlugin": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.plugin.AutorefsPlugin", "kind": "Gdef"}, "BaseHandler": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.BaseHandler", "kind": "Gdef"}, "BasePlugin": {".class": "SymbolTableNode", "cross_ref": "mkdocs.plugins.BasePlugin", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.base.Config", "kind": "Gdef"}, "Environment": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Environment", "kind": "Gdef"}, "Handlers": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.Handlers", "kind": "Gdef"}, "MkDocsConfig": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.defaults.MkDocsConfig", "kind": "Gdef"}, "MkdocstringsExtension": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.extension.MkdocstringsExtension", "kind": "Gdef"}, "MkdocstringsPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["mkdocstrings._internal.plugin.PluginConfig"], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin", "name": "MkdocstringsPlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocstrings._internal.plugin", "mro": ["mkdocstrings._internal.plugin.MkdocstringsPlugin", "mkdocs.plugins.BasePlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocstrings._internal.plugin.MkdocstringsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MkdocstringsPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handlers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin._handlers", "name": "_handlers", "type": {".class": "UnionType", "items": ["mkdocstrings._internal.handlers.base.Handlers", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "css_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.css_filename", "name": "css_filename", "type": "builtins.str"}}, "get_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "handler_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.get_handler", "name": "get_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "handler_name"], "arg_types": ["mkdocstrings._internal.plugin.MkdocstringsPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_handler of MkdocstringsPlugin", "ret_type": "mkdocstrings._internal.handlers.base.BaseHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.handlers", "name": "handlers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocstrings._internal.plugin.MkdocstringsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handlers of MkdocstringsPlugin", "ret_type": "mkdocstrings._internal.handlers.base.Handlers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.handlers", "name": "handlers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocstrings._internal.plugin.MkdocstringsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handlers of MkdocstringsPlugin", "ret_type": "mkdocstrings._internal.handlers.base.Handlers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "inventory_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.inventory_enabled", "name": "inventory_enabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocstrings._internal.plugin.MkdocstringsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inventory_enabled of MkdocstringsPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.inventory_enabled", "name": "inventory_enabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocstrings._internal.plugin.MkdocstringsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inventory_enabled of MkdocstringsPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.on_config", "name": "on_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["mkdocstrings._internal.plugin.MkdocstringsPlugin", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_config of MkdocstringsPlugin", "ret_type": {".class": "UnionType", "items": ["mkdocs.config.defaults.MkDocsConfig", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "env", "config", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.on_env", "name": "on_env", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "env", "config", "args", "kwargs"], "arg_types": ["mkdocstrings._internal.plugin.MkdocstringsPlugin", "jinja2.environment.Environment", "mkdocs.config.defaults.MkDocsConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_env of MkdocstringsPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_post_build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.on_post_build", "name": "on_post_build", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["mkdocstrings._internal.plugin.MkdocstringsPlugin", "mkdocs.config.defaults.MkDocsConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_post_build of MkdocstringsPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.plugin_enabled", "name": "plugin_enabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocstrings._internal.plugin.MkdocstringsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plugin_enabled of MkdocstringsPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.plugin_enabled", "name": "plugin_enabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocstrings._internal.plugin.MkdocstringsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plugin_enabled of MkdocstringsPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.plugin.MkdocstringsPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.plugin.MkdocstringsPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PluginConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mkdocs.config.base.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.plugin.PluginConfig", "name": "PluginConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.plugin.PluginConfig", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocstrings._internal.plugin", "mro": ["mkdocstrings._internal.plugin.PluginConfig", "mkdocs.config.base.Config", "collections.UserDict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "custom_templates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocstrings._internal.plugin.PluginConfig.custom_templates", "name": "custom_templates", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "default_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocstrings._internal.plugin.PluginConfig.default_handler", "name": "default_handler", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Type"}}}, "enable_inventory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocstrings._internal.plugin.PluginConfig.enable_inventory", "name": "enable_inventory", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocstrings._internal.plugin.PluginConfig.enabled", "name": "enabled", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Type"}}}, "handlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocstrings._internal.plugin.PluginConfig.handlers", "name": "handlers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Type"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.plugin.PluginConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.plugin.PluginConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.plugin.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.plugin.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.plugin.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.plugin.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.plugin.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.plugin.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mkdocstrings._internal.plugin._logger", "name": "_logger", "type": "mkdocstrings._internal.loggers.LoggerAdapter"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "catch_warnings": {".class": "SymbolTableNode", "cross_ref": "warnings.catch_warnings", "kind": "Gdef"}, "get_logger": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.loggers.get_logger", "kind": "Gdef"}, "opt": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.config_options", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "simplefilter": {".class": "SymbolTableNode", "cross_ref": "warnings.simplefilter", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "write_file": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils.write_file", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/_internal/plugin.py"}
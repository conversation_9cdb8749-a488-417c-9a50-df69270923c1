{#- Macro for Japanese translations. -#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
{% endblock logs %}

{% macro t(key) %}{{ {
  "ATTRIBUTE": "属性",
  "Attributes:": "属性：",
  "Classes:": "クラス：",
  "CLASS": "クラス",
  "DEFAULT:": "デフォルト：",
  "Default": "デフォルト",
  "default:": "デフォルト：",
  "DESCRIPTION": "デスクリプション",
  "Description": "デスクリプション",
  "Examples:": "例：",
  "Functions:": "関数：",
  "FUNCTION": "関数",
  "Methods:": "メソッド：",
  "METHOD": "メソッド",
  "Modules:": "モジュール：",
  "MODULE": "モジュール",
  "Name": "名前",
  "Other Parameters:": "他の引数：",
  "PARAMETER": "引数",
  "Parameters:": "引数：",
  "RAISES": "発生",
  "Raises:" : "発生：",
  "RECEIVES": "取得",
  "Receives:": "取得：",
  "required": "必須",
  "RETURNS": "戻り値",
  "Returns:": "戻り値：",
  "Source code in": "ソースコード位置：",
  "TYPE:": "タイプ：",
  "Type": "タイプ",
  "WARNS": "警告",
  "Warns:": "警告：",
  "YIELDS": "返す",
  "Yields:": "返す：",
}[key] }}{% endmacro %}

{"data_mtime": 1741470542, "dep_lines": [3, 10, 8, 1, 3, 4, 5, 13, 195, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 10, 10, 25, 20, 5, 30, 30], "dependencies": ["collections.abc", "markupsafe._native", "markupsafe._speedups", "__future__", "collections", "string", "typing", "typing_extensions", "html", "builtins", "_frozen_importlib", "abc"], "hash": "5a75567f30d1393e8371b4225d1115fdf5b58810", "id": "markupsafe", "ignore_all": true, "interface_hash": "d37d127297fa7c1f7707a1df1ac4c668810fc619", "mtime": 1731325761, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/markupsafe/__init__.py", "plugin_data": null, "size": 13214, "suppressed": [], "version_id": "1.15.0"}
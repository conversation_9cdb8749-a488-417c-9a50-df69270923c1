{".class": "MypyFile", "_fullname": "wsgiref.simple_server", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BaseHTTPRequestHandler": {".class": "SymbolTableNode", "cross_ref": "http.server.BaseHTTPRequestHandler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ErrorStream": {".class": "SymbolTableNode", "cross_ref": "_typeshed.wsgi.ErrorStream", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HTTPServer": {".class": "SymbolTableNode", "cross_ref": "http.server.HTTPServer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ServerHandler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wsgiref.handlers.SimpleHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wsgiref.simple_server.ServerHandler", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wsgiref.simple_server.ServerHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wsgiref.simple_server", "mro": ["wsgiref.simple_server.ServerHandler", "wsgiref.handlers.SimpleHandler", "wsgiref.handlers.BaseHandler", "builtins.object"], "names": {".class": "SymbolTable", "server_software": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wsgiref.simple_server.ServerHandler.server_software", "name": "server_software", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server.ServerHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wsgiref.simple_server.ServerHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleHandler": {".class": "SymbolTableNode", "cross_ref": "wsgiref.handlers.SimpleHandler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StartResponse": {".class": "SymbolTableNode", "cross_ref": "_typeshed.wsgi.StartResponse", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WSGIApplication": {".class": "SymbolTableNode", "cross_ref": "_typeshed.wsgi.WSGIApplication", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WSGIEnvironment": {".class": "SymbolTableNode", "cross_ref": "_typeshed.wsgi.WSGIEnvironment", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WSGIRequestHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.server.BaseHTTPRequestHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wsgiref.simple_server.WSGIRequestHandler", "name": "WSGIRequestHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wsgiref.simple_server.WSGIRequestHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wsgiref.simple_server", "mro": ["wsgiref.simple_server.WSGIRequestHandler", "http.server.BaseHTTPRequestHandler", "socketserver.StreamRequestHandler", "socketserver.BaseRequestHandler", "builtins.object"], "names": {".class": "SymbolTable", "get_environ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsgiref.simple_server.WSGIRequestHandler.get_environ", "name": "get_environ", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wsgiref.simple_server.WSGIRequestHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_environ of WSGIRequestHandler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_stderr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsgiref.simple_server.WSGIRequestHandler.get_stderr", "name": "get_stderr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wsgiref.simple_server.WSGIRequestHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stderr of WSGIRequestHandler", "ret_type": "_typeshed.wsgi.ErrorStream", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "server_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wsgiref.simple_server.WSGIRequestHandler.server_version", "name": "server_version", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server.WSGIRequestHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wsgiref.simple_server.WSGIRequestHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WSGIServer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.server.HTTPServer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wsgiref.simple_server.WSGIServer", "name": "WSGIServer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wsgiref.simple_server.WSGIServer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wsgiref.simple_server", "mro": ["wsgiref.simple_server.WSGIServer", "http.server.HTTPServer", "socketserver.TCPServer", "socketserver.BaseServer", "builtins.object"], "names": {".class": "SymbolTable", "application": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wsgiref.simple_server.WSGIServer.application", "name": "application", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "base_environ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wsgiref.simple_server.WSGIServer.base_environ", "name": "base_environ", "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}}}, "get_app": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsgiref.simple_server.WSGIServer.get_app", "name": "get_app", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wsgiref.simple_server.WSGIServer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_app of WSGIServer", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_app": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "application"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsgiref.simple_server.WSGIServer.set_app", "name": "set_app", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "application"], "arg_types": ["wsgiref.simple_server.WSGIServer", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_app of WSGIServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setup_environ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsgiref.simple_server.WSGIServer.setup_environ", "name": "setup_environ", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wsgiref.simple_server.WSGIServer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_environ of WSGIServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server.WSGIServer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wsgiref.simple_server.WSGIServer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_S": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server._S", "name": "_S", "upper_bound": "wsgiref.simple_server.WSGIServer", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "wsgiref.simple_server.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsgiref.simple_server.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsgiref.simple_server.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsgiref.simple_server.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsgiref.simple_server.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsgiref.simple_server.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsgiref.simple_server.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "demo_app": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["environ", "start_response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wsgiref.simple_server.demo_app", "name": "demo_app", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["environ", "start_response"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIEnvironment"}, "_typeshed.wsgi.StartResponse"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "demo_app", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_server": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "wsgiref.simple_server.make_server", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["host", "port", "app", "handler_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "wsgiref.simple_server.make_server", "name": "make_server", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["host", "port", "app", "handler_class"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, {".class": "TypeType", "item": "wsgiref.simple_server.WSGIRequestHandler"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_server", "ret_type": "wsgiref.simple_server.WSGIServer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "wsgiref.simple_server.make_server", "name": "make_server", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["host", "port", "app", "handler_class"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, {".class": "TypeType", "item": "wsgiref.simple_server.WSGIRequestHandler"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_server", "ret_type": "wsgiref.simple_server.WSGIServer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["host", "port", "app", "server_class", "handler_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "wsgiref.simple_server.make_server", "name": "make_server", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["host", "port", "app", "server_class", "handler_class"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server._S", "id": -1, "name": "_S", "namespace": "wsgiref.simple_server.make_server", "upper_bound": "wsgiref.simple_server.WSGIServer", "values": [], "variance": 0}}, {".class": "TypeType", "item": "wsgiref.simple_server.WSGIRequestHandler"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_server", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server._S", "id": -1, "name": "_S", "namespace": "wsgiref.simple_server.make_server", "upper_bound": "wsgiref.simple_server.WSGIServer", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server._S", "id": -1, "name": "_S", "namespace": "wsgiref.simple_server.make_server", "upper_bound": "wsgiref.simple_server.WSGIServer", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "wsgiref.simple_server.make_server", "name": "make_server", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["host", "port", "app", "server_class", "handler_class"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server._S", "id": -1, "name": "_S", "namespace": "wsgiref.simple_server.make_server", "upper_bound": "wsgiref.simple_server.WSGIServer", "values": [], "variance": 0}}, {".class": "TypeType", "item": "wsgiref.simple_server.WSGIRequestHandler"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_server", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server._S", "id": -1, "name": "_S", "namespace": "wsgiref.simple_server.make_server", "upper_bound": "wsgiref.simple_server.WSGIServer", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server._S", "id": -1, "name": "_S", "namespace": "wsgiref.simple_server.make_server", "upper_bound": "wsgiref.simple_server.WSGIServer", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["host", "port", "app", "handler_class"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, {".class": "TypeType", "item": "wsgiref.simple_server.WSGIRequestHandler"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_server", "ret_type": "wsgiref.simple_server.WSGIServer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["host", "port", "app", "server_class", "handler_class"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.wsgi.WSGIApplication"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server._S", "id": -1, "name": "_S", "namespace": "wsgiref.simple_server.make_server", "upper_bound": "wsgiref.simple_server.WSGIServer", "values": [], "variance": 0}}, {".class": "TypeType", "item": "wsgiref.simple_server.WSGIRequestHandler"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_server", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server._S", "id": -1, "name": "_S", "namespace": "wsgiref.simple_server.make_server", "upper_bound": "wsgiref.simple_server.WSGIServer", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wsgiref.simple_server._S", "id": -1, "name": "_S", "namespace": "wsgiref.simple_server.make_server", "upper_bound": "wsgiref.simple_server.WSGIServer", "values": [], "variance": 0}]}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "server_version": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsgiref.simple_server.server_version", "name": "server_version", "type": "builtins.str"}}, "software_version": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsgiref.simple_server.software_version", "name": "software_version", "type": "builtins.str"}}, "sys_version": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wsgiref.simple_server.sys_version", "name": "sys_version", "type": "builtins.str"}}}, "path": "/home/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/wsgiref/simple_server.pyi"}
{".class": "MypyFile", "_fullname": "html.parser", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "HTMLParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_markupbase.ParserBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "html.parser.HTMLParser", "name": "HTMLParser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "html.parser", "mro": ["html.parser.HTMLParser", "_markupbase.ParserBase", "builtins.object"], "names": {".class": "SymbolTable", "CDATA_CONTENT_ELEMENTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "html.parser.HTMLParser.CDATA_CONTENT_ELEMENTS", "name": "CDATA_CONTENT_ELEMENTS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "convert_charrefs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "convert_charrefs"], "arg_types": ["html.parser.HTMLParser", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cdata_elem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "html.parser.HTMLParser.cdata_elem", "name": "cdata_elem", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "check_for_whole_start_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.check_for_whole_start_tag", "name": "check_for_whole_start_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "i"], "arg_types": ["html.parser.HTMLParser", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_for_whole_start_tag of HTMLParser", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_cdata_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.clear_cdata_mode", "name": "clear_cdata_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["html.parser.HTMLParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_cdata_mode of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["html.parser.HTMLParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_charrefs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "html.parser.HTMLParser.convert_charrefs", "name": "convert_charrefs", "type": "builtins.bool"}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["html.parser.HTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_starttag_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.get_starttag_text", "name": "get_starttag_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["html.parser.HTMLParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_starttag_text of HTMLParser", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "goahead": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.goahead", "name": "goahead", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "end"], "arg_types": ["html.parser.HTMLParser", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "goahead of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_charref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.handle_charref", "name": "handle_charref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["html.parser.HTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_charref of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.handle_comment", "name": "handle_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["html.parser.HTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_comment of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.handle_data", "name": "handle_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["html.parser.HTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_data of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_decl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "decl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.handle_decl", "name": "handle_decl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "decl"], "arg_types": ["html.parser.HTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_decl of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_endtag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.handle_endtag", "name": "handle_endtag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["html.parser.HTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_endtag of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_entityref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.handle_entityref", "name": "handle_entityref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["html.parser.HTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_entityref of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_pi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.handle_pi", "name": "handle_pi", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["html.parser.HTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_pi of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_startendtag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.handle_startendtag", "name": "handle_startendtag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag", "attrs"], "arg_types": ["html.parser.HTMLParser", "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_startendtag of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_starttag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.handle_starttag", "name": "handle_starttag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag", "attrs"], "arg_types": ["html.parser.HTMLParser", "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_starttag of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "interesting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "html.parser.HTMLParser.interesting", "name": "interesting", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "lasttag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "html.parser.HTMLParser.lasttag", "name": "lasttag", "type": "builtins.str"}}, "parse_bogus_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "i", "report"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.parse_bogus_comment", "name": "parse_bogus_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "i", "report"], "arg_types": ["html.parser.HTMLParser", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_bogus_comment of HTMLParser", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_endtag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.parse_endtag", "name": "parse_endtag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "i"], "arg_types": ["html.parser.HTMLParser", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_endtag of HTMLParser", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_html_declaration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.parse_html_declaration", "name": "parse_html_declaration", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "i"], "arg_types": ["html.parser.HTMLParser", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_html_declaration of HTMLParser", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_pi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.parse_pi", "name": "parse_pi", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "i"], "arg_types": ["html.parser.HTMLParser", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_pi of HTMLParser", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_starttag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.parse_starttag", "name": "parse_starttag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "i"], "arg_types": ["html.parser.HTMLParser", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_starttag of HTMLParser", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rawdata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "html.parser.HTMLParser.rawdata", "name": "rawdata", "type": "builtins.str"}}, "set_cdata_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "elem"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "html.parser.HTMLParser.set_cdata_mode", "name": "set_cdata_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "elem"], "arg_types": ["html.parser.HTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_cdata_mode of HTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "html.parser.HTMLParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "html.parser.HTMLParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParserBase": {".class": "SymbolTableNode", "cross_ref": "_markupbase.ParserBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "html.parser.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.parser.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.parser.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.parser.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.parser.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.parser.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.parser.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/home/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/html/parser.pyi"}
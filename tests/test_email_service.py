"""Test suite for email service module."""

import pytest
import os
from unittest.mock import AsyncMock, patch, MagicMock

from src.infrastructure.email_service import send_email_async


class TestEmailService:
    """Test cases for email service functionality."""

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_success(self, mock_send):
        """Test successful email sending."""
        mock_send.return_value = None
        
        await send_email_async(
            subject="Test Subject",
            recipient="<EMAIL>",
            body="Test email body"
        )
        
        # Verify aiosmtplib.send was called
        mock_send.assert_called_once()
        
        # Verify the call arguments
        call_args = mock_send.call_args
        message = call_args[0][0]
        kwargs = call_args[1]
        
        assert message["Subject"] == "Test Subject"
        assert message["To"] == "<EMAIL>"
        assert message["From"] == "<EMAIL>"
        assert kwargs["hostname"] == "smtp.gmail.com"
        assert kwargs["port"] == 587
        assert kwargs["start_tls"] is True
        assert kwargs["username"] == "<EMAIL>"
        assert kwargs["password"] == "test_password"

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_smtp_error(self, mock_send):
        """Test email sending with SMTP error."""
        mock_send.side_effect = Exception("SMTP connection failed")
        
        with pytest.raises(Exception, match="SMTP connection failed"):
            await send_email_async(
                subject="Test Subject",
                recipient="<EMAIL>",
                body="Test email body"
            )

    @pytest.mark.asyncio
    @patch.dict(os.environ, {}, clear=True)
    async def test_send_email_no_gmail_user(self):
        """Test email sending when GMAIL_USER is not set."""
        with pytest.raises(RuntimeError, match="GMAIL_USER environment variable is not set"):
            await send_email_async(
                subject="Test Subject",
                recipient="<EMAIL>",
                body="Test email body"
            )

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_empty_gmail_user(self, mock_send):
        """Test email sending when GMAIL_USER is empty."""
        with pytest.raises(RuntimeError, match="GMAIL_USER environment variable is not set"):
            await send_email_async(
                subject="Test Subject",
                recipient="<EMAIL>",
                body="Test email body"
            )

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': ''
    })
    async def test_send_email_empty_gmail_pass(self, mock_send):
        """Test email sending when GMAIL_PASS is empty."""
        mock_send.return_value = None
        
        # Should still attempt to send (password validation happens at SMTP level)
        await send_email_async(
            subject="Test Subject",
            recipient="<EMAIL>",
            body="Test email body"
        )
        
        mock_send.assert_called_once()

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch('src.infrastructure.email_service.logger')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_logging_success(self, mock_logger, mock_send):
        """Test that successful email sending is logged."""
        mock_send.return_value = None
        
        await send_email_async(
            subject="Test Subject",
            recipient="<EMAIL>",
            body="Test email body"
        )
        
        # Verify success logging
        mock_logger.info.assert_called_with(
            "Email sent",
            to="<EMAIL>",
            subject="Test Subject"
        )

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch('src.infrastructure.email_service.logger')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_logging_error(self, mock_logger, mock_send):
        """Test that email sending errors are logged."""
        error_message = "SMTP authentication failed"
        mock_send.side_effect = Exception(error_message)
        
        with pytest.raises(Exception):
            await send_email_async(
                subject="Test Subject",
                recipient="<EMAIL>",
                body="Test email body"
            )
        
        # Verify error logging
        mock_logger.error.assert_called_with(
            "Failed to send email",
            error=error_message,
            to="<EMAIL>"
        )

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_special_characters(self, mock_send):
        """Test email sending with special characters in content."""
        mock_send.return_value = None
        
        special_subject = "Test Subject with émojis 🚀 and spëcial chars"
        special_body = "Body with ñoñó characters and 中文 text"
        
        await send_email_async(
            subject=special_subject,
            recipient="<EMAIL>",
            body=special_body
        )
        
        mock_send.assert_called_once()
        
        # Verify the message content
        call_args = mock_send.call_args
        message = call_args[0][0]
        assert message["Subject"] == special_subject

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_long_content(self, mock_send):
        """Test email sending with very long content."""
        mock_send.return_value = None
        
        long_subject = "A" * 1000  # Very long subject
        long_body = "B" * 10000   # Very long body
        
        await send_email_async(
            subject=long_subject,
            recipient="<EMAIL>",
            body=long_body
        )
        
        mock_send.assert_called_once()

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_multiple_recipients_format(self, mock_send):
        """Test email sending with different recipient formats."""
        mock_send.return_value = None
        
        recipients = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for recipient in recipients:
            await send_email_async(
                subject="Test Subject",
                recipient=recipient,
                body="Test body"
            )
        
        assert mock_send.call_count == len(recipients)

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_concurrent(self, mock_send):
        """Test concurrent email sending."""
        import asyncio
        
        mock_send.return_value = None
        
        # Send multiple emails concurrently
        tasks = [
            send_email_async(
                subject=f"Test Subject {i}",
                recipient=f"recipient{i}@example.com",
                body=f"Test body {i}"
            )
            for i in range(5)
        ]
        
        await asyncio.gather(*tasks)
        
        assert mock_send.call_count == 5

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_timeout_error(self, mock_send):
        """Test email sending with timeout error."""
        import asyncio
        
        mock_send.side_effect = asyncio.TimeoutError("Connection timeout")
        
        with pytest.raises(asyncio.TimeoutError):
            await send_email_async(
                subject="Test Subject",
                recipient="<EMAIL>",
                body="Test body"
            )

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_connection_error(self, mock_send):
        """Test email sending with connection error."""
        mock_send.side_effect = ConnectionError("Cannot connect to SMTP server")
        
        with pytest.raises(ConnectionError):
            await send_email_async(
                subject="Test Subject",
                recipient="<EMAIL>",
                body="Test body"
            )

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_authentication_error(self, mock_send):
        """Test email sending with authentication error."""
        from aiosmtplib import SMTPAuthenticationError
        from src.shared.errors.exceptions import IntegrationError

        mock_send.side_effect = SMTPAuthenticationError(535, "Authentication failed")

        with pytest.raises(IntegrationError) as exc_info:
            await send_email_async(
                subject="Test Subject",
                recipient="<EMAIL>",
                body="Test body"
            )

        assert "Authentication failed" in str(exc_info.value)
        assert exc_info.value.service_name == "email_service"

    @pytest.mark.asyncio
    @patch('src.infrastructure.email_service.aiosmtplib.send')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    async def test_send_email_message_structure(self, mock_send):
        """Test that email message is properly structured."""
        mock_send.return_value = None
        
        await send_email_async(
            subject="Test Subject",
            recipient="<EMAIL>",
            body="Test email body with\nmultiple lines\nand formatting"
        )
        
        # Verify message structure
        call_args = mock_send.call_args
        message = call_args[0][0]
        
        # Check headers
        assert "From" in message
        assert "To" in message
        assert "Subject" in message
        
        # Check content
        content = message.get_content()
        assert "Test email body with" in content
        assert "multiple lines" in content
        assert "and formatting" in content


class TestEmailServiceConfiguration:
    """Test cases for email service configuration."""

    @patch('src.infrastructure.email_service.logger')
    @patch.dict(os.environ, {}, clear=True)
    def test_missing_credentials_warning(self, mock_logger):
        """Test that missing credentials trigger a warning."""
        from src.infrastructure.email_service import get_email_credentials

        # Call the function to trigger the warning
        get_email_credentials()

        # Verify warning was logged
        mock_logger.warning.assert_called_with(
            "Gmail credentials not set in environment variables."
        )

    @patch('src.infrastructure.email_service.logger')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': ''
    })
    def test_partial_credentials_warning(self, mock_logger):
        """Test that partial credentials trigger a warning."""
        from src.infrastructure.email_service import get_email_credentials

        # Call the function to trigger the warning
        get_email_credentials()

        # Verify warning was logged
        mock_logger.warning.assert_called_with(
            "Gmail credentials not set in environment variables."
        )

    @patch('src.infrastructure.email_service.logger')
    @patch.dict(os.environ, {
        'GMAIL_USER': '<EMAIL>',
        'GMAIL_PASS': 'test_password'
    })
    def test_complete_credentials_no_warning(self, mock_logger):
        """Test that complete credentials don't trigger a warning."""
        # Import the module
        import importlib
        import src.infrastructure.email_service
        importlib.reload(src.infrastructure.email_service)
        
        # Verify no warning was logged
        mock_logger.warning.assert_not_called()
{".class": "MypyFile", "_fullname": "pathspec", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "GitIgnorePattern": {".class": "SymbolTableNode", "cross_ref": "pathspec.patterns.gitwildmatch.GitIgnorePattern", "kind": "Gdef", "module_public": false}, "GitIgnoreSpec": {".class": "SymbolTableNode", "cross_ref": "pathspec.gitignore.GitIgnoreSpec", "kind": "Gdef"}, "PathSpec": {".class": "SymbolTableNode", "cross_ref": "pathspec.pathspec.PathSpec", "kind": "Gdef"}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "pathspec.pattern.Pattern", "kind": "Gdef"}, "RecursionError": {".class": "SymbolTableNode", "cross_ref": "pathspec.util.RecursionError", "kind": "Gdef"}, "RegexPattern": {".class": "SymbolTableNode", "cross_ref": "pathspec.pattern.RegexPattern", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pathspec.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "cross_ref": "pathspec._meta.__author__", "kind": "Gdef"}, "__copyright__": {".class": "SymbolTableNode", "cross_ref": "pathspec._meta.__copyright__", "kind": "Gdef"}, "__credits__": {".class": "SymbolTableNode", "cross_ref": "pathspec._meta.__credits__", "kind": "Gdef"}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.__file__", "name": "__file__", "type": "builtins.str"}}, "__license__": {".class": "SymbolTableNode", "cross_ref": "pathspec._meta.__license__", "kind": "Gdef"}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "pathspec._meta.__version__", "kind": "Gdef"}, "iter_tree": {".class": "SymbolTableNode", "cross_ref": "pathspec.util.iter_tree", "kind": "Gdef"}, "lookup_pattern": {".class": "SymbolTableNode", "cross_ref": "pathspec.util.lookup_pattern", "kind": "Gdef"}, "match_files": {".class": "SymbolTableNode", "cross_ref": "pathspec.util.match_files", "kind": "Gdef"}, "patterns": {".class": "SymbolTableNode", "cross_ref": "pathspec.patterns", "kind": "Gdef", "module_public": false}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/pathspec/__init__.py"}
{#- Template for Python classes.

This template renders a Python class.

Context:
  class (griffe.Class): The class to render.
  root (bool): Whether this is the root object, injected with `:::` in a Markdown page.
  heading_level (int): The HTML heading level to use.
  config (dict): The configuration options.
-#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
  {{ log.debug("Rendering " + class.path) }}
{% endblock logs %}

<div class="doc doc-object doc-class">
  {% with obj = class, html_id = class.path, all_members = class.all_members %}

    {% if root %}
      {% set show_full_path = config.show_root_full_path %}
      {% set root_members = True %}
    {% elif root_members %}
      {% set show_full_path = config.show_root_members_full_path or config.show_object_full_path %}
      {% set root_members = False %}
    {% else %}
      {% set show_full_path = config.show_object_full_path %}
    {% endif %}

    {% set class_name = class.path if show_full_path else class.name %}

    {% if not root or config.show_root_heading %}
      {% filter heading(
          heading_level,
          role="class",
          id=html_id,
          class="doc doc-heading",
          toc_label=('<code class="doc-symbol doc-symbol-toc doc-symbol-class"></code>&nbsp;'|safe if config.show_symbol_type_toc else '') + (config.toc_label if config.toc_label and root else class.name),
        ) %}

        {% block heading scoped %}
          {#- Heading block.

          This block renders the heading for the class.
          -#}
          {% if config.show_symbol_type_heading %}<code class="doc-symbol doc-symbol-heading doc-symbol-class"></code>{% endif %}
          {% if config.heading and root %}
            {{ config.heading }}
          {% elif config.separate_signature %}
            <span class="doc doc-object-name doc-class-name">{{ class_name }}</span>
          {% elif config.merge_init_into_class and "__init__" in all_members %}
            {% with function = all_members["__init__"] %}
              {%+ filter highlight(language="python", inline=True) -%}
                {#- YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. -#}
                {{ class_name }}{% include "signature"|get_template with context %}
              {%- endfilter %}
            {% endwith %}
          {% else %}
            <code>{{ class_name }}</code>
          {% endif %}
        {% endblock heading %}

        {% block labels scoped %}
          {#- Labels block.

          This block renders the labels for the class.
          -#}
          {% with labels = class.labels %}
            {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
            {% include "labels"|get_template with context %}
          {% endwith %}
        {% endblock labels %}

      {% endfilter %}

      {% block signature scoped %}
        {#- Signature block.

        This block renders the signature for the class.
        Overloads of the `__init__` method are rendered if `merge_init_into_class` is enabled.
        The actual `__init__` method signature is only rendered if `separate_signature` is also enabled.
        -#}
        {% if config.merge_init_into_class %}
          {% if "__init__" in all_members %}
            {% with function = all_members["__init__"] %}
              {% if function.overloads and config.show_overloads %}
                <div class="doc-overloads">
                  {% for overload in function.overloads %}
                    {% filter format_signature(overload, config.line_length, annotations=True, crossrefs=config.signature_crossrefs) %}
                      {{ class.name }}
                    {% endfilter %}
                  {% endfor %}
                </div>
              {% endif %}
              {% if config.separate_signature %}
                {% filter format_signature(function, config.line_length, crossrefs=config.signature_crossrefs) %}
                  {{ class.name }}
                {% endfilter %}
              {% endif %}
            {% endwith %}
          {% endif %}
        {% endif %}
      {% endblock signature %}

    {% else %}
      {% if config.show_root_toc_entry %}
        {% filter heading(heading_level,
            role="class",
            id=html_id,
            toc_label=('<code class="doc-symbol doc-symbol-toc doc-symbol-class"></code>&nbsp;'|safe if config.show_symbol_type_toc else '') + (config.toc_label if config.toc_label and root else class.name),
            hidden=True,
          ) %}
        {% endfilter %}
      {% endif %}
      {% set heading_level = heading_level - 1 %}
    {% endif %}

    <div class="doc doc-contents {% if root %}first{% endif %}">
      {% block contents scoped %}
        {#- Contents block.

        This block renders the contents of the class.
        It contains other blocks that users can override.
        Overriding the contents block allows to rearrange the order of the blocks.
        -#}
        {% block bases scoped %}
          {#- Class bases block.

          This block renders the bases for the class.
          -#}
          {% if config.show_bases and class.bases %}
            <p class="doc doc-class-bases">
              Bases: {% for expression in class.bases -%}
                <code>
                  {%- with backlink_type = "subclassed-by" -%}
                    {#- YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. -#}
                    {%- include "expression"|get_template with context -%}
                  {%- endwith -%}
                </code>{% if not loop.last %}, {% endif %}
              {% endfor -%}
            </p>
          {% endif %}
        {% endblock bases %}

        {% block docstring scoped %}
          {#- Docstring block.

          This block renders the docstring for the class.
          -#}
          {% with docstring_sections = class.docstring.parsed %}
            {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
            {% include "docstring"|get_template with context %}
          {% endwith %}
          {% if config.merge_init_into_class %}
            {# We don't want to merge the inherited `__init__` method docstring into the class docstring #}
            {# if such inherited method was not selected through `inherited_members`. #}
            {% with check_members = all_members if (config.inherited_members is true or (config.inherited_members is iterable and "__init__" in config.inherited_members)) else class.members %}
              {% if "__init__" in check_members and check_members["__init__"].has_docstring %}
                {% with function = check_members["__init__"] %}
                  {% with obj = function, docstring_sections = function.docstring.parsed %}
                    {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
                    {% include "docstring"|get_template with context %}
                  {% endwith %}
                {% endwith %}
              {% endif %}
            {% endwith %}
          {% endif %}
        {% endblock docstring %}

        {% if config.backlinks %}
          <backlinks identifier="{{ html_id }}" handler="python" />
        {% endif %}

        {% block summary scoped %}
          {#- Summary block.

          This block renders auto-summaries for classes, methods, and attributes.
          -#}
          {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
          {% include "summary"|get_template with context %}
        {% endblock summary %}

        {% block source scoped %}
          {#- Source block.

          This block renders the source code for the class.
          -#}
          {% if config.show_source %}
            {% if config.merge_init_into_class %}
              {% if "__init__" in all_members and all_members["__init__"].source %}
                {% with init = all_members["__init__"] %}
                  <details class="quote">
                    <summary>Source code in <code>
                      {%- if init.relative_filepath.is_absolute() -%}
                        {{ init.relative_package_filepath }}
                      {%- else -%}
                        {{ init.relative_filepath }}
                      {%- endif -%}
                    </code></summary>
                    {{ init.source|highlight(language="python", linestart=init.lineno or 0, linenums=True) }}
                  </details>
                {% endwith %}
              {% endif %}
            {% elif class.source %}
              <details class="quote">
                <summary>Source code in <code>
                  {%- if class.relative_filepath.is_absolute() -%}
                    {{ class.relative_package_filepath }}
                  {%- else -%}
                    {{ class.relative_filepath }}
                  {%- endif -%}
                </code></summary>
                {{ class.source|highlight(language="python", linestart=class.lineno or 0, linenums=True) }}
              </details>
            {% endif %}
          {% endif %}
        {% endblock source %}

        {% block children scoped %}
          {#- Children block.

          This block renders the children (members) of the class.
          -#}
          {% set root = False %}
          {% set heading_level = heading_level + 1 %}
          {# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
          {% include "children"|get_template with context %}
        {% endblock children %}
      {% endblock contents %}
    </div>

  {% endwith %}
</div>

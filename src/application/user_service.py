# FILE: src/application/user_service.py

from typing import Optional
import structlog
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.domain.user import User
from src.shared.security import hash_password
from src.shared.errors.exceptions import BusinessRuleViolation

logger = structlog.get_logger()

class UserService:
    """Service for managing user-related operations with enterprise error handling."""

    async def register_user(
        self,
        db: AsyncSession,
        email: str,
        password: str,
        is_google_account: bool = False,
    ) -> User:
        """
        Register a new user, handling potential duplicate emails gracefully.
        """
        logger.info("Attempting to register new user", email=email)
        hashed_password = hash_password(password)
        
        new_user = User(
            email=email,
            hashed_password=hashed_password,
            is_google_account=is_google_account,
        )
        
        db.add(new_user)
        
        try:
            await db.commit()
            await db.refresh(new_user)
            logger.info("User registered successfully", user_id=new_user.id, email=email)
            return new_user
        except IntegrityError:
            await db.rollback()  # Important: rollback the failed transaction
            logger.warning("User registration failed due to duplicate email", email=email)
            # Create a BusinessRuleViolation as expected by tests
            from src.shared.errors.exceptions import ErrorContext
            raise BusinessRuleViolation(
                rule="unique_email",
                message=f"A user with the email '{email}' already exists.",
                error_code="EMAIL_ALREADY_EXISTS",
                context=ErrorContext(
                    correlation_id=f"user_service_{email}",
                    operation="user_registration",
                    metadata={"email": email}
                ),
                recovery_actions=["Try logging in", "Use password reset"]
            )

    async def get_user_by_email(self, db: AsyncSession, email: str) -> Optional[User]:
        """Retrieve a user by their email address."""
        logger.debug("Fetching user by email", email=email)
        result = await db.execute(select(User).where(User.email == email))
        user = result.scalars().first()
        if user:
            logger.debug("User found by email", user_id=user.id, email=email)
        else:
            logger.debug("User not found by email", email=email)
        return user

    async def get_user_by_id(self, db: AsyncSession, user_id: int) -> Optional[User]:
        """Retrieve a user by their ID."""
        logger.debug("Fetching user by id", user_id=user_id)
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()
        if user:
            logger.debug("User found by id", user_id=user_id)
        else:
            logger.debug("User not found by id", user_id=user_id)
        return user
{"data_mtime": 1741470548, "dep_lines": [17, 18, 20, 36, 41, 45, 46, 49, 60, 61, 62, 940, 5, 6, 8, 9, 10, 13, 15, 17, 58, 864, 1286, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 20, 20, 10, 10, 10, 5, 5, 5, 5, 20, 25, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["jinja2.nodes", "jinja2.compiler", "jinja2.defaults", "jinja2.exceptions", "jinja2.lexer", "jinja2.parser", "jinja2.runtime", "jinja2.utils", "jinja2.bccache", "jinja2.ext", "jinja2.loaders", "jinja2.debug", "os", "typing", "weakref", "collections", "functools", "types", "markupsafe", "jinja2", "typing_extensions", "zipfile", "asyncio", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "jinja2.filters", "jinja2.tests", "jinja2.visitor"], "hash": "df489a1b858345c429c27557a927b1810d9ad24e", "id": "jinja2.environment", "ignore_all": true, "interface_hash": "5df757c04d6eb9b3538c91d2e6d472dd1e38113c", "mtime": 1734903670, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/jinja2/environment.py", "plugin_data": null, "size": 61513, "suppressed": [], "version_id": "1.15.0"}
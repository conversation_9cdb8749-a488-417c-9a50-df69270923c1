{".class": "MypyFile", "_fullname": "pathspec.patterns.gitwildmatch", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnyStr": {".class": "SymbolTableNode", "cross_ref": "typing.AnyStr", "kind": "Gdef"}, "GitIgnorePattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pathspec.patterns.gitwildmatch.GitWildMatchPattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pathspec.patterns.gitwildmatch.GitIgnorePattern", "name": "GitIgnorePattern", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pathspec.patterns.gitwildmatch.GitIgnorePattern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pathspec.patterns.gitwildmatch", "mro": ["pathspec.patterns.gitwildmatch.GitIgnorePattern", "pathspec.patterns.gitwildmatch.GitWildMatchPattern", "pathspec.pattern.RegexPattern", "pathspec.pattern.Pattern", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.patterns.gitwildmatch.GitIgnorePattern.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": ["pathspec.patterns.gitwildmatch.GitIgnorePattern", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GitIgnorePattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_deprecated": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pathspec.patterns.gitwildmatch.GitIgnorePattern._deprecated", "name": "_deprecated", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deprecated of GitIgnorePattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pathspec.patterns.gitwildmatch.GitIgnorePattern._deprecated", "name": "_deprecated", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deprecated of GitIgnorePattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pattern_to_regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pathspec.patterns.gitwildmatch.GitIgnorePattern.pattern_to_regex", "name": "pattern_to_regex", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pathspec.patterns.gitwildmatch.GitIgnorePattern.pattern_to_regex", "name": "pattern_to_regex", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kw"], "arg_types": [{".class": "TypeType", "item": "pathspec.patterns.gitwildmatch.GitIgnorePattern"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pattern_to_regex of GitIgnorePattern", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.patterns.gitwildmatch.GitIgnorePattern.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pathspec.patterns.gitwildmatch.GitIgnorePattern", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GitWildMatchPattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pathspec.pattern.RegexPattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPattern", "name": "GitWildMatchPattern", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPattern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pathspec.patterns.gitwildmatch", "mro": ["pathspec.patterns.gitwildmatch.GitWildMatchPattern", "pathspec.pattern.RegexPattern", "pathspec.pattern.Pattern", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_translate_segment_glob": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPattern._translate_segment_glob", "name": "_translate_segment_glob", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pattern"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_translate_segment_glob of GitWildMatchPattern", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPattern._translate_segment_glob", "name": "_translate_segment_glob", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pattern"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_translate_segment_glob of GitWildMatchPattern", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "escape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.escape", "name": "escape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.escape", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "escape of GitWildMatchPattern", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.escape", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.escape", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.escape", "name": "escape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.escape", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "escape of GitWildMatchPattern", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.escape", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.escape", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}}, "pattern_to_regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.pattern_to_regex", "name": "pattern_to_regex", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "pattern"], "arg_types": [{".class": "TypeType", "item": "pathspec.patterns.gitwildmatch.GitWildMatchPattern"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.pattern_to_regex", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pattern_to_regex of GitWildMatchPattern", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.pattern_to_regex", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.pattern_to_regex", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.pattern_to_regex", "name": "pattern_to_regex", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "pattern"], "arg_types": [{".class": "TypeType", "item": "pathspec.patterns.gitwildmatch.GitWildMatchPattern"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.pattern_to_regex", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pattern_to_regex of GitWildMatchPattern", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.pattern_to_regex", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.pattern_to_regex", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPattern.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pathspec.patterns.gitwildmatch.GitWildMatchPattern", "values": [], "variance": 0}, "slots": ["include", "pattern", "regex"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GitWildMatchPatternError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPatternError", "name": "GitWildMatchPatternError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPatternError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pathspec.patterns.gitwildmatch", "mro": ["pathspec.patterns.gitwildmatch.GitWildMatchPatternError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.patterns.gitwildmatch.GitWildMatchPatternError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pathspec.patterns.gitwildmatch.GitWildMatchPatternError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RegexPattern": {".class": "SymbolTableNode", "cross_ref": "pathspec.pattern.RegexPattern", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "_BYTES_ENCODING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pathspec.patterns.gitwildmatch._BYTES_ENCODING", "name": "_BYTES_ENCODING", "type": "builtins.str"}}, "_DIR_MARK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pathspec.patterns.gitwildmatch._DIR_MARK", "name": "_DIR_MARK", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.patterns.gitwildmatch.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.patterns.gitwildmatch.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.patterns.gitwildmatch.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.patterns.gitwildmatch.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.patterns.gitwildmatch.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.patterns.gitwildmatch.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "pathspec.util", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/pathspec/patterns/gitwildmatch.py"}
{"data_mtime": 1741470541, "dep_lines": [5, 1, 2, 3, 4, 6, 7, 8, 9, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "sys", "types", "_socket", "_typeshed", "io", "socket", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_io", "abc"], "hash": "7413e9d16e477d1d00b50edb6cc3f230a6d1d011", "id": "socketserver", "ignore_all": true, "interface_hash": "30b28a7b934886730612d0a96c3f97ce34a4a728", "mtime": 1741275134, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/socketserver.pyi", "plugin_data": null, "size": 6774, "suppressed": [], "version_id": "1.15.0"}
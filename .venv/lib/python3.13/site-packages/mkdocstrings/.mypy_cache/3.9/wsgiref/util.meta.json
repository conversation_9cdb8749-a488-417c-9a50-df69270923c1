{"data_mtime": 1741470541, "dep_lines": [2, 3, 1, 4, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 30, 30, 30], "dependencies": ["_typeshed.wsgi", "collections.abc", "sys", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "b31faa92ea12a1ff1b3a3f2ad2a72d88238c39ef", "id": "wsgiref.util", "ignore_all": true, "interface_hash": "c1b95759f5579a226f211997ca4a7f697fc042f6", "mtime": 1741275134, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/wsgiref/util.pyi", "plugin_data": null, "size": 1060, "suppressed": [], "version_id": "1.15.0"}
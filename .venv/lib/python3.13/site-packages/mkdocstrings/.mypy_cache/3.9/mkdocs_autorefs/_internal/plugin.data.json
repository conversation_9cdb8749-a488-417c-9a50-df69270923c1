{".class": "MypyFile", "_fullname": "mkdocs_autorefs._internal.plugin", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnchorLink": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.toc.AnchorLink", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutorefsConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mkdocs.config.base.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs_autorefs._internal.plugin.AutorefsConfig", "name": "AutorefsConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsConfig", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocs_autorefs._internal.plugin", "mro": ["mkdocs_autorefs._internal.plugin.AutorefsConfig", "mkdocs.config.base.Config", "collections.UserDict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "link_titles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsConfig.link_titles", "name": "link_titles", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "external"}], "uses_pep604_syntax": true}}}, "resolve_closest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsConfig.resolve_closest", "name": "resolve_closest", "type": "builtins.bool"}}, "strip_title_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsConfig.strip_title_tags", "name": "strip_title_tags", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs_autorefs._internal.plugin.AutorefsConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs_autorefs._internal.plugin.AutorefsConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutorefsExtension": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.AutorefsExtension", "kind": "Gdef"}, "AutorefsPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["mkdocs_autorefs._internal.plugin.AutorefsConfig"], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin", "name": "AutorefsPlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs_autorefs._internal.plugin", "mro": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "mkdocs.plugins.BasePlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AutorefsPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_abs_url_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._abs_url_map", "name": "_abs_url_map", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_backlink_page_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._backlink_page_map", "name": "_backlink_page_map", "type": {".class": "Instance", "args": ["builtins.str", "mkdocs.structure.pages.Page"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_backlinks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._backlinks", "name": "_backlinks", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_crumbs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_url", "backlink_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._crumbs", "name": "_crumbs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_url", "backlink_url"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_crumbs of AutorefsPlugin", "ret_type": "mkdocs_autorefs._internal.backlinks.Backlink", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_closest_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["from_url", "urls", "qualifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._get_closest_url", "name": "_get_closest_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["from_url", "urls", "qualifier"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_closest_url of AutorefsPlugin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._get_closest_url", "name": "_get_closest_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["from_url", "urls", "qualifier"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_closest_url of AutorefsPlugin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_fallback_anchor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._get_fallback_anchor", "name": "_get_fallback_anchor", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_get_item_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "identifier", "from_url", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._get_item_url", "name": "_get_item_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "identifier", "from_url", "fallback"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_item_url of AutorefsPlugin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_urls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._get_urls", "name": "_get_urls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "identifier"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_urls of AutorefsPlugin", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_link_titles": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._link_titles", "name": "_link_titles", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "external"}], "uses_pep604_syntax": true}}}, "_primary_url_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._primary_url_map", "name": "_primary_url_map", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_record_backlink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "identifier", "backlink_type", "backlink_anchor", "page_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._record_backlink", "name": "_record_backlink", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "identifier", "backlink_type", "backlink_anchor", "page_url"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_record_backlink of AutorefsPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_secondary_url_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._secondary_url_map", "name": "_secondary_url_map", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_strip_title_tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._strip_title_tags", "name": "_strip_title_tags", "type": "builtins.bool"}}, "_title_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._title_map", "name": "_title_map", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_url_to_page": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin._url_to_page", "name": "_url_to_page", "type": {".class": "Instance", "args": ["builtins.str", "mkdocs.structure.pages.Page"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "current_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.current_page", "name": "current_page", "type": {".class": "UnionType", "items": ["mkdocs.structure.pages.Page", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_backlinks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3], "arg_names": ["self", "identifiers", "from_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.get_backlinks", "name": "get_backlinks", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3], "arg_names": ["self", "identifiers", "from_url"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_backlinks of AutorefsPlugin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["mkdocs_autorefs._internal.backlinks.Backlink"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_fallback_anchor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.get_fallback_anchor", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.get_fallback_anchor", "name": "get_fallback_anchor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fallback_anchor of AutorefsPlugin", "ret_type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.get_fallback_anchor", "name": "get_fallback_anchor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fallback_anchor of AutorefsPlugin", "ret_type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.get_fallback_anchor", "name": "get_fallback_anchor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fallback_anchor of AutorefsPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "get_fallback_anchor", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fallback_anchor of AutorefsPlugin", "ret_type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_item_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "identifier", "from_url", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.get_item_url", "name": "get_item_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "identifier", "from_url", "fallback"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_item_url of AutorefsPlugin", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "legacy_refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.legacy_refs", "name": "legacy_refs", "type": "builtins.bool"}}, "map_urls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "page", "anchor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.map_urls", "name": "map_urls", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "page", "anchor"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "mkdocs.structure.pages.Page", "mkdocs.structure.toc.AnchorLink"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "map_urls of AutorefsPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.on_config", "name": "on_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_config of AutorefsPlugin", "ret_type": {".class": "UnionType", "items": ["mkdocs.config.defaults.MkDocsConfig", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.on_env", "name": "on_env", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "config", "files"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "jinja2.environment.Environment", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_env of AutorefsPlugin", "ret_type": "jinja2.environment.Environment", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.on_env", "name": "on_env", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "config", "files"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "jinja2.environment.Environment", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_env of AutorefsPlugin", "ret_type": "jinja2.environment.Environment", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_page_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "html", "page", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.on_page_content", "name": "on_page_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "html", "page", "kwargs"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "builtins.str", "mkdocs.structure.pages.Page", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_page_content of AutorefsPlugin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_page_markdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "markdown", "page", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.on_page_markdown", "name": "on_page_markdown", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "markdown", "page", "kwargs"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "builtins.str", "mkdocs.structure.pages.Page", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_page_markdown of AutorefsPlugin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "record_backlinks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.record_backlinks", "name": "record_backlinks", "type": "builtins.bool"}}, "register_anchor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5, 5], "arg_names": ["self", "page", "identifier", "anchor", "title", "primary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.register_anchor", "name": "register_anchor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5], "arg_names": ["self", "page", "identifier", "anchor", "title", "primary"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "mkdocs.structure.pages.Page", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_anchor of AutorefsPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "identifier", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.register_url", "name": "register_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "identifier", "url"], "arg_types": ["mkdocs_autorefs._internal.plugin.AutorefsPlugin", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_url of AutorefsPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scan_toc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.scan_toc", "name": "scan_toc", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs_autorefs._internal.plugin.AutorefsPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs_autorefs._internal.plugin.AutorefsPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Backlink": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.backlinks.Backlink", "kind": "Gdef"}, "BacklinkCrumb": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.backlinks.BacklinkCrumb", "kind": "Gdef"}, "BasePlugin": {".class": "SymbolTableNode", "cross_ref": "mkdocs.plugins.BasePlugin", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Choice": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.config_options.Choice", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.base.Config", "kind": "Gdef"}, "Environment": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Environment", "kind": "Gdef"}, "Files": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.files.Files", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MkDocsConfig": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.defaults.MkDocsConfig", "kind": "Gdef"}, "Page": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.pages.Page", "kind": "Gdef"}, "Section": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.nav.Section", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.config_options.Type", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "pathlib.PurePosixPath", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs._internal.plugin.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs._internal.plugin.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs._internal.plugin.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs._internal.plugin.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs._internal.plugin.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs._internal.plugin.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mkdocs_autorefs._internal.plugin._log", "name": "_log", "type": "mkdocs.plugins.PrefixedLogger"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "event_priority": {".class": "SymbolTableNode", "cross_ref": "mkdocs.plugins.event_priority", "kind": "Gdef"}, "fix_refs": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.fix_refs", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_plugin_logger": {".class": "SymbolTableNode", "cross_ref": "mkdocs.plugins.get_plugin_logger", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "relative_url": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.relative_url", "kind": "Gdef"}, "urlsplit": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlsplit", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs_autorefs/_internal/plugin.py"}
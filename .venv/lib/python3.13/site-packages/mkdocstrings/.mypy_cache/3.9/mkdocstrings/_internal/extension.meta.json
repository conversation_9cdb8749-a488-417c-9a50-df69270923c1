{"data_mtime": 1741470549, "dep_lines": [37, 28, 38, 31, 32, 33, 34, 35, 41, 23, 25, 26, 27, 30, 43, 44, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 5, 5, 10, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mkdocstrings._internal.handlers.base", "xml.etree.ElementTree", "mkdocstrings._internal.loggers", "jinja2.exceptions", "markdown.blockprocessors", "markdown.extensions", "markdown.treeprocessors", "mkdocs.exceptions", "collections.abc", "__future__", "re", "typing", "warnings", "yaml", "markdown", "mkdocs_autorefs", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "logging", "markdown.blockparser", "markdown.core", "markdown.util", "mkdocs", "mkdocs.plugins", "mkdocs_autorefs._internal", "mkdocs_autorefs._internal.plugin", "mkdocstrings._internal.handlers", "types", "xml", "xml.etree"], "hash": "eaa566ab878c2febce35063c2380caafdf500730", "id": "mkdocstrings._internal.extension", "ignore_all": true, "interface_hash": "cffdd7bff942e66ea6086daca68e133b4fc30852", "mtime": 1741453377, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/_internal/extension.py", "plugin_data": null, "size": 14772, "suppressed": [], "version_id": "1.15.0"}
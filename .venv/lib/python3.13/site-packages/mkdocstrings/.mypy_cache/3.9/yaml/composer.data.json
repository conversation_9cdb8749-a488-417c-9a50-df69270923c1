{".class": "MypyFile", "_fullname": "yaml.composer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Composer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.composer.Composer", "name": "Composer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.composer.Composer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.composer", "mro": ["yaml.composer.Composer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.composer.Composer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["yaml.composer.Composer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Composer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "anchors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.composer.Composer.anchors", "name": "anchors", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "yaml.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "check_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.composer.Composer.check_node", "name": "check_node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["yaml.composer.Composer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_node of Composer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compose_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.composer.Composer.compose_document", "name": "compose_document", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["yaml.composer.Composer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compose_document of Composer", "ret_type": {".class": "UnionType", "items": ["yaml.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compose_mapping_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "anchor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.composer.Composer.compose_mapping_node", "name": "compose_mapping_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "anchor"], "arg_types": ["yaml.composer.Composer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "yaml.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compose_mapping_node of Composer", "ret_type": "yaml.nodes.MappingNode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compose_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.composer.Composer.compose_node", "name": "compose_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "index"], "arg_types": ["yaml.composer.Composer", {".class": "UnionType", "items": ["yaml.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compose_node of Composer", "ret_type": {".class": "UnionType", "items": ["yaml.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compose_scalar_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "anchor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.composer.Composer.compose_scalar_node", "name": "compose_scalar_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "anchor"], "arg_types": ["yaml.composer.Composer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "yaml.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compose_scalar_node of Composer", "ret_type": "yaml.nodes.ScalarNode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compose_sequence_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "anchor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.composer.Composer.compose_sequence_node", "name": "compose_sequence_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "anchor"], "arg_types": ["yaml.composer.Composer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "yaml.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compose_sequence_node of Composer", "ret_type": "yaml.nodes.SequenceNode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.composer.Composer.get_node", "name": "get_node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["yaml.composer.Composer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_node of Composer", "ret_type": {".class": "UnionType", "items": ["yaml.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_single_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.composer.Composer.get_single_node", "name": "get_single_node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["yaml.composer.Composer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_single_node of Composer", "ret_type": {".class": "UnionType", "items": ["yaml.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.composer.Composer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.composer.Composer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ComposerError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.error.MarkedYAMLError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.composer.ComposerE<PERSON>r", "name": "Composer<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.composer.ComposerE<PERSON>r", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.composer", "mro": ["yaml.composer.ComposerE<PERSON>r", "yaml.error.MarkedYAMLError", "yaml.error.YAMLError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.composer.ComposerError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.composer.ComposerE<PERSON>r", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MappingNode": {".class": "SymbolTableNode", "cross_ref": "yaml.nodes.MappingNode", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MarkedYAMLError": {".class": "SymbolTableNode", "cross_ref": "yaml.error.MarkedYAMLError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Node": {".class": "SymbolTableNode", "cross_ref": "yaml.nodes.Node", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ScalarNode": {".class": "SymbolTableNode", "cross_ref": "yaml.nodes.ScalarNode", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SequenceNode": {".class": "SymbolTableNode", "cross_ref": "yaml.nodes.SequenceNode", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.composer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.composer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.composer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.composer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.composer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.composer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/yaml-stubs/composer.pyi"}
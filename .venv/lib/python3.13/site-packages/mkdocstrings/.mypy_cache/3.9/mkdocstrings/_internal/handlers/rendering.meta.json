{"data_mtime": 1741470545, "dep_lines": [11, 17, 10, 12, 3, 5, 6, 7, 8, 13, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14], "dep_prios": [5, 25, 5, 5, 5, 10, 10, 10, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["markdown.extensions.codehilite", "xml.etree.ElementTree", "markdown.extensions", "markdown.treeprocessors", "__future__", "copy", "re", "textwrap", "typing", "markupsafe", "markdown", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "markdown.core", "markdown.util", "typing_extensions", "xml", "xml.etree"], "hash": "209d7ea1473d5278fadff65805775b4d0a50fdbf", "id": "mkdocstrings._internal.handlers.rendering", "ignore_all": true, "interface_hash": "f956d0c8d1efd933b50f690e7fdd6b318c139e6c", "mtime": 1741454064, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/_internal/handlers/rendering.py", "plugin_data": null, "size": 12012, "suppressed": ["pymdownx.highlight"], "version_id": "1.15.0"}
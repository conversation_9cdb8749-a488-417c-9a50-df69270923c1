{".class": "MypyFile", "_fullname": "markdown.inlinepatterns", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AUTOLINK_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.AUTOLINK_RE", "name": "AUTOLINK_RE", "type": "builtins.str"}}, "AUTOMAIL_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.AUTOMAIL_RE", "name": "AUTOMAIL_RE", "type": "builtins.str"}}, "AsteriskProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.InlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.AsteriskProcessor", "name": "AsteriskProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.AsteriskProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.AsteriskProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable", "PATTERNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.inlinepatterns.AsteriskProcessor.PATTERNS", "name": "PATTERNS", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "markdown.inlinepatterns.EmStrongItem"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "build_double": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "m", "tags", "idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.AsteriskProcessor.build_double", "name": "build_double", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "m", "tags", "idx"], "arg_types": ["markdown.inlinepatterns.AsteriskProcessor", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_double of AsteriskProcessor", "ret_type": "xml.etree.ElementTree.Element", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_double2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "m", "tags", "idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.AsteriskProcessor.build_double2", "name": "build_double2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "m", "tags", "idx"], "arg_types": ["markdown.inlinepatterns.AsteriskProcessor", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_double2 of AsteriskProcessor", "ret_type": "xml.etree.ElementTree.Element", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "m", "builder", "tags", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.AsteriskProcessor.build_element", "name": "build_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "m", "builder", "tags", "index"], "arg_types": ["markdown.inlinepatterns.AsteriskProcessor", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_element of AsteriskProcessor", "ret_type": "xml.etree.ElementTree.Element", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_single": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "m", "tag", "idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.AsteriskProcessor.build_single", "name": "build_single", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "m", "tag", "idx"], "arg_types": ["markdown.inlinepatterns.AsteriskProcessor", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_single of AsteriskProcessor", "ret_type": "xml.etree.ElementTree.Element", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_sub_patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "data", "parent", "last", "idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.AsteriskProcessor.parse_sub_patterns", "name": "parse_sub_patterns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "data", "parent", "last", "idx"], "arg_types": ["markdown.inlinepatterns.AsteriskProcessor", "builtins.str", "xml.etree.ElementTree.Element", {".class": "UnionType", "items": ["xml.etree.ElementTree.Element", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_sub_patterns of AsteriskProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.AsteriskProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.AsteriskProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutolinkInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.InlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.AutolinkInlineProcessor", "name": "AutolinkInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.AutolinkInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.AutolinkInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.AutolinkInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.AutolinkInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutomailInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.InlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.AutomailInlineProcessor", "name": "AutomailInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.AutomailInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.AutomailInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.AutomailInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.AutomailInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BACKTICK_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.BACKTICK_RE", "name": "BACKTICK_RE", "type": "builtins.str"}}, "BacktickInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.InlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.BacktickInlineProcessor", "name": "BacktickInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.BacktickInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.BacktickInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable", "ESCAPED_BSLASH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.BacktickInlineProcessor.ESCAPED_BSLASH", "name": "ESCAPED_BSLASH", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.BacktickInlineProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pattern"], "arg_types": ["markdown.inlinepatterns.BacktickInlineProcessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BacktickInlineProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.BacktickInlineProcessor.tag", "name": "tag", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.BacktickInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.BacktickInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DoubleTagInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.SimpleTagInlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.DoubleTagInlineProcessor", "name": "DoubleTagInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.DoubleTagInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.DoubleTagInlineProcessor", "markdown.inlinepatterns.SimpleTagInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.DoubleTagInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.DoubleTagInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DoubleTagPattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.SimpleTagPattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.DoubleTagPattern", "name": "DoubleTagPattern", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.DoubleTagPattern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.DoubleTagPattern", "markdown.inlinepatterns.SimpleTagPattern", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.DoubleTagPattern.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.DoubleTagPattern", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EMPHASIS_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.EMPHASIS_RE", "name": "EMPHASIS_RE", "type": "builtins.str"}}, "EM_STRONG2_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.EM_STRONG2_RE", "name": "EM_STRONG2_RE", "type": "builtins.str"}}, "EM_STRONG_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.EM_STRONG_RE", "name": "EM_STRONG_RE", "type": "builtins.str"}}, "ENTITY_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.ENTITY_RE", "name": "ENTITY_RE", "type": "builtins.str"}}, "ESCAPE_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.ESCAPE_RE", "name": "ESCAPE_RE", "type": "builtins.str"}}, "Element": {".class": "SymbolTableNode", "cross_ref": "xml.etree.ElementTree.Element", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EmStrongItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.EmStrongItem", "name": "EmStrongItem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "markdown.inlinepatterns.EmStrongItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["pattern", "builder", "tags"]}}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.EmStrongItem", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.EmStrongItem.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.EmStrongItem.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "pattern", "builder", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "markdown.inlinepatterns.EmStrongItem.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "pattern", "builder", "tags"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of EmStrongItem", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.EmStrongItem._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of EmStrongItem", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.EmStrongItem._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.EmStrongItem._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.EmStrongItem._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "markdown.inlinepatterns.EmStrongItem._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of EmStrongItem", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "markdown.inlinepatterns.EmStrongItem._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of EmStrongItem", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "pattern", "builder", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.EmStrongItem._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "pattern", "builder", "tags"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of EmStrongItem", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem._NT", "id": -1, "name": "_NT", "namespace": "markdown.inlinepatterns.EmStrongItem._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.EmStrongItem._source", "name": "_source", "type": "builtins.str"}}, "builder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "markdown.inlinepatterns.EmStrongItem.builder", "name": "builder", "type": "builtins.str"}}, "builder-redefinition": {".class": "SymbolTableNode", "cross_ref": "markdown.inlinepatterns.EmStrongItem.builder", "kind": "<PERSON><PERSON><PERSON>"}, "pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "markdown.inlinepatterns.EmStrongItem.pattern", "name": "pattern", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "pattern-redefinition": {".class": "SymbolTableNode", "cross_ref": "markdown.inlinepatterns.EmStrongItem.pattern", "kind": "<PERSON><PERSON><PERSON>"}, "tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "markdown.inlinepatterns.EmStrongItem.tags", "name": "tags", "type": "builtins.str"}}, "tags-redefinition": {".class": "SymbolTableNode", "cross_ref": "markdown.inlinepatterns.EmStrongItem.tags", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EmStrongItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": "markdown.inlinepatterns.EmStrongItem"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "EscapeInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.InlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.EscapeInlineProcessor", "name": "EscapeInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.EscapeInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.EscapeInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.EscapeInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.EscapeInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTML_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.HTML_RE", "name": "HTML_RE", "type": "builtins.str"}}, "HtmlInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.InlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.HtmlInlineProcessor", "name": "HtmlInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.HtmlInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.HtmlInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable", "backslash_unescape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.HtmlInlineProcessor.backslash_unescape", "name": "backslash_unescape", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["markdown.inlinepatterns.HtmlInlineProcessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backslash_unescape of HtmlInlineProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unescape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.HtmlInlineProcessor.unescape", "name": "unescape", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["markdown.inlinepatterns.HtmlInlineProcessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unescape of HtmlInlineProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.HtmlInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.HtmlInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IMAGE_LINK_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.IMAGE_LINK_RE", "name": "IMAGE_LINK_RE", "type": "builtins.str"}}, "IMAGE_REFERENCE_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.IMAGE_REFERENCE_RE", "name": "IMAGE_REFERENCE_RE", "type": "builtins.str"}}, "ImageInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.LinkInlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.ImageInlineProcessor", "name": "ImageInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.ImageInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.ImageInlineProcessor", "markdown.inlinepatterns.LinkInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.ImageInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.ImageInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImageReferenceInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.ReferenceInlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.ImageReferenceInlineProcessor", "name": "ImageReferenceInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.ImageReferenceInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.ImageReferenceInlineProcessor", "markdown.inlinepatterns.ReferenceInlineProcessor", "markdown.inlinepatterns.LinkInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.ImageReferenceInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.ImageReferenceInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.Pattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.InlineProcessor", "name": "InlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.InlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "pattern", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.InlineProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "pattern", "md"], "arg_types": ["markdown.inlinepatterns.InlineProcessor", "builtins.str", {".class": "UnionType", "items": ["markdown.core.Markdown", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InlineProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handleMatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "m", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.InlineProcessor.handleMatch", "name": "handleMatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "m", "data"], "arg_types": ["markdown.inlinepatterns.InlineProcessor", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handleMatch of InlineProcessor", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["xml.etree.ElementTree.Element", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "safe_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.InlineProcessor.safe_mode", "name": "safe_mode", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.InlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.InlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LINE_BREAK_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.LINE_BREAK_RE", "name": "LINE_BREAK_RE", "type": "builtins.str"}}, "LINK_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.LINK_RE", "name": "LINK_RE", "type": "builtins.str"}}, "LinkInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.InlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.LinkInlineProcessor", "name": "LinkInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.LinkInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.LinkInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable", "RE_LINK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.inlinepatterns.LinkInlineProcessor.RE_LINK", "name": "RE_LINK", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_TITLE_CLEAN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.inlinepatterns.LinkInlineProcessor.RE_TITLE_CLEAN", "name": "RE_TITLE_CLEAN", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "getLink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.LinkInlineProcessor.getLink", "name": "getLink", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "index"], "arg_types": ["markdown.inlinepatterns.LinkInlineProcessor", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getLink of LinkInlineProcessor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getText": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.LinkInlineProcessor.getText", "name": "getText", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "index"], "arg_types": ["markdown.inlinepatterns.LinkInlineProcessor", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getText of LinkInlineProcessor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.LinkInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.LinkInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Markdown": {".class": "SymbolTableNode", "cross_ref": "markdown.core.Markdown", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NOIMG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.NOIMG", "name": "NOIMG", "type": "builtins.str"}}, "NOT_STRONG_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.NOT_STRONG_RE", "name": "NOT_STRONG_RE", "type": "builtins.str"}}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Pattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.Pattern", "name": "Pattern", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.Pattern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable", "ANCESTOR_EXCLUDES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.inlinepatterns.Pattern.ANCESTOR_EXCLUDES", "name": "ANCESTOR_EXCLUDES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "pattern", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.Pattern.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "pattern", "md"], "arg_types": ["markdown.inlinepatterns.Pattern", "builtins.str", {".class": "UnionType", "items": ["markdown.core.Markdown", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Pattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compiled_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.Pattern.compiled_re", "name": "compiled_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "getCompiledRegExp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.Pattern.getCompiledRegExp", "name": "getCompiledRegExp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["markdown.inlinepatterns.Pattern"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getCompiledRegExp of Pattern", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handleMatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.Pattern.handleMatch", "name": "handleMatch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "m"], "arg_types": ["markdown.inlinepatterns.Pattern", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handleMatch of Pattern", "ret_type": {".class": "UnionType", "items": ["builtins.str", "xml.etree.ElementTree.Element", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "md": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.Pattern.md", "name": "md", "type": "markdown.core.Markdown"}}, "pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.Pattern.pattern", "name": "pattern", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.Pattern.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["markdown.inlinepatterns.Pattern"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of Pattern", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unescape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.Pattern.unescape", "name": "unescape", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["markdown.inlinepatterns.Pattern", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unescape of Pattern", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.Pattern.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.Pattern", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "REFERENCE_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.REFERENCE_RE", "name": "REFERENCE_RE", "type": "builtins.str"}}, "ReferenceInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.LinkInlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.ReferenceInlineProcessor", "name": "ReferenceInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.ReferenceInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.ReferenceInlineProcessor", "markdown.inlinepatterns.LinkInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable", "NEWLINE_CLEANUP_RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.inlinepatterns.ReferenceInlineProcessor.NEWLINE_CLEANUP_RE", "name": "NEWLINE_CLEANUP_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "evalId": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "index", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.ReferenceInlineProcessor.evalId", "name": "evalId", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "index", "text"], "arg_types": ["markdown.inlinepatterns.ReferenceInlineProcessor", "builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "evalId of ReferenceInlineProcessor", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "makeTag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "href", "title", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.ReferenceInlineProcessor.makeTag", "name": "makeTag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "href", "title", "text"], "arg_types": ["markdown.inlinepatterns.ReferenceInlineProcessor", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makeTag of ReferenceInlineProcessor", "ret_type": "xml.etree.ElementTree.Element", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.ReferenceInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.ReferenceInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SMART_EMPHASIS_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.SMART_EMPHASIS_RE", "name": "SMART_EMPHASIS_RE", "type": "builtins.str"}}, "SMART_STRONG_EM_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.SMART_STRONG_EM_RE", "name": "SMART_STRONG_EM_RE", "type": "builtins.str"}}, "SMART_STRONG_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.SMART_STRONG_RE", "name": "SMART_STRONG_RE", "type": "builtins.str"}}, "STRONG_EM2_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.STRONG_EM2_RE", "name": "STRONG_EM2_RE", "type": "builtins.str"}}, "STRONG_EM3_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.STRONG_EM3_RE", "name": "STRONG_EM3_RE", "type": "builtins.str"}}, "STRONG_EM_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.STRONG_EM_RE", "name": "STRONG_EM_RE", "type": "builtins.str"}}, "STRONG_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.STRONG_RE", "name": "STRONG_RE", "type": "builtins.str"}}, "ShortImageReferenceInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.ImageReferenceInlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.ShortImageReferenceInlineProcessor", "name": "ShortImageReferenceInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.ShortImageReferenceInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.ShortImageReferenceInlineProcessor", "markdown.inlinepatterns.ImageReferenceInlineProcessor", "markdown.inlinepatterns.ReferenceInlineProcessor", "markdown.inlinepatterns.LinkInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.ShortImageReferenceInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.ShortImageReferenceInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShortReferenceInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.ReferenceInlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.ShortReferenceInlineProcessor", "name": "ShortReferenceInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.ShortReferenceInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.ShortReferenceInlineProcessor", "markdown.inlinepatterns.ReferenceInlineProcessor", "markdown.inlinepatterns.LinkInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.ShortReferenceInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.ShortReferenceInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleTagInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.InlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.SimpleTagInlineProcessor", "name": "SimpleTagInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.SimpleTagInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.SimpleTagInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pattern", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.SimpleTagInlineProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pattern", "tag"], "arg_types": ["markdown.inlinepatterns.SimpleTagInlineProcessor", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SimpleTagInlineProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.SimpleTagInlineProcessor.tag", "name": "tag", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.SimpleTagInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.SimpleTagInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleTagPattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.Pattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.SimpleTagPattern", "name": "SimpleTagPattern", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.SimpleTagPattern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.SimpleTagPattern", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pattern", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.SimpleTagPattern.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pattern", "tag"], "arg_types": ["markdown.inlinepatterns.SimpleTagPattern", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SimpleTagPattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.inlinepatterns.SimpleTagPattern.tag", "name": "tag", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.SimpleTagPattern.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.SimpleTagPattern", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleTextInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.InlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.SimpleTextInlineProcessor", "name": "SimpleTextInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.SimpleTextInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.SimpleTextInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.SimpleTextInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.SimpleTextInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleTextPattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.Pattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.SimpleTextPattern", "name": "SimpleTextPattern", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.SimpleTextPattern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.SimpleTextPattern", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.SimpleTextPattern.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.SimpleTextPattern", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SubstituteTagInlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.SimpleTagInlineProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.SubstituteTagInlineProcessor", "name": "SubstituteTagInlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.SubstituteTagInlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.SubstituteTagInlineProcessor", "markdown.inlinepatterns.SimpleTagInlineProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.SubstituteTagInlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.SubstituteTagInlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SubstituteTagPattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.SimpleTagPattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.SubstituteTagPattern", "name": "SubstituteTagPattern", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.SubstituteTagPattern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.SubstituteTagPattern", "markdown.inlinepatterns.SimpleTagPattern", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.SubstituteTagPattern.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.SubstituteTagPattern", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnderscoreProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.inlinepatterns.AsteriskProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.inlinepatterns.UnderscoreProcessor", "name": "UnderscoreProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.UnderscoreProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.inlinepatterns", "mro": ["markdown.inlinepatterns.UnderscoreProcessor", "markdown.inlinepatterns.AsteriskProcessor", "markdown.inlinepatterns.InlineProcessor", "markdown.inlinepatterns.Pattern", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.inlinepatterns.UnderscoreProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.inlinepatterns.UnderscoreProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.inlinepatterns.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "build_inlinepatterns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["md", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.build_inlinepatterns", "name": "build_inlinepatterns", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["md", "kwargs"], "arg_types": ["markdown.core.Markdown", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_inlinepatterns", "ret_type": {".class": "Instance", "args": ["markdown.inlinepatterns.Pattern"], "extra_attrs": null, "type_ref": "markdown.util.Registry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dequote": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.inlinepatterns.dequote", "name": "dequote", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["string"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dequote", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_hidden": true, "module_public": false}, "util": {".class": "SymbolTableNode", "cross_ref": "markdown.util", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/markdown-stubs/inlinepatterns.pyi"}
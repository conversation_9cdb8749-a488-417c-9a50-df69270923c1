{"data_mtime": 1741470549, "dep_lines": [9, 20, 7, 15, 16, 1, 3, 4, 5, 12, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mkdocs.config.base", "mkdocs.utils.babel_stub", "jinja2.ext", "babel.core", "babel.support", "__future__", "logging", "os", "typing", "jinja2", "builtins", "_frozen_importlib", "abc", "babel", "gettext", "jinja2.environment", "mkdocs.utils", "posixpath"], "hash": "a8865ed87082cc6e26d475f6a2a8b601a749ee26", "id": "mkdocs.localization", "ignore_all": true, "interface_hash": "45e48b825e91e0dff17a443c6968ec16f97cb5b1", "mtime": 1731169893, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/localization.py", "plugin_data": null, "size": 3049, "suppressed": [], "version_id": "1.15.0"}
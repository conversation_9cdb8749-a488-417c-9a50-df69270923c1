{"data_mtime": 1741470549, "dep_lines": [10, 5, 6, 7, 1, 2, 3, 4, 5, 8, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 10, 10, 10, 20, 5, 5, 30, 30, 30, 30], "dependencies": ["mkdocstrings._internal.loggers", "urllib.parse", "urllib.request", "collections.abc", "base64", "gzip", "os", "re", "urllib", "typing", "builtins", "_frozen_importlib", "abc", "enum", "logging"], "hash": "379f9c2d16110c044e41444e1c36b31af0d00157", "id": "mkdocstrings._internal.download", "ignore_all": true, "interface_hash": "59678e3d362fd251c758a6614f3822d3f7a9f5fb", "mtime": 1741453475, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/_internal/download.py", "plugin_data": null, "size": 2937, "suppressed": [], "version_id": "1.15.0"}
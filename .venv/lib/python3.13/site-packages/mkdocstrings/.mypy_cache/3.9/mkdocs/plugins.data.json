{".class": "MypyFile", "_fullname": "mkdocs.plugins", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BasePlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.plugins.BasePlugin", "name": "BasePlugin", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.plugins", "mro": ["mkdocs.plugins.BasePlugin", "builtins.object"], "names": {".class": "SymbolTable", "__class_getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "config_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "mkdocs.plugins.BasePlugin.__class_getitem__", "name": "__class_getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "config_class"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}}, {".class": "TypeType", "item": "mkdocs.config.base.Config"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__class_getitem__ of BasePlugin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "mkdocs.plugins.BasePlugin.__init_subclass__", "name": "__init_subclass__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.plugins.BasePlugin.config", "name": "config", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.plugins.BasePlugin.config_class", "name": "config_class", "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}}}}, "config_scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.plugins.BasePlugin.config_scheme", "name": "config_scheme", "type": {".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.config.base.PlainConfigSchema"}}}, "load_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "options", "config_file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.load_config", "name": "load_config", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "options", "config_file_path"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_config of BasePlugin", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.config.base.ConfigErrors"}, {".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.config.base.ConfigWarnings"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_build_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_build_error", "name": "on_build_error", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "error"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_build_error of BasePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_config", "name": "on_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_config of BasePlugin", "ret_type": {".class": "UnionType", "items": ["mkdocs.config.defaults.MkDocsConfig", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_env", "name": "on_env", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "config", "files"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "jinja2.environment.Environment", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_env of BasePlugin", "ret_type": {".class": "UnionType", "items": ["jinja2.environment.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": [null, null, "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_files", "name": "on_files", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": [null, null, "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "mkdocs.structure.files.Files", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_files of BasePlugin", "ret_type": {".class": "UnionType", "items": ["mkdocs.structure.files.Files", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_nav": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_nav", "name": "on_nav", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "config", "files"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "mkdocs.structure.nav.Navigation", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_nav of BasePlugin", "ret_type": {".class": "UnionType", "items": ["mkdocs.structure.nav.Navigation", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_page_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3], "arg_names": [null, null, "page", "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_page_content", "name": "on_page_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3], "arg_names": [null, null, "page", "config", "files"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "builtins.str", "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_page_content of BasePlugin", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_page_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3], "arg_names": [null, null, "page", "config", "nav"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_page_context", "name": "on_page_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3], "arg_names": [null, null, "page", "config", "nav"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, {".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.nav.Navigation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_page_context of BasePlugin", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_page_markdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3], "arg_names": [null, null, "page", "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_page_markdown", "name": "on_page_markdown", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3], "arg_names": [null, null, "page", "config", "files"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "builtins.str", "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_page_markdown of BasePlugin", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_page_read_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": [null, "page", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_page_read_source", "name": "on_page_read_source", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": [null, "page", "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_page_read_source of BasePlugin", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_post_build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_post_build", "name": "on_post_build", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_post_build of BasePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_post_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "page", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_post_page", "name": "on_post_page", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "page", "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "builtins.str", "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_post_page of BasePlugin", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_post_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "template_name", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_post_template", "name": "on_post_template", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "template_name", "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "builtins.str", "builtins.str", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_post_template of BasePlugin", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_pre_build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_pre_build", "name": "on_pre_build", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_pre_build of BasePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_pre_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_pre_page", "name": "on_pre_page", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "config", "files"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_pre_page of BasePlugin", "ret_type": {".class": "UnionType", "items": ["mkdocs.structure.pages.Page", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_pre_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "template_name", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_pre_template", "name": "on_pre_template", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "template_name", "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "jinja2.environment.Template", "builtins.str", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_pre_template of BasePlugin", "ret_type": {".class": "UnionType", "items": ["jinja2.environment.Template", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_serve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "config", "builder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_serve", "name": "on_serve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "config", "builder"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "mkdocs.livereload.LiveReloadServer", "mkdocs.config.defaults.MkDocsConfig", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_serve of BasePlugin", "ret_type": {".class": "UnionType", "items": ["mkdocs.livereload.LiveReloadServer", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_shutdown", "name": "on_shutdown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_shutdown of BasePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_startup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "command", "dirty"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_startup", "name": "on_startup", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "command", "dirty"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "build"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gh-deploy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serve"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_startup of BasePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_template_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "template_name", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.BasePlugin.on_template_context", "name": "on_template_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": [null, null, "template_name", "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, {".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, "builtins.str", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_template_context of BasePlugin", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_multiple_instances": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.plugins.BasePlugin.supports_multiple_instances", "name": "supports_multiple_instances", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.BasePlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "id": 1, "name": "SomeConfig", "namespace": "mkdocs.plugins.BasePlugin", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["SomeConfig"], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CombinedEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.plugins.CombinedEvent", "name": "CombinedEvent", "type_vars": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "mkdocs.plugins.P", "id": 1, "name": "P", "namespace": "mkdocs.plugins.CombinedEvent", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": 2, "name": "T", "namespace": "mkdocs.plugins.CombinedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.CombinedEvent", "has_param_spec_type": true, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.plugins", "mro": ["mkdocs.plugins.CombinedEvent", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "instance", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.CombinedEvent.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "instance", "args", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "mkdocs.plugins.P", "id": 1, "name": "P", "namespace": "mkdocs.plugins.CombinedEvent", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": 2, "name": "T", "namespace": "mkdocs.plugins.CombinedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.CombinedEvent"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "mkdocs.plugins.P", "id": 1, "name": "P", "namespace": "mkdocs.plugins.CombinedEvent", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "mkdocs.plugins.P", "id": 1, "name": "P", "namespace": "mkdocs.plugins.CombinedEvent", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CombinedEvent", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": 2, "name": "T", "namespace": "mkdocs.plugins.CombinedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.CombinedEvent.__get__", "name": "__get__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "methods"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.CombinedEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "methods"], "arg_types": [{".class": "Instance", "args": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "mkdocs.plugins.P", "id": 1, "name": "P", "namespace": "mkdocs.plugins.CombinedEvent", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": 2, "name": "T", "namespace": "mkdocs.plugins.CombinedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.CombinedEvent"}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "mkdocs.plugins.P", "id": 1, "name": "P", "namespace": "mkdocs.plugins.CombinedEvent", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "mkdocs.plugins.P", "id": 1, "name": "P", "namespace": "mkdocs.plugins.CombinedEvent", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": 2, "name": "T", "namespace": "mkdocs.plugins.CombinedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CombinedEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "methods": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocs.plugins.CombinedEvent.methods", "name": "methods", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "mkdocs.plugins.P", "id": 1, "name": "P", "namespace": "mkdocs.plugins.CombinedEvent", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "mkdocs.plugins.P", "id": 1, "name": "P", "namespace": "mkdocs.plugins.CombinedEvent", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": 2, "name": "T", "namespace": "mkdocs.plugins.CombinedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.CombinedEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "mkdocs.plugins.P", "id": 1, "name": "P", "namespace": "mkdocs.plugins.CombinedEvent", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": 2, "name": "T", "namespace": "mkdocs.plugins.CombinedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "mkdocs.plugins.CombinedEvent"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["P", "T"], "typeddict_type": null}}, "Concatenate": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Concatenate", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.base.Config", "kind": "Gdef"}, "ConfigErrors": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.base.ConfigErrors", "kind": "Gdef"}, "ConfigWarnings": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.base.ConfigWarnings", "kind": "Gdef"}, "EVENTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mkdocs.plugins.EVENTS", "name": "EVENTS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "EntryPoint": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.EntryPoint", "kind": "Gdef"}, "Files": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.files.Files", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "LegacyConfig": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.base.LegacyConfig", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "LiveReloadServer": {".class": "SymbolTableNode", "cross_ref": "mkdocs.livereload.LiveReloadServer", "kind": "Gdef"}, "MkDocsConfig": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.defaults.MkDocsConfig", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "Navigation": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.nav.Navigation", "kind": "Gdef"}, "P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.P", "name": "P", "upper_bound": "builtins.object", "variance": 0}}, "Page": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.pages.Page", "kind": "Gdef"}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.ParamSpec", "kind": "Gdef"}, "PlainConfigSchema": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.base.PlainConfigSchema", "kind": "Gdef"}, "PluginCollection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.plugins.PluginCollection", "name": "PluginCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocs.plugins", "mro": ["mkdocs.plugins.PluginCollection", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PluginCollection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["mkdocs.plugins.PluginCollection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PluginCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mkdocs.plugins.BasePlugin"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of PluginCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_current_plugin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.plugins.PluginCollection._current_plugin", "name": "_current_plugin", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_event_origins": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs.plugins.PluginCollection._event_origins", "name": "_event_origins", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_register_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "event_name", "method", "plugin_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection._register_event", "name": "_register_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "event_name", "method", "plugin_name"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "mkdocs.plugins.CombinedEvent"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_event of PluginCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "events": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs.plugins.PluginCollection.events", "name": "events", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "on_build_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_build_error", "name": "on_build_error", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "error"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_build_error of PluginCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_config", "name": "on_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["mkdocs.plugins.PluginCollection", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_config of PluginCollection", "ret_type": "mkdocs.config.defaults.MkDocsConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "env", "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_env", "name": "on_env", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "env", "config", "files"], "arg_types": ["mkdocs.plugins.PluginCollection", "jinja2.environment.Environment", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_env of PluginCollection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["self", "files", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_files", "name": "on_files", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", "files", "config"], "arg_types": ["mkdocs.plugins.PluginCollection", "mkdocs.structure.files.Files", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_files of PluginCollection", "ret_type": "mkdocs.structure.files.Files", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_nav": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "nav", "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_nav", "name": "on_nav", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "nav", "config", "files"], "arg_types": ["mkdocs.plugins.PluginCollection", "mkdocs.structure.nav.Navigation", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_nav of PluginCollection", "ret_type": "mkdocs.structure.nav.Navigation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_page_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["self", "html", "page", "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_page_content", "name": "on_page_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["self", "html", "page", "config", "files"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_page_content of PluginCollection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_page_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["self", "context", "page", "config", "nav"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_page_context", "name": "on_page_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["self", "context", "page", "config", "nav"], "arg_types": ["mkdocs.plugins.PluginCollection", {".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.nav.Navigation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_page_context of PluginCollection", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_page_markdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["self", "markdown", "page", "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_page_markdown", "name": "on_page_markdown", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["self", "markdown", "page", "config", "files"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_page_markdown of PluginCollection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_page_read_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "page", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_page_read_source", "name": "on_page_read_source", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "page", "config"], "arg_types": ["mkdocs.plugins.PluginCollection", "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_page_read_source of PluginCollection", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_post_build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_post_build", "name": "on_post_build", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "config"], "arg_types": ["mkdocs.plugins.PluginCollection", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_post_build of PluginCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_post_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "output", "page", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_post_page", "name": "on_post_page", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "output", "page", "config"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_post_page of PluginCollection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_post_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "output_content", "template_name", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_post_template", "name": "on_post_template", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "output_content", "template_name", "config"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", "builtins.str", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_post_template of PluginCollection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_pre_build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_pre_build", "name": "on_pre_build", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "config"], "arg_types": ["mkdocs.plugins.PluginCollection", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_pre_build of PluginCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_pre_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "page", "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_pre_page", "name": "on_pre_page", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "page", "config", "files"], "arg_types": ["mkdocs.plugins.PluginCollection", "mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_pre_page of PluginCollection", "ret_type": "mkdocs.structure.pages.Page", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_pre_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "template", "template_name", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_pre_template", "name": "on_pre_template", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "template", "template_name", "config"], "arg_types": ["mkdocs.plugins.PluginCollection", "jinja2.environment.Template", "builtins.str", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_pre_template of PluginCollection", "ret_type": "jinja2.environment.Template", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_serve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "server", "config", "builder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_serve", "name": "on_serve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "server", "config", "builder"], "arg_types": ["mkdocs.plugins.PluginCollection", "mkdocs.livereload.LiveReloadServer", "mkdocs.config.defaults.MkDocsConfig", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_serve of PluginCollection", "ret_type": "mkdocs.livereload.LiveReloadServer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_shutdown", "name": "on_shutdown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.plugins.PluginCollection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_shutdown of PluginCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_startup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "command", "dirty"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_startup", "name": "on_startup", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "command", "dirty"], "arg_types": ["mkdocs.plugins.PluginCollection", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "build"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gh-deploy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serve"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_startup of PluginCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_template_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "context", "template_name", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.on_template_context", "name": "on_template_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "context", "template_name", "config"], "arg_types": ["mkdocs.plugins.PluginCollection", {".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, "builtins.str", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_template_context of PluginCollection", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.utils.templates.TemplateContext"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PluginCollection.run_event", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "name", "item", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "mkdocs.plugins.PluginCollection.run_event", "name": "run_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "name", "item", "kwargs"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_event of PluginCollection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "mkdocs.plugins.PluginCollection.run_event", "name": "run_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "name", "kwargs"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_event of PluginCollection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mkdocs.plugins.PluginCollection.run_event", "name": "run_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "name", "kwargs"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_event of PluginCollection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "name", "item", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "mkdocs.plugins.PluginCollection.run_event", "name": "run_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "name", "item", "kwargs"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "mkdocs.plugins.PluginCollection.run_event#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_event of PluginCollection", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "mkdocs.plugins.PluginCollection.run_event#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "mkdocs.plugins.PluginCollection.run_event#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mkdocs.plugins.PluginCollection.run_event", "name": "run_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "name", "item", "kwargs"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "mkdocs.plugins.PluginCollection.run_event#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_event of PluginCollection", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "mkdocs.plugins.PluginCollection.run_event#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "mkdocs.plugins.PluginCollection.run_event#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "name", "kwargs"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_event of PluginCollection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "name", "item", "kwargs"], "arg_types": ["mkdocs.plugins.PluginCollection", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "mkdocs.plugins.PluginCollection.run_event#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_event of PluginCollection", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "mkdocs.plugins.PluginCollection.run_event#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "mkdocs.plugins.PluginCollection.run_event#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.PluginCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.plugins.PluginCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PrefixedLogger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "logging.LoggerAdapter"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.plugins.PrefixedLogger", "name": "PrefixedLogger", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PrefixedLogger", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.plugins", "mro": ["mkdocs.plugins.PrefixedLogger", "logging.LoggerAdapter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "prefix", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PrefixedLogger.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "prefix", "logger"], "arg_types": ["mkdocs.plugins.PrefixedLogger", "builtins.str", "logging.Logger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PrefixedLogger", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocs.plugins.PrefixedLogger.prefix", "name": "prefix", "type": "builtins.str"}}, "process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.PrefixedLogger.process", "name": "process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "kwargs"], "arg_types": ["mkdocs.plugins.PrefixedLogger", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of PrefixedLogger", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.PrefixedLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.plugins.PrefixedLogger", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SomeConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.SomeConfig", "name": "SomeConfig", "upper_bound": "mkdocs.config.base.Config", "values": [], "variance": 0}}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TemplateContext": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils.templates.TemplateContext", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.plugins.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.plugins.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.plugins.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.plugins.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.plugins.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.plugins.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "entry_points": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.entry_points", "kind": "Gdef"}, "event_priority": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["priority"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.event_priority", "name": "event_priority", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["priority"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "event_priority", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.plugins.T", "id": -1, "name": "T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_plugin_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.get_plugin_logger", "name": "get_plugin_logger", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_plugin_logger", "ret_type": "mkdocs.plugins.PrefixedLogger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_plugins": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.plugins.get_plugins", "name": "get_plugins", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_plugins", "ret_type": {".class": "Instance", "args": ["builtins.str", "importlib_metadata.EntryPoint"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jinja2": {".class": "SymbolTableNode", "cross_ref": "jinja2", "kind": "Gdef"}, "k": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "mkdocs.plugins.k", "name": "k", "type": "builtins.str"}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mkdocs.plugins.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/plugins.py"}
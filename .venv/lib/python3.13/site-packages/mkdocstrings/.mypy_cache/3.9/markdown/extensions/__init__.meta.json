{"data_mtime": 1741470544, "dep_lines": [1, 4, 2, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "markdown.core", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "5527a46a050a8b28d15d4f143f0797835b2c168c", "id": "markdown.extensions", "ignore_all": true, "interface_hash": "0e8cd5fe37db5ab0f7a103df1c8005eb241dab30", "mtime": 1733498037, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/markdown-stubs/extensions/__init__.pyi", "plugin_data": null, "size": 588, "suppressed": [], "version_id": "1.15.0"}
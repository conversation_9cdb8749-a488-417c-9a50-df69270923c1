{"data_mtime": 1741470545, "dep_lines": [11, 10, 14, 1, 3, 4, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["watchdog.utils.bricks", "watchdog.utils", "watchdog.events", "__future__", "contextlib", "queue", "threading", "collections", "pathlib", "typing", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "os", "typing_extensions"], "hash": "1c5371ac6969776e1f6c1b91fb37f47a185c619f", "id": "watchdog.observers.api", "ignore_all": true, "interface_hash": "7ecd5f3f1086130088bb7d6a2aa228b80a3aba77", "mtime": 1731169881, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/watchdog/observers/api.py", "plugin_data": null, "size": 13802, "suppressed": [], "version_id": "1.15.0"}
{".class": "MypyFile", "_fullname": "pathspec.pathspec", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnyStr": {".class": "SymbolTableNode", "cross_ref": "typing.AnyStr", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CheckResult": {".class": "SymbolTableNode", "cross_ref": "pathspec.util.CheckResult", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "CollectionType": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PathSpec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pathspec.pathspec.PathSpec", "name": "PathSpec", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pathspec.pathspec", "mro": ["pathspec.pathspec.PathSpec", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.__add__", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}, "pathspec.pathspec.PathSpec"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of PathSpec", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.__add__", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.__add__", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}]}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pathspec.pathspec.PathSpec", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of PathSpec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iadd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.__iadd__", "name": "__iadd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.__iadd__", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}, "pathspec.pathspec.PathSpec"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iadd__ of PathSpec", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.__iadd__", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.__iadd__", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "patterns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "patterns"], "arg_types": ["pathspec.pathspec.PathSpec", {".class": "Instance", "args": ["pathspec.pattern.Pattern"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PathSpec", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pathspec.pathspec.PathSpec"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of PathSpec", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pathspec.pathspec.PathSpec._match_file", "name": "_match_file", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0, 0], "arg_names": ["patterns", "file"], "arg_types": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "pathspec.pattern.Pattern"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "check_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "file", "separators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.check_file", "name": "check_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "file", "separators"], "arg_types": ["pathspec.pathspec.PathSpec", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.util.TStrPath", "id": -1, "name": "TStrPath", "namespace": "pathspec.pathspec.PathSpec.check_file", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_file of PathSpec", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.util.TStrPath", "id": -1, "name": "TStrPath", "namespace": "pathspec.pathspec.PathSpec.check_file", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pathspec.util.CheckResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.util.TStrPath", "id": -1, "name": "TStrPath", "namespace": "pathspec.pathspec.PathSpec.check_file", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "check_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "files", "separators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.check_files", "name": "check_files", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "files", "separators"], "arg_types": ["pathspec.pathspec.PathSpec", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.util.TStrPath", "id": -1, "name": "TStrPath", "namespace": "pathspec.pathspec.PathSpec.check_files", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_files of PathSpec", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.util.TStrPath", "id": -1, "name": "TStrPath", "namespace": "pathspec.pathspec.PathSpec.check_files", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pathspec.util.CheckResult"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.util.TStrPath", "id": -1, "name": "TStrPath", "namespace": "pathspec.pathspec.PathSpec.check_files", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "check_tree_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "root", "on_error", "follow_links"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.check_tree_files", "name": "check_tree_files", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "root", "on_error", "follow_links"], "arg_types": ["pathspec.pathspec.PathSpec", {".class": "TypeAliasType", "args": [], "type_ref": "pathspec.util.StrPath"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.OSError"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_tree_files of PathSpec", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "pathspec.util.CheckResult"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "pattern_factory", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pathspec.pathspec.PathSpec.from_lines", "name": "from_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "pattern_factory", "lines"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -2, "name": "AnyStr", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pathspec.pattern.Pattern", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -2, "name": "AnyStr", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_lines of PathSpec", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -2, "name": "AnyStr", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pathspec.pathspec.PathSpec.from_lines", "name": "from_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "pattern_factory", "lines"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -2, "name": "AnyStr", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pathspec.pattern.Pattern", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -2, "name": "AnyStr", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_lines of PathSpec", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "id": -1, "name": "Self", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -2, "name": "AnyStr", "namespace": "pathspec.pathspec.PathSpec.from_lines", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}}, "match_entries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "entries", "separators", "negate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.match_entries", "name": "match_entries", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "entries", "separators", "negate"], "arg_types": ["pathspec.pathspec.PathSpec", {".class": "Instance", "args": ["pathspec.util.TreeEntry"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_entries of PathSpec", "ret_type": {".class": "Instance", "args": ["pathspec.util.TreeEntry"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "file", "separators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.match_file", "name": "match_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "file", "separators"], "arg_types": ["pathspec.pathspec.PathSpec", {".class": "TypeAliasType", "args": [], "type_ref": "pathspec.util.StrPath"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_file of PathSpec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "files", "separators", "negate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.match_files", "name": "match_files", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "files", "separators", "negate"], "arg_types": ["pathspec.pathspec.PathSpec", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pathspec.util.StrPath"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_files of PathSpec", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pathspec.util.StrPath"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pathspec.pathspec.PathSpec.match_tree", "name": "match_tree", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "root", "on_error", "follow_links", "negate"], "arg_types": ["pathspec.pathspec.PathSpec", {".class": "TypeAliasType", "args": [], "type_ref": "pathspec.util.StrPath"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.OSError"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_tree_entries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "root", "on_error", "follow_links", "negate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.match_tree_entries", "name": "match_tree_entries", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "root", "on_error", "follow_links", "negate"], "arg_types": ["pathspec.pathspec.PathSpec", {".class": "TypeAliasType", "args": [], "type_ref": "pathspec.util.StrPath"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.OSError"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_tree_entries of PathSpec", "ret_type": {".class": "Instance", "args": ["pathspec.util.TreeEntry"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_tree_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "root", "on_error", "follow_links", "negate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pathspec.pathspec.PathSpec.match_tree_files", "name": "match_tree_files", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "root", "on_error", "follow_links", "negate"], "arg_types": ["pathspec.pathspec.PathSpec", {".class": "TypeAliasType", "args": [], "type_ref": "pathspec.util.StrPath"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.OSError"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_tree_files of PathSpec", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "patterns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pathspec.pathspec.PathSpec.patterns", "name": "patterns", "type": {".class": "Instance", "args": ["pathspec.pattern.Pattern"], "extra_attrs": null, "type_ref": "typing.Collection"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.PathSpec.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "pathspec.pattern.Pattern", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pathspec.pathspec.Self", "name": "Self", "upper_bound": "pathspec.pathspec.PathSpec", "values": [], "variance": 0}}, "StrPath": {".class": "SymbolTableNode", "cross_ref": "pathspec.util.StrPath", "kind": "Gdef"}, "TStrPath": {".class": "SymbolTableNode", "cross_ref": "pathspec.util.TStrPath", "kind": "Gdef"}, "TreeEntry": {".class": "SymbolTableNode", "cross_ref": "pathspec.util.TreeEntry", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.pathspec.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.pathspec.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.pathspec.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.pathspec.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.pathspec.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pathspec.pathspec.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_filter_check_patterns": {".class": "SymbolTableNode", "cross_ref": "pathspec.util._filter_check_patterns", "kind": "Gdef"}, "_is_iterable": {".class": "SymbolTableNode", "cross_ref": "pathspec.util._is_iterable", "kind": "Gdef"}, "normalize_file": {".class": "SymbolTableNode", "cross_ref": "pathspec.util.normalize_file", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "pathspec.util", "kind": "Gdef"}, "zip_longest": {".class": "SymbolTableNode", "cross_ref": "itertools.zip_longest", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/pathspec/pathspec.py"}
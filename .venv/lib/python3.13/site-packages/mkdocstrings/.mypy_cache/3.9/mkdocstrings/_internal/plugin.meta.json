{"data_mtime": 1741470549, "dep_lines": [28, 22, 27, 29, 38, 21, 23, 24, 37, 14, 16, 17, 18, 19, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 25, 5, 5, 5, 25, 5, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mkdocstrings._internal.handlers.base", "mkdocs.config.config_options", "mkdocstrings._internal.extension", "mkdocstrings._internal.loggers", "mkdocs.config.defaults", "mkdocs.config", "mkdocs.plugins", "mkdocs.utils", "jinja2.environment", "__future__", "os", "sys", "typing", "warnings", "mkdocs_autorefs", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "jinja2", "logging", "markdown", "markdown.extensions", "mkdocs", "mkdocs.config.base", "mkdocs.theme", "mkdocs_autorefs._internal", "mkdocs_autorefs._internal.plugin", "mkdocstrings._internal.handlers", "posixpath", "types", "typing_extensions"], "hash": "397e32272713589406ee26acfb0c1acd49e4919e", "id": "mkdocstrings._internal.plugin", "ignore_all": true, "interface_hash": "269cbf258f25bb6b9d0f24f6235c53c7aa407509", "mtime": 1741455545, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/_internal/plugin.py", "plugin_data": null, "size": 9851, "suppressed": [], "version_id": "1.15.0"}
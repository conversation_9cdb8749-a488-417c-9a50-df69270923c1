{".class": "MypyFile", "_fullname": "mkdocstrings._internal.handlers.rendering", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CodeHiliteExtension": {".class": "SymbolTableNode", "cross_ref": "markdown.extensions.codehilite.CodeHiliteExtension", "kind": "Gdef"}, "Element": {".class": "SymbolTableNode", "cross_ref": "xml.etree.ElementTree.Element", "kind": "Gdef"}, "Extension": {".class": "SymbolTableNode", "cross_ref": "markdown.extensions.Extension", "kind": "Gdef"}, "HeadingShiftingTreeprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor", "name": "HeadingShiftingTreeprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocstrings._internal.handlers.rendering", "mro": ["mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "md", "shift_by"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "md", "shift_by"], "arg_types": ["mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor", "markdown.core.Markdown", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HeadingShiftingTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor.name", "name": "name", "type": "builtins.str"}}, "regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor.regex", "name": "regex", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "root"], "arg_types": ["mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of HeadingShiftingTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shift_by": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor.shift_by", "name": "shift_by", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Highlight": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mkdocstrings._internal.handlers.rendering.Highlight", "name": "Highlight", "type": {".class": "AnyType", "missing_import_name": "mkdocstrings._internal.handlers.rendering.Highlight", "source_any": null, "type_of_any": 3}}}, "HighlightExtension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mkdocstrings._internal.handlers.rendering.HighlightExtension", "name": "HighlightExtension", "type": {".class": "AnyType", "missing_import_name": "mkdocstrings._internal.handlers.rendering.HighlightExtension", "source_any": null, "type_of_any": 3}}}, "Highlighter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.handlers.rendering.Highlighter", "name": "Highlighter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "mkdocstrings._internal.handlers.rendering.Highlighter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocstrings._internal.handlers.rendering", "mro": ["mkdocstrings._internal.handlers.rendering.Highlighter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.Highlighter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "md"], "arg_types": ["mkdocstrings._internal.handlers.rendering.Highlighter", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON>lighter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_css_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocstrings._internal.handlers.rendering.Highlighter._css_class", "name": "_css_class", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_highlight_config_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocstrings._internal.handlers.rendering.Highlighter._highlight_config_keys", "name": "_highlight_config_keys", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_highlighter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocstrings._internal.handlers.rendering.Highlighter._highlighter", "name": "_highlighter", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "highlight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 4], "arg_names": ["self", "src", "language", "inline", "dedent", "linenums", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.Highlighter.highlight", "name": "highlight", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 4], "arg_names": ["self", "src", "language", "inline", "dedent", "linenums", "kwargs"], "arg_types": ["mkdocstrings._internal.handlers.rendering.Highlighter", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "highlight of <PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "linenums": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocstrings._internal.handlers.rendering.Highlighter.linenums", "name": "linenums", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.handlers.rendering.Highlighter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.handlers.rendering.Highlighter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IdPrependingTreeprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor", "name": "IdPrependingTreeprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocstrings._internal.handlers.rendering", "mro": ["mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "md", "id_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "md", "id_prefix"], "arg_types": ["mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor", "markdown.core.Markdown", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IdPrependingTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prefix_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor._prefix_ids", "name": "_prefix_ids", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "root"], "arg_types": ["mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prefix_ids of IdPrependingTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "id_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor.id_prefix", "name": "id_prefix", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor.name", "name": "name", "type": "builtins.str"}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "root"], "arg_types": ["mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of IdPrependingTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Markdown": {".class": "SymbolTableNode", "cross_ref": "markdown.core.Markdown", "kind": "Gdef"}, "Markup": {".class": "SymbolTableNode", "cross_ref": "markupsafe.Markup", "kind": "Gdef"}, "MkdocstringsInnerExtension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.extensions.Extension"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.handlers.rendering.MkdocstringsInnerExtension", "name": "MkdocstringsInnerExtension", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.MkdocstringsInnerExtension", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocstrings._internal.handlers.rendering", "mro": ["mkdocstrings._internal.handlers.rendering.MkdocstringsInnerExtension", "markdown.extensions.Extension", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.MkdocstringsInnerExtension.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headings"], "arg_types": ["mkdocstrings._internal.handlers.rendering.MkdocstringsInnerExtension", {".class": "Instance", "args": ["xml.etree.ElementTree.Element"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MkdocstringsInnerExtension", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extendMarkdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.MkdocstringsInnerExtension.extendMarkdown", "name": "extendMarkdown", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "md"], "arg_types": ["mkdocstrings._internal.handlers.rendering.MkdocstringsInnerExtension", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extendMarkdown of MkdocstringsInnerExtension", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocstrings._internal.handlers.rendering.MkdocstringsInnerExtension.headings", "name": "headings", "type": {".class": "Instance", "args": ["xml.etree.ElementTree.Element"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.handlers.rendering.MkdocstringsInnerExtension.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.handlers.rendering.MkdocstringsInnerExtension", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParagraphStrippingTreeprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.handlers.rendering.ParagraphStrippingTreeprocessor", "name": "ParagraphStrippingTreeprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.ParagraphStrippingTreeprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocstrings._internal.handlers.rendering", "mro": ["mkdocstrings._internal.handlers.rendering.ParagraphStrippingTreeprocessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocstrings._internal.handlers.rendering.ParagraphStrippingTreeprocessor.name", "name": "name", "type": "builtins.str"}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering.ParagraphStrippingTreeprocessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "root"], "arg_types": ["mkdocstrings._internal.handlers.rendering.ParagraphStrippingTreeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of ParagraphStrippingTreeprocessor", "ret_type": {".class": "UnionType", "items": ["xml.etree.ElementTree.Element", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "strip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocstrings._internal.handlers.rendering.ParagraphStrippingTreeprocessor.strip", "name": "strip", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.handlers.rendering.ParagraphStrippingTreeprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.handlers.rendering.ParagraphStrippingTreeprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Treeprocessor": {".class": "SymbolTableNode", "cross_ref": "markdown.treeprocessors.Treeprocessor", "kind": "Gdef"}, "_HeadingReportingTreeprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor", "name": "_HeadingReportingTreeprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocstrings._internal.handlers.rendering", "mro": ["mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "md", "headings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "md", "headings"], "arg_types": ["mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor", "markdown.core.Markdown", {".class": "Instance", "args": ["xml.etree.ElementTree.Element"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _HeadingReportingTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor.headings", "name": "headings", "type": {".class": "Instance", "args": ["xml.etree.ElementTree.Element"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor.name", "name": "name", "type": "builtins.str"}}, "regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor.regex", "name": "regex", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "root"], "arg_types": ["mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of _HeadingReportingTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.handlers.rendering._HeadingReportingTreeprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.handlers.rendering.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.handlers.rendering.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.handlers.rendering.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.handlers.rendering.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.handlers.rendering.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.handlers.rendering.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/_internal/handlers/rendering.py"}
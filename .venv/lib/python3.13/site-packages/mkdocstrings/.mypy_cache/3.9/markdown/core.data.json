{".class": "MypyFile", "_fullname": "markdown.core", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Element": {".class": "SymbolTableNode", "cross_ref": "xml.etree.ElementTree.Element", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Extension": {".class": "SymbolTableNode", "cross_ref": "markdown.extensions.Extension", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HtmlStash": {".class": "SymbolTableNode", "cross_ref": "markdown.util.HtmlStash", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Markdown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.core.Markdown", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.core.Markdown", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.core", "mro": ["markdown.core.Markdown", "builtins.object"], "names": {".class": "SymbolTable", "ESCAPED_CHARS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.ESCAPED_CHARS", "name": "ESCAPED_CHARS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "extensions", "extension_configs", "output_format", "tab_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.Markdown.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "extensions", "extension_configs", "output_format", "tab_length"], "arg_types": ["markdown.core.Markdown", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "markdown.extensions.Extension"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "xhtml"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "html"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Markdown", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "block_level_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.block_level_elements", "name": "block_level_elements", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "build_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ext_name", "configs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.Markdown.build_extension", "name": "build_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ext_name", "configs"], "arg_types": ["markdown.core.Markdown", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_extension of Markdown", "ret_type": "markdown.extensions.Extension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.Markdown.build_parser", "name": "build_parser", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_parser of Markdown", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}]}}}, "convert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.Markdown.convert", "name": "convert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source"], "arg_types": ["markdown.core.Markdown", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of Markdown", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convertFile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "input", "output", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.Markdown.convertFile", "name": "convertFile", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "input", "output", "encoding"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", "codecs._ReadableStream", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "codecs._WritableStream", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convertFile of Markdown", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}]}}}, "doc_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.core.Markdown.doc_tag", "name": "doc_tag", "type": "builtins.str"}}, "htmlStash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.htmlStash", "name": "htmlStash", "type": "markdown.util.HtmlStash"}}, "inlinePatterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.inlinePatterns", "name": "inlinePatterns", "type": {".class": "Instance", "args": ["markdown.inlinepatterns.Pattern"], "extra_attrs": null, "type_ref": "markdown.util.Registry"}}}, "is_block_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.Markdown.is_block_level", "name": "is_block_level", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["markdown.core.Markdown", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_block_level of Markdown", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.output_format", "name": "output_format", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "xhtml"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "html"}], "uses_pep604_syntax": false}}}, "output_formats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.core.Markdown.output_formats", "name": "output_formats", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "xhtml"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "html"}], "uses_pep604_syntax": false}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.parser", "name": "parser", "type": "markdown.blockparser.BlockParser"}}, "postprocessors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.postprocessors", "name": "postprocessors", "type": {".class": "Instance", "args": ["markdown.postprocessors.Postprocessor"], "extra_attrs": null, "type_ref": "markdown.util.Registry"}}}, "preprocessors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.preprocessors", "name": "preprocessors", "type": {".class": "Instance", "args": ["markdown.preprocessors.Preprocessor"], "extra_attrs": null, "type_ref": "markdown.util.Registry"}}}, "registerExtension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "extension"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.Markdown.registerExtension", "name": "registerExtension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "extension"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}, "markdown.extensions.Extension"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "registerExtension of Markdown", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}]}}}, "registerExtensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extensions", "configs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.Markdown.registerExtensions", "name": "registerExtensions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "extensions", "configs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["markdown.extensions.Extension", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "registerExtensions of <PERSON>down", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}]}}}, "registeredExtensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.registeredExtensions", "name": "registeredExtensions", "type": {".class": "Instance", "args": ["markdown.extensions.Extension"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.Markdown.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of Markdown", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}]}}}, "serializer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.serializer", "name": "serializer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_output_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.Markdown.set_output_format", "name": "set_output_format", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "format"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "xhtml"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "html"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_output_format of Markdown", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}]}}}, "stripTopLevelTags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.stripTopLevelTags", "name": "stripTopLevelTags", "type": "builtins.bool"}}, "tab_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.tab_length", "name": "tab_length", "type": "builtins.int"}}, "treeprocessors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.core.Markdown.treeprocessors", "name": "treeprocessors", "type": {".class": "Instance", "args": ["markdown.treeprocessors.Treeprocessor"], "extra_attrs": null, "type_ref": "markdown.util.Registry"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.core.Markdown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.core.Markdown", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Registry": {".class": "SymbolTableNode", "cross_ref": "markdown.util.Registry", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ReadableStream": {".class": "SymbolTableNode", "cross_ref": "codecs._ReadableStream", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_WritableStream": {".class": "SymbolTableNode", "cross_ref": "codecs._WritableStream", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.core.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.core.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.core.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.core.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.core.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.core.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "blockparser": {".class": "SymbolTableNode", "cross_ref": "markdown.blockparser", "kind": "Gdef", "module_hidden": true, "module_public": false}, "inlinepatterns": {".class": "SymbolTableNode", "cross_ref": "markdown.inlinepatterns", "kind": "Gdef", "module_hidden": true, "module_public": false}, "markdown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["text", "extensions", "extension_configs", "output_format", "tab_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.markdown", "name": "markdown", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["text", "extensions", "extension_configs", "output_format", "tab_length"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "markdown.extensions.Extension"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "xhtml"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "html"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "markdown", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "markdownFromFile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["input", "output", "encoding", "extensions", "extension_configs", "output_format", "tab_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.core.markdownFromFile", "name": "markdownFromFile", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["input", "output", "encoding", "extensions", "extension_configs", "output_format", "tab_length"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "codecs._ReadableStream", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "codecs._WritableStream", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "markdown.extensions.Extension"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "xhtml"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "html"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "markdownFromFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "postprocessors": {".class": "SymbolTableNode", "cross_ref": "markdown.postprocessors", "kind": "Gdef", "module_hidden": true, "module_public": false}, "preprocessors": {".class": "SymbolTableNode", "cross_ref": "markdown.preprocessors", "kind": "Gdef", "module_hidden": true, "module_public": false}, "treeprocessors": {".class": "SymbolTableNode", "cross_ref": "markdown.treeprocessors", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/markdown-stubs/core.pyi"}
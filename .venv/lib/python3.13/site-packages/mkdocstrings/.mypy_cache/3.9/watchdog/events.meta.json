{"data_mtime": 1741470543, "dep_lines": [90, 85, 93, 82, 84, 85, 86, 87, 88, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 25, 5, 10, 20, 10, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["watchdog.utils.patterns", "os.path", "collections.abc", "__future__", "logging", "os", "re", "dataclasses", "typing", "builtins", "_frozen_importlib", "abc", "enum", "typing_extensions"], "hash": "83ef6fc752d5f9284e5e899e5997157a8c6dfdaa", "id": "watchdog.events", "ignore_all": true, "interface_hash": "874d6162c19945eeb6b9204b77c17d49cdadf90b", "mtime": 1731169881, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/watchdog/events.py", "plugin_data": null, "size": 16323, "suppressed": [], "version_id": "1.15.0"}
{".class": "MypyFile", "_fullname": "markdown", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Extension": {".class": "SymbolTableNode", "cross_ref": "markdown.extensions.Extension", "kind": "Gdef"}, "Markdown": {".class": "SymbolTableNode", "cross_ref": "markdown.core.Markdown", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "markdown": {".class": "SymbolTableNode", "cross_ref": "markdown.core.markdown", "kind": "Gdef"}, "markdownFromFile": {".class": "SymbolTableNode", "cross_ref": "markdown.core.markdownFromFile", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/markdown-stubs/__init__.pyi"}
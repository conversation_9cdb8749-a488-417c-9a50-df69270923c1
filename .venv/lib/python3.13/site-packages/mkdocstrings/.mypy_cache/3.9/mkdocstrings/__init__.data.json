{".class": "MypyFile", "_fullname": "mkdocstrings", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoDocProcessor": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.extension.AutoDocProcessor", "kind": "Gdef"}, "BaseHandler": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.BaseHandler", "kind": "Gdef"}, "CollectionError": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.CollectionError", "kind": "Gdef"}, "CollectorItem": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.CollectorItem", "kind": "Gdef"}, "HandlerConfig": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.HandlerConfig", "kind": "Gdef"}, "HandlerOptions": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.HandlerOptions", "kind": "Gdef"}, "Handlers": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.Handlers", "kind": "Gdef"}, "HeadingShiftingTreeprocessor": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.rendering.HeadingShiftingTreeprocessor", "kind": "Gdef"}, "Highlighter": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.rendering.Highlighter", "kind": "Gdef"}, "IdPrependingTreeprocessor": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.rendering.IdPrependingTreeprocessor", "kind": "Gdef"}, "Inventory": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.inventory.Inventory", "kind": "Gdef"}, "InventoryItem": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.inventory.InventoryItem", "kind": "Gdef"}, "LoggerAdapter": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.loggers.LoggerAdapter", "kind": "Gdef"}, "MkdocstringsExtension": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.extension.MkdocstringsExtension", "kind": "Gdef"}, "MkdocstringsInnerExtension": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.rendering.MkdocstringsInnerExtension", "kind": "Gdef"}, "MkdocstringsPlugin": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.plugin.MkdocstringsPlugin", "kind": "Gdef"}, "ParagraphStrippingTreeprocessor": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.rendering.ParagraphStrippingTreeprocessor", "kind": "Gdef"}, "PluginConfig": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.plugin.PluginConfig", "kind": "Gdef"}, "TEMPLATES_DIRS": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.loggers.TEMPLATES_DIRS", "kind": "Gdef"}, "TemplateLogger": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.loggers.TemplateLogger", "kind": "Gdef"}, "ThemeNotSupported": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.ThemeNotSupported", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "mkdocstrings.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "do_any": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.do_any", "kind": "Gdef"}, "get_logger": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.loggers.get_logger", "kind": "Gdef"}, "get_template_logger": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.loggers.get_template_logger", "kind": "Gdef"}, "get_template_logger_function": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.loggers.get_template_logger_function", "kind": "Gdef"}, "get_template_path": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.loggers.get_template_path", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/__init__.py"}
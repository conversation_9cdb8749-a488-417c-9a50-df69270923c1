{".class": "MypyFile", "_fullname": "mkdocs.config.defaults", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MkDocsConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mkdocs.config.base.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.config.defaults.MkDocsConfig", "name": "MkDocsConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.config.defaults.MkDocsConfig", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocs.config.defaults", "mro": ["mkdocs.config.defaults.MkDocsConfig", "mkdocs.config.base.Config", "collections.UserDict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "Validation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mkdocs.config.base.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation", "name": "Validation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocs.config.defaults", "mro": ["mkdocs.config.defaults.MkDocsConfig.Validation", "mkdocs.config.base.Config", "collections.UserDict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "LinksValidation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mkdocs.config.base.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.LinksValidation", "name": "LinksValidation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.LinksValidation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocs.config.defaults", "mro": ["mkdocs.config.defaults.MkDocsConfig.Validation.LinksValidation", "mkdocs.config.base.Config", "collections.UserDict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "absolute_links": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.LinksValidation.absolute_links", "name": "absolute_links", "type": "mkdocs.config.defaults._AbsoluteLinksValidation"}}, "anchors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.LinksValidation.anchors", "name": "anchors", "type": "mkdocs.config.defaults._LogLevel"}}, "not_found": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.LinksValidation.not_found", "name": "not_found", "type": "mkdocs.config.defaults._LogLevel"}}, "unrecognized_links": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.LinksValidation.unrecognized_links", "name": "unrecognized_links", "type": "mkdocs.config.defaults._LogLevel"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.LinksValidation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.config.defaults.MkDocsConfig.Validation.LinksValidation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NavValidation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mkdocs.config.base.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.NavValidation", "name": "NavValidation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.NavValidation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocs.config.defaults", "mro": ["mkdocs.config.defaults.MkDocsConfig.Validation.NavValidation", "mkdocs.config.base.Config", "collections.UserDict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "absolute_links": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.NavValidation.absolute_links", "name": "absolute_links", "type": "mkdocs.config.defaults._AbsoluteLinksValidation"}}, "not_found": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.NavValidation.not_found", "name": "not_found", "type": "mkdocs.config.defaults._LogLevel"}}, "omitted_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.NavValidation.omitted_files", "name": "omitted_files", "type": "mkdocs.config.defaults._LogLevel"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.NavValidation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.config.defaults.MkDocsConfig.Validation.NavValidation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "links": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.links", "name": "links", "type": {".class": "Instance", "args": ["mkdocs.config.defaults.MkDocsConfig.Validation.LinksValidation"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.SubConfig"}}}, "nav": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.nav", "name": "nav", "type": {".class": "Instance", "args": ["mkdocs.config.defaults.MkDocsConfig.Validation.NavValidation"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.SubConfig"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.config.defaults.MkDocsConfig.Validation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.config.defaults.MkDocsConfig.Validation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_current_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig._current_page", "name": "_current_page", "type": {".class": "UnionType", "items": ["mkdocs.structure.pages.Page", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "config_file_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.config_file_path", "name": "config_file_path", "type": "builtins.str"}}, "copyright": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.copyright", "name": "copyright", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "dev_addr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.dev_addr", "name": "dev_addr", "type": "mkdocs.config.config_options.IpAddress"}}, "docs_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.docs_dir", "name": "docs_dir", "type": "mkdocs.config.config_options.DocsDir"}}, "draft_docs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.draft_docs", "name": "draft_docs", "type": {".class": "Instance", "args": ["pathspec.gitignore.GitIgnoreSpec"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "edit_uri": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.edit_uri", "name": "edit_uri", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "edit_uri_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.edit_uri_template", "name": "edit_uri_template", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "exclude_docs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.exclude_docs", "name": "exclude_docs", "type": {".class": "Instance", "args": ["pathspec.gitignore.GitIgnoreSpec"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "extra": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.extra", "name": "extra", "type": {".class": "Instance", "args": ["mkdocs.config.base.LegacyConfig"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.SubConfig"}}}, "extra_css": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.extra_css", "name": "extra_css", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Type"}}}, "extra_javascript": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.extra_javascript", "name": "extra_javascript", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["mkdocs.config.config_options.ExtraScriptValue", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.ListOfItems"}}}, "extra_templates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.extra_templates", "name": "extra_templates", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Type"}}}, "google_analytics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.google_analytics", "name": "google_analytics", "type": "mkdocs.config.config_options.Deprecated"}}, "hooks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.hooks", "name": "hooks", "type": "mkdocs.config.config_options.Hooks"}}, "load_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "patch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.config.defaults.MkDocsConfig.load_dict", "name": "load_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "patch"], "arg_types": ["mkdocs.config.defaults.MkDocsConfig", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_dict of MkDocsConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.config.defaults.MkDocsConfig.load_file", "name": "load_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config_file"], "arg_types": ["mkdocs.config.defaults.MkDocsConfig", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_file of MkDocsConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "markdown_extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.markdown_extensions", "name": "markdown_extensions", "type": "mkdocs.config.config_options.MarkdownExtensions"}}, "mdx_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.mdx_configs", "name": "mdx_configs", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Private"}}}, "nav": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.nav", "name": "nav", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "not_in_nav": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.not_in_nav", "name": "not_in_nav", "type": {".class": "Instance", "args": ["pathspec.gitignore.GitIgnoreSpec"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "pages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.pages", "name": "pages", "type": "mkdocs.config.config_options.Deprecated"}}, "plugins": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.plugins", "name": "plugins", "type": "mkdocs.config.config_options.Plugins"}}, "remote_branch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.remote_branch", "name": "remote_branch", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Type"}}}, "remote_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.remote_name", "name": "remote_name", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Type"}}}, "repo_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.repo_name", "name": "repo_name", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "repo_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.repo_url", "name": "repo_url", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "site_author": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.site_author", "name": "site_author", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "site_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.site_description", "name": "site_description", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "site_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.site_dir", "name": "site_dir", "type": "mkdocs.config.config_options.SiteDir"}}, "site_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.site_name", "name": "site_name", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Type"}}}, "site_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.site_url", "name": "site_url", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Optional"}}}, "strict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.strict", "name": "strict", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Type"}}}, "theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.theme", "name": "theme", "type": "mkdocs.config.config_options.Theme"}}, "use_directory_urls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.use_directory_urls", "name": "use_directory_urls", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.Type"}}}, "validation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.validation", "name": "validation", "type": {".class": "Instance", "args": ["mkdocs.config.defaults.MkDocsConfig.Validation"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.PropagatingSubConfig"}}}, "watch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.config.defaults.MkDocsConfig.watch", "name": "watch", "type": "mkdocs.config.config_options.ListOfPaths"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.config.defaults.MkDocsConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.config.defaults.MkDocsConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Page": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.pages.Page", "kind": "Gdef"}, "_AbsoluteLinksValidation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mkdocs.config.defaults._LogLevel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.config.defaults._AbsoluteLinksValidation", "name": "_AbsoluteLinksValidation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.config.defaults._AbsoluteLinksValidation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.config.defaults", "mro": ["mkdocs.config.defaults._AbsoluteLinksValidation", "mkdocs.config.defaults._LogLevel", "mkdocs.config.config_options.OptionallyRequired", "mkdocs.config.base.BaseConfigOption", "builtins.object"], "names": {".class": "SymbolTable", "levels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.config.defaults._AbsoluteLinksValidation.levels", "name": "levels", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.config.defaults._AbsoluteLinksValidation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.config.defaults._AbsoluteLinksValidation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AbsoluteLinksValidationValue": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.pages._AbsoluteLinksValidationValue", "kind": "Gdef"}, "_LogLevel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "mkdocs.config.config_options.OptionallyRequired"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.config.defaults._LogLevel", "name": "_LogLevel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.config.defaults._LogLevel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.config.defaults", "mro": ["mkdocs.config.defaults._LogLevel", "mkdocs.config.config_options.OptionallyRequired", "mkdocs.config.base.BaseConfigOption", "builtins.object"], "names": {".class": "SymbolTable", "levels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.config.defaults._LogLevel.levels", "name": "levels", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "run_validation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.config.defaults._LogLevel.run_validation", "name": "run_validation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["mkdocs.config.defaults._LogLevel", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_validation of _LogLevel", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.config.defaults._LogLevel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.config.defaults._LogLevel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.config.defaults.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.config.defaults.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.config.defaults.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.config.defaults.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.config.defaults.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.config.defaults.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "base": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.base", "kind": "Gdef"}, "c": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.config_options", "kind": "Gdef"}, "get_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.config.defaults.get_schema", "name": "get_schema", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "mkdocs.config.base.PlainConfigSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_yaml_loader": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils.yaml.get_yaml_loader", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "yaml_load": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils.yaml.yaml_load", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/config/defaults.py"}
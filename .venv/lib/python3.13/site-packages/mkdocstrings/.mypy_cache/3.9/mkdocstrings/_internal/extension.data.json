{".class": "MypyFile", "_fullname": "mkdocstrings._internal.extension", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutoDocProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.blockprocessors.BlockProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.extension.AutoDocProcessor", "name": "AutoDocProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension.AutoDocProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocstrings._internal.extension", "mro": ["mkdocstrings._internal.extension.AutoDocProcessor", "markdown.blockprocessors.BlockProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "md", "handlers", "autorefs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension.AutoDocProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "md", "handlers", "autorefs"], "arg_types": ["mkdocstrings._internal.extension.AutoDocProcessor", "markdown.core.Markdown", "mkdocstrings._internal.handlers.base.Handlers", "mkdocs_autorefs._internal.plugin.AutorefsPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AutoDocProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_autorefs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocstrings._internal.extension.AutoDocProcessor._autorefs", "name": "_autorefs", "type": "mkdocs_autorefs._internal.plugin.AutorefsPlugin"}}, "_handlers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocstrings._internal.extension.AutoDocProcessor._handlers", "name": "_handlers", "type": "mkdocstrings._internal.handlers.base.Handlers"}}, "_process_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "identifier", "yaml_block", "heading_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension.AutoDocProcessor._process_block", "name": "_process_block", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "identifier", "yaml_block", "heading_level"], "arg_types": ["mkdocstrings._internal.extension.AutoDocProcessor", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_block of AutoDocProcessor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "mkdocstrings._internal.handlers.base.BaseHandler", {".class": "TypeAliasType", "args": [], "type_ref": "mkdocstrings._internal.handlers.base.CollectorItem"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_headings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "handler", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension.AutoDocProcessor._process_headings", "name": "_process_headings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "handler", "element"], "arg_types": ["mkdocstrings._internal.extension.AutoDocProcessor", "mkdocstrings._internal.handlers.base.BaseHandler", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_headings of AutoDocProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_updated_envs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocstrings._internal.extension.AutoDocProcessor._updated_envs", "name": "_updated_envs", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "md": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocstrings._internal.extension.AutoDocProcessor.md", "name": "md", "type": "markdown.core.Markdown"}}, "regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "mkdocstrings._internal.extension.AutoDocProcessor.regex", "name": "regex", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "blocks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension.AutoDocProcessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "blocks"], "arg_types": ["mkdocstrings._internal.extension.AutoDocProcessor", "xml.etree.ElementTree.Element", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableSequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of AutoDocProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "block"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension.AutoDocProcessor.test", "name": "test", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "block"], "arg_types": ["mkdocstrings._internal.extension.AutoDocProcessor", "xml.etree.ElementTree.Element", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test of AutoDocProcessor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.extension.AutoDocProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.extension.AutoDocProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutorefsPlugin": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.plugin.AutorefsPlugin", "kind": "Gdef"}, "BaseHandler": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.BaseHandler", "kind": "Gdef"}, "BlockProcessor": {".class": "SymbolTableNode", "cross_ref": "markdown.blockprocessors.BlockProcessor", "kind": "Gdef"}, "CollectionError": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.CollectionError", "kind": "Gdef"}, "CollectorItem": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.CollectorItem", "kind": "Gdef"}, "Element": {".class": "SymbolTableNode", "cross_ref": "xml.etree.ElementTree.Element", "kind": "Gdef"}, "Extension": {".class": "SymbolTableNode", "cross_ref": "markdown.extensions.Extension", "kind": "Gdef"}, "Handlers": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.handlers.base.Handlers", "kind": "Gdef"}, "Markdown": {".class": "SymbolTableNode", "cross_ref": "markdown.core.Markdown", "kind": "Gdef"}, "MkdocstringsExtension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.extensions.Extension"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.extension.MkdocstringsExtension", "name": "MkdocstringsExtension", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension.MkdocstringsExtension", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocstrings._internal.extension", "mro": ["mkdocstrings._internal.extension.MkdocstringsExtension", "markdown.extensions.Extension", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "handlers", "autorefs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension.MkdocstringsExtension.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "handlers", "autorefs", "kwargs"], "arg_types": ["mkdocstrings._internal.extension.MkdocstringsExtension", "mkdocstrings._internal.handlers.base.Handlers", "mkdocs_autorefs._internal.plugin.AutorefsPlugin", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MkdocstringsExtension", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_autorefs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocstrings._internal.extension.MkdocstringsExtension._autorefs", "name": "_autorefs", "type": "mkdocs_autorefs._internal.plugin.AutorefsPlugin"}}, "_handlers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocstrings._internal.extension.MkdocstringsExtension._handlers", "name": "_handlers", "type": "mkdocstrings._internal.handlers.base.Handlers"}}, "extendMarkdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension.MkdocstringsExtension.extendMarkdown", "name": "extendMarkdown", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "md"], "arg_types": ["mkdocstrings._internal.extension.MkdocstringsExtension", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extendMarkdown of MkdocstringsExtension", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.extension.MkdocstringsExtension.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.extension.MkdocstringsExtension", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef"}, "PluginError": {".class": "SymbolTableNode", "cross_ref": "mkdocs.exceptions.PluginError", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TemplateNotFound": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplateNotFound", "kind": "Gdef"}, "Treeprocessor": {".class": "SymbolTableNode", "cross_ref": "markdown.treeprocessors.Treeprocessor", "kind": "Gdef"}, "_HeadingsPostProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.extension._HeadingsPostProcessor", "name": "_HeadingsPostProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension._HeadingsPostProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocstrings._internal.extension", "mro": ["mkdocstrings._internal.extension._HeadingsPostProcessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "_remove_duplicated_headings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension._HeadingsPostProcessor._remove_duplicated_headings", "name": "_remove_duplicated_headings", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "arg_types": ["mkdocstrings._internal.extension._HeadingsPostProcessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_duplicated_headings of _HeadingsPostProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension._HeadingsPostProcessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "root"], "arg_types": ["mkdocstrings._internal.extension._HeadingsPostProcessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of _HeadingsPostProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.extension._HeadingsPostProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.extension._HeadingsPostProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TocLabelsTreeProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocstrings._internal.extension._TocLabelsTreeProcessor", "name": "_TocLabelsTreeProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension._TocLabelsTreeProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocstrings._internal.extension", "mro": ["mkdocstrings._internal.extension._TocLabelsTreeProcessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "_override_toc_labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension._TocLabelsTreeProcessor._override_toc_labels", "name": "_override_toc_labels", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "arg_types": ["mkdocstrings._internal.extension._TocLabelsTreeProcessor", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_override_toc_labels of _TocLabelsTreeProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocstrings._internal.extension._TocLabelsTreeProcessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "root"], "arg_types": ["mkdocstrings._internal.extension._TocLabelsTreeProcessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of _TocLabelsTreeProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocstrings._internal.extension._TocLabelsTreeProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocstrings._internal.extension._TocLabelsTreeProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.extension.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.extension.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.extension.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.extension.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.extension.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocstrings._internal.extension.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mkdocstrings._internal.extension._logger", "name": "_logger", "type": "mkdocstrings._internal.loggers.LoggerAdapter"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "get_logger": {".class": "SymbolTableNode", "cross_ref": "mkdocstrings._internal.loggers.get_logger", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}, "yaml": {".class": "SymbolTableNode", "cross_ref": "yaml", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/_internal/extension.py"}
{".class": "MypyFile", "_fullname": "markdown.treeprocessors", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Element": {".class": "SymbolTableNode", "cross_ref": "xml.etree.ElementTree.Element", "kind": "Gdef", "module_hidden": true, "module_public": false}, "InlineProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.treeprocessors.InlineProcessor", "name": "InlineProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.treeprocessors.InlineProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.treeprocessors", "mro": ["markdown.treeprocessors.InlineProcessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.treeprocessors.InlineProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "md"], "arg_types": ["markdown.treeprocessors.InlineProcessor", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InlineProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ancestors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.treeprocessors.InlineProcessor.ancestors", "name": "ancestors", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "inlinePatterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.treeprocessors.InlineProcessor.inlinePatterns", "name": "inlinePatterns", "type": {".class": "Instance", "args": ["markdown.treeprocessors.InlineProcessor"], "extra_attrs": null, "type_ref": "markdown.util.Registry"}}}, "parent_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.treeprocessors.InlineProcessor.parent_map", "name": "parent_map", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "tree", "ancestors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.treeprocessors.InlineProcessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "tree", "ancestors"], "arg_types": ["markdown.treeprocessors.InlineProcessor", "xml.etree.ElementTree.Element", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of InlineProcessor", "ret_type": "xml.etree.ElementTree.Element", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stashed_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.treeprocessors.InlineProcessor.stashed_nodes", "name": "stashed_nodes", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["xml.etree.ElementTree.Element", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.treeprocessors.InlineProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.treeprocessors.InlineProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Markdown": {".class": "SymbolTableNode", "cross_ref": "markdown.core.Markdown", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PrettifyTreeprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.treeprocessors.PrettifyTreeprocessor", "name": "PrettifyTreeprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.treeprocessors.PrettifyTreeprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.treeprocessors", "mro": ["markdown.treeprocessors.PrettifyTreeprocessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.treeprocessors.PrettifyTreeprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.treeprocessors.PrettifyTreeprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Treeprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.util.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.treeprocessors.Treeprocessor", "name": "Treeprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.treeprocessors.Treeprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.treeprocessors", "mro": ["markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.treeprocessors.Treeprocessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "root"], "arg_types": ["markdown.treeprocessors.Treeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of Treeprocessor", "ret_type": {".class": "UnionType", "items": ["xml.etree.ElementTree.Element", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.treeprocessors.Treeprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.treeprocessors.Treeprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeGuard", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UnescapeTreeprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.treeprocessors.UnescapeTreeprocessor", "name": "UnescapeTreeprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.treeprocessors.UnescapeTreeprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.treeprocessors", "mro": ["markdown.treeprocessors.UnescapeTreeprocessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "markdown.treeprocessors.UnescapeTreeprocessor.RE", "name": "RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "unescape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.treeprocessors.UnescapeTreeprocessor.unescape", "name": "unescape", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["markdown.treeprocessors.UnescapeTreeprocessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unescape of UnescapeTreeprocessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.treeprocessors.UnescapeTreeprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.treeprocessors.UnescapeTreeprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.treeprocessors.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.treeprocessors.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.treeprocessors.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.treeprocessors.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.treeprocessors.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.treeprocessors.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "build_treeprocessors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["md", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.treeprocessors.build_treeprocessors", "name": "build_treeprocessors", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["md", "kwargs"], "arg_types": ["markdown.core.Markdown", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_treeprocessors", "ret_type": {".class": "Instance", "args": ["markdown.treeprocessors.Treeprocessor"], "extra_attrs": null, "type_ref": "markdown.util.Registry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.treeprocessors.isString", "name": "isString", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isString", "ret_type": "builtins.bool", "type_guard": "builtins.str", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "util": {".class": "SymbolTableNode", "cross_ref": "markdown.util", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/markdown-stubs/treeprocessors.pyi"}
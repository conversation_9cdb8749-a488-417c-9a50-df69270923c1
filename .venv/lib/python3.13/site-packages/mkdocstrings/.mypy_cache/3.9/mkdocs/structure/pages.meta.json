{"data_mtime": 1741470549, "dep_lines": [12, 20, 21, 22, 25, 27, 28, 8, 12, 13, 14, 15, 16, 18, 19, 25, 1, 3, 4, 5, 6, 7, 11, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 25, 25, 25, 5, 20, 10, 10, 10, 5, 5, 5, 25, 5, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["markdown.extensions.toc", "mkdocs.structure.toc", "mkdocs.utils.meta", "mkdocs.utils.rendering", "xml.etree.ElementTree", "mkdocs.config.defaults", "mkdocs.structure.files", "urllib.parse", "markdown.extensions", "markdown.htmlparser", "markdown.postprocessors", "markdown.treeprocessors", "markdown.util", "mkdocs.utils", "mkdocs.structure", "xml.etree", "__future__", "enum", "logging", "posixpath", "warnings", "typing", "markdown", "mkdocs", "builtins", "_frozen_importlib", "abc", "collections", "markdown.core", "markdown.preprocessors", "mkdocs.config", "mkdocs.config.base", "mkdocs.plugins", "mkdocs.structure.nav", "os", "types", "urllib", "xml"], "hash": "a97c80a81381f5072c152c3bf0ddf90c08d47d2d", "id": "mkdocs.structure.pages", "ignore_all": true, "interface_hash": "ad3e776f4540f54218e5ba93e792aee0a5fc8116", "mtime": 1738609362, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/structure/pages.py", "plugin_data": null, "size": 22112, "suppressed": [], "version_id": "1.15.0"}
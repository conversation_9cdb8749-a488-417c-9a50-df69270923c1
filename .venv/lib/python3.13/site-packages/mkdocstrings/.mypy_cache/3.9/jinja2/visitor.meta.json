{"data_mtime": 1741470548, "dep_lines": [7, 5, 10, 1, 1, 1], "dep_prios": [5, 10, 25, 5, 30, 30], "dependencies": ["jinja2.nodes", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "5f77dec07cd334660d542c123d115d5962a874c7", "id": "jinja2.visitor", "ignore_all": true, "interface_hash": "da0712dbaf841628644fc324333902bc9b99c657", "mtime": 1734903670, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/jinja2/visitor.py", "plugin_data": null, "size": 3557, "suppressed": [], "version_id": "1.15.0"}
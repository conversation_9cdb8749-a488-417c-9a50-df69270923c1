from fastapi import Depends, HTTPException, status  # type: ignore[import-not-found]
from fastapi.security import OAuth2<PERSON><PERSON>word<PERSON>earer  # type: ignore[import-not-found]
from jose import JWTError  # type: ignore[import-untyped]
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Any

from src.application.user_service import UserService
from src.infrastructure.db import get_db
from src.shared.security import verify_access_token

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/login")

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_db),
) -> Any:
    try:
        payload = verify_access_token(token)
        user_id = int(payload["sub"])
    except (J<PERSON><PERSON>rror, KeyError, ValueError) as err:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
        ) from err
    user_service = UserService()
    user = await user_service.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    return user

def generate_unique_id() -> str:
    """Generate a unique identifier for users."""
    import uuid
    return str(uuid.uuid4())

def format_email(email: str | None) -> str:
    """Format and sanitize the email address. Handles None safely."""
    if not email:
        return ""
    # Strip whitespace and zero-width characters
    cleaned = email.strip()
    # Remove zero-width characters and other invisible characters
    cleaned = ''.join(char for char in cleaned if char.isprintable() and not char.isspace() or char == ' ')
    cleaned = cleaned.strip()
    if not cleaned:
        return ""
    return cleaned.lower()

def is_valid_password(password: str) -> bool:
    """Check if the password meets security requirements."""
    if not password or len(password) < 8:
        return False

    has_upper = any(char.isupper() for char in password)
    has_lower = any(char.islower() for char in password)
    has_digit = any(char.isdigit() for char in password)
    has_special = any(char in "!@#$%^&*()_+-=[]{}|;:,.<>?" for char in password)

    return has_upper and has_lower and has_digit and has_special
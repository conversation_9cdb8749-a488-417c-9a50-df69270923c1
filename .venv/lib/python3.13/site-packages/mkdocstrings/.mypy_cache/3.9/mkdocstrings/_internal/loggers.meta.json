{"data_mtime": 1741470549, "dep_lines": [16, 18, 3, 5, 6, 7, 8, 13, 1, 1, 1, 1, 1, 22], "dep_prios": [25, 25, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 10], "dependencies": ["collections.abc", "jinja2.runtime", "__future__", "logging", "contextlib", "pathlib", "typing", "jinja2", "builtins", "_frozen_importlib", "abc", "os", "types"], "hash": "c8d3c6afb1c6e1caa978114f5970dd9497158b98", "id": "mkdocstrings._internal.loggers", "ignore_all": true, "interface_hash": "cad1dc08817417918954b1702fc8b7294ff2e6be", "mtime": 1741454122, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/_internal/loggers.py", "plugin_data": null, "size": 6590, "suppressed": ["mkdocstrings_handlers"], "version_id": "1.15.0"}
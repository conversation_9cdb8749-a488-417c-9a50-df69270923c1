{"data_mtime": 1741470549, "dep_lines": [9, 19, 8, 26, 27, 36, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["mkdocstrings._internal.handlers.base", "mkdocstrings._internal.handlers.rendering", "mkdocstrings._internal.extension", "mkdocstrings._internal.inventory", "mkdocstrings._internal.loggers", "mkdocstrings._internal.plugin", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "fefe09f74574114d1739f2236af3bbfaf8eb21bd", "id": "mkdocstrings", "ignore_all": true, "interface_hash": "af8f9e2cf5aa594125e5e9b115d27ef9fa8bbf40", "mtime": 1741455540, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings/src/mkdocstrings/__init__.py", "plugin_data": null, "size": 1614, "suppressed": [], "version_id": "1.15.0"}
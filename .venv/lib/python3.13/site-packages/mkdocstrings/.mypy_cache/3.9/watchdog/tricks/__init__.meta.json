{"data_mtime": 1741470544, "dep_lines": [38, 38, 39, 40, 37, 38, 26, 28, 29, 30, 31, 32, 33, 34, 35, 106, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["watchdog.utils.echo", "watchdog.utils.platform", "watchdog.utils.event_debouncer", "watchdog.utils.process_watcher", "watchdog.events", "watchdog.utils", "__future__", "contextlib", "functools", "logging", "os", "signal", "subprocess", "threading", "time", "string", "builtins", "_frozen_importlib", "_thread", "abc", "enum", "types", "typing"], "hash": "83942f93b7aad352fa220e96b95c2505a482a937", "id": "watchdog.tricks", "ignore_all": true, "interface_hash": "192a6da1a853a1bf0252c28c5f352d96eb102308", "mtime": 1731169881, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/watchdog/tricks/__init__.py", "plugin_data": null, "size": 9492, "suppressed": [], "version_id": "1.15.0"}
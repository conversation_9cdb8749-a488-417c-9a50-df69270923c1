{".class": "MypyFile", "_fullname": "stat", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SF_APPEND": {".class": "SymbolTableNode", "cross_ref": "_stat.SF_APPEND", "kind": "Gdef"}, "SF_ARCHIVED": {".class": "SymbolTableNode", "cross_ref": "_stat.SF_ARCHIVED", "kind": "Gdef"}, "SF_IMMUTABLE": {".class": "SymbolTableNode", "cross_ref": "_stat.SF_IMMUTABLE", "kind": "Gdef"}, "SF_NOUNLINK": {".class": "SymbolTableNode", "cross_ref": "_stat.SF_NOUNLINK", "kind": "Gdef"}, "SF_SNAPSHOT": {".class": "SymbolTableNode", "cross_ref": "_stat.SF_SNAPSHOT", "kind": "Gdef"}, "ST_ATIME": {".class": "SymbolTableNode", "cross_ref": "_stat.ST_ATIME", "kind": "Gdef"}, "ST_CTIME": {".class": "SymbolTableNode", "cross_ref": "_stat.ST_CTIME", "kind": "Gdef"}, "ST_DEV": {".class": "SymbolTableNode", "cross_ref": "_stat.ST_DEV", "kind": "Gdef"}, "ST_GID": {".class": "SymbolTableNode", "cross_ref": "_stat.ST_GID", "kind": "Gdef"}, "ST_INO": {".class": "SymbolTableNode", "cross_ref": "_stat.ST_INO", "kind": "Gdef"}, "ST_MODE": {".class": "SymbolTableNode", "cross_ref": "_stat.ST_MODE", "kind": "Gdef"}, "ST_MTIME": {".class": "SymbolTableNode", "cross_ref": "_stat.ST_MTIME", "kind": "Gdef"}, "ST_NLINK": {".class": "SymbolTableNode", "cross_ref": "_stat.ST_NLINK", "kind": "Gdef"}, "ST_SIZE": {".class": "SymbolTableNode", "cross_ref": "_stat.ST_SIZE", "kind": "Gdef"}, "ST_UID": {".class": "SymbolTableNode", "cross_ref": "_stat.ST_UID", "kind": "Gdef"}, "S_ENFMT": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ENFMT", "kind": "Gdef"}, "S_IEXEC": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IEXEC", "kind": "Gdef"}, "S_IFBLK": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFBLK", "kind": "Gdef"}, "S_IFCHR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFCHR", "kind": "Gdef"}, "S_IFDIR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFDIR", "kind": "Gdef"}, "S_IFDOOR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFDOOR", "kind": "Gdef"}, "S_IFIFO": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFIFO", "kind": "Gdef"}, "S_IFLNK": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFLNK", "kind": "Gdef"}, "S_IFMT": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFMT", "kind": "Gdef"}, "S_IFPORT": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFPORT", "kind": "Gdef"}, "S_IFREG": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFREG", "kind": "Gdef"}, "S_IFSOCK": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFSOCK", "kind": "Gdef"}, "S_IFWHT": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IFWHT", "kind": "Gdef"}, "S_IMODE": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IMODE", "kind": "Gdef"}, "S_IREAD": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IREAD", "kind": "Gdef"}, "S_IRGRP": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IRGRP", "kind": "Gdef"}, "S_IROTH": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IROTH", "kind": "Gdef"}, "S_IRUSR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IRUSR", "kind": "Gdef"}, "S_IRWXG": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IRWXG", "kind": "Gdef"}, "S_IRWXO": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IRWXO", "kind": "Gdef"}, "S_IRWXU": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IRWXU", "kind": "Gdef"}, "S_ISBLK": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISBLK", "kind": "Gdef"}, "S_ISCHR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISCHR", "kind": "Gdef"}, "S_ISDIR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISDIR", "kind": "Gdef"}, "S_ISDOOR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISDOOR", "kind": "Gdef"}, "S_ISFIFO": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISFIFO", "kind": "Gdef"}, "S_ISGID": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISGID", "kind": "Gdef"}, "S_ISLNK": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISLNK", "kind": "Gdef"}, "S_ISPORT": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISPORT", "kind": "Gdef"}, "S_ISREG": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISREG", "kind": "Gdef"}, "S_ISSOCK": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISSOCK", "kind": "Gdef"}, "S_ISUID": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISUID", "kind": "Gdef"}, "S_ISVTX": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISVTX", "kind": "Gdef"}, "S_ISWHT": {".class": "SymbolTableNode", "cross_ref": "_stat.S_ISWHT", "kind": "Gdef"}, "S_IWGRP": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IWGRP", "kind": "Gdef"}, "S_IWOTH": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IWOTH", "kind": "Gdef"}, "S_IWRITE": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IWRITE", "kind": "Gdef"}, "S_IWUSR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IWUSR", "kind": "Gdef"}, "S_IXGRP": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IXGRP", "kind": "Gdef"}, "S_IXOTH": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IXOTH", "kind": "Gdef"}, "S_IXUSR": {".class": "SymbolTableNode", "cross_ref": "_stat.S_IXUSR", "kind": "Gdef"}, "UF_APPEND": {".class": "SymbolTableNode", "cross_ref": "_stat.UF_APPEND", "kind": "Gdef"}, "UF_COMPRESSED": {".class": "SymbolTableNode", "cross_ref": "_stat.UF_COMPRESSED", "kind": "Gdef"}, "UF_HIDDEN": {".class": "SymbolTableNode", "cross_ref": "_stat.UF_HIDDEN", "kind": "Gdef"}, "UF_IMMUTABLE": {".class": "SymbolTableNode", "cross_ref": "_stat.UF_IMMUTABLE", "kind": "Gdef"}, "UF_NODUMP": {".class": "SymbolTableNode", "cross_ref": "_stat.UF_NODUMP", "kind": "Gdef"}, "UF_NOUNLINK": {".class": "SymbolTableNode", "cross_ref": "_stat.UF_NOUNLINK", "kind": "Gdef"}, "UF_OPAQUE": {".class": "SymbolTableNode", "cross_ref": "_stat.UF_OPAQUE", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "stat.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "stat.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "stat.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "stat.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "stat.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "stat.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "filemode": {".class": "SymbolTableNode", "cross_ref": "_stat.filemode", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/home/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/stat.pyi"}
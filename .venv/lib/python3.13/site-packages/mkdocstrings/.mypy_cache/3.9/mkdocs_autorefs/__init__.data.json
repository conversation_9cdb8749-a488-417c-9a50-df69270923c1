{".class": "MypyFile", "_fullname": "mkdocs_autorefs", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AUTOREF_RE": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.AUTOREF_RE", "kind": "Gdef"}, "AUTO_REF_RE": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.AUTO_REF_RE", "kind": "Gdef"}, "AnchorScannerTreeProcessor": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.AnchorScannerTreeProcessor", "kind": "Gdef"}, "AutorefsConfig": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.plugin.AutorefsConfig", "kind": "Gdef"}, "AutorefsExtension": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.AutorefsExtension", "kind": "Gdef"}, "AutorefsHookInterface": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.AutorefsHookInterface", "kind": "Gdef"}, "AutorefsInlineProcessor": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.AutorefsInlineProcessor", "kind": "Gdef"}, "AutorefsPlugin": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.plugin.AutorefsPlugin", "kind": "Gdef"}, "Backlink": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.backlinks.Backlink", "kind": "Gdef"}, "BacklinkCrumb": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.backlinks.BacklinkCrumb", "kind": "Gdef"}, "BacklinksTreeProcessor": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.backlinks.BacklinksTreeProcessor", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "mkdocs_autorefs.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs_autorefs.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "fix_ref": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.fix_ref", "kind": "Gdef"}, "fix_refs": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.fix_refs", "kind": "Gdef"}, "relative_url": {".class": "SymbolTableNode", "cross_ref": "mkdocs_autorefs._internal.references.relative_url", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs_autorefs/__init__.py"}
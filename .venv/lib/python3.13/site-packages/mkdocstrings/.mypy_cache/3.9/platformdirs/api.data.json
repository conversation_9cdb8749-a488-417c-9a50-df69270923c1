{".class": "MypyFile", "_fullname": "platformdirs.api", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PlatformDirsABC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["site_cache_dir", 1], ["site_config_dir", 1], ["site_data_dir", 1], ["site_runtime_dir", 1], ["user_cache_dir", 1], ["user_config_dir", 1], ["user_data_dir", 1], ["user_desktop_dir", 1], ["user_documents_dir", 1], ["user_downloads_dir", 1], ["user_log_dir", 1], ["user_music_dir", 1], ["user_pictures_dir", 1], ["user_runtime_dir", 1], ["user_state_dir", 1], ["user_videos_dir", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "platformdirs.api.PlatformDirsABC", "name": "PlatformDirsABC", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "platformdirs.api.PlatformDirsABC", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "platformdirs.api", "mro": ["platformdirs.api.PlatformDirsABC", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "appname", "appauthor", "version", "roaming", "multipath", "opinion", "ensure_exists"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "appname", "appauthor", "version", "roaming", "multipath", "opinion", "ensure_exists"], "arg_types": ["platformdirs.api.PlatformDirsABC", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PlatformDirsABC", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_append_app_name_and_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "base"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC._append_app_name_and_version", "name": "_append_app_name_and_version", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "base"], "arg_types": ["platformdirs.api.PlatformDirsABC", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_append_app_name_and_version of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_first_item_as_path_if_multipath": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "directory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC._first_item_as_path_if_multipath", "name": "_first_item_as_path_if_multipath", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "directory"], "arg_types": ["platformdirs.api.PlatformDirsABC", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_first_item_as_path_if_multipath of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_optionally_create_directory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC._optionally_create_directory", "name": "_optionally_create_directory", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["platformdirs.api.PlatformDirsABC", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_optionally_create_directory of PlatformDirsABC", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "appauthor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.appauthor", "name": "appauthor", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": true}}}, "appname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.appname", "name": "appname", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "ensure_exists": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.ensure_exists", "name": "ensure_exists", "type": "builtins.bool"}}, "iter_cache_dirs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC.iter_cache_dirs", "name": "iter_cache_dirs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_cache_dirs of PlatformDirsABC", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_cache_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC.iter_cache_paths", "name": "iter_cache_paths", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_cache_paths of PlatformDirsABC", "ret_type": {".class": "Instance", "args": ["pathlib.Path"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_config_dirs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC.iter_config_dirs", "name": "iter_config_dirs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_config_dirs of PlatformDirsABC", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_config_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC.iter_config_paths", "name": "iter_config_paths", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_config_paths of PlatformDirsABC", "ret_type": {".class": "Instance", "args": ["pathlib.Path"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_data_dirs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC.iter_data_dirs", "name": "iter_data_dirs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_data_dirs of PlatformDirsABC", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_data_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC.iter_data_paths", "name": "iter_data_paths", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_data_paths of PlatformDirsABC", "ret_type": {".class": "Instance", "args": ["pathlib.Path"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_runtime_dirs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC.iter_runtime_dirs", "name": "iter_runtime_dirs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_runtime_dirs of PlatformDirsABC", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_runtime_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "platformdirs.api.PlatformDirsABC.iter_runtime_paths", "name": "iter_runtime_paths", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_runtime_paths of PlatformDirsABC", "ret_type": {".class": "Instance", "args": ["pathlib.Path"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "multipath": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.multipath", "name": "multipath", "type": "builtins.bool"}}, "opinion": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.opinion", "name": "opinion", "type": "builtins.bool"}}, "roaming": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.roaming", "name": "roaming", "type": "builtins.bool"}}, "site_cache_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.site_cache_dir", "name": "site_cache_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_cache_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.site_cache_dir", "name": "site_cache_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_cache_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "site_cache_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.site_cache_path", "name": "site_cache_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_cache_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.site_cache_path", "name": "site_cache_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_cache_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "site_config_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.site_config_dir", "name": "site_config_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_config_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.site_config_dir", "name": "site_config_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_config_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "site_config_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.site_config_path", "name": "site_config_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_config_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.site_config_path", "name": "site_config_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_config_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "site_data_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.site_data_dir", "name": "site_data_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_data_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.site_data_dir", "name": "site_data_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_data_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "site_data_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.site_data_path", "name": "site_data_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_data_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.site_data_path", "name": "site_data_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_data_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "site_runtime_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.site_runtime_dir", "name": "site_runtime_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_runtime_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.site_runtime_dir", "name": "site_runtime_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_runtime_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "site_runtime_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.site_runtime_path", "name": "site_runtime_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_runtime_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.site_runtime_path", "name": "site_runtime_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "site_runtime_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_cache_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_cache_dir", "name": "user_cache_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_cache_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_cache_dir", "name": "user_cache_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_cache_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_cache_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_cache_path", "name": "user_cache_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_cache_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_cache_path", "name": "user_cache_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_cache_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_config_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_config_dir", "name": "user_config_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_config_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_config_dir", "name": "user_config_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_config_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_config_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_config_path", "name": "user_config_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_config_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_config_path", "name": "user_config_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_config_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_data_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_data_dir", "name": "user_data_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_data_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_data_dir", "name": "user_data_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_data_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_data_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_data_path", "name": "user_data_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_data_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_data_path", "name": "user_data_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_data_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_desktop_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_desktop_dir", "name": "user_desktop_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_desktop_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_desktop_dir", "name": "user_desktop_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_desktop_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_desktop_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_desktop_path", "name": "user_desktop_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_desktop_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_desktop_path", "name": "user_desktop_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_desktop_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_documents_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_documents_dir", "name": "user_documents_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_documents_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_documents_dir", "name": "user_documents_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_documents_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_documents_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_documents_path", "name": "user_documents_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_documents_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_documents_path", "name": "user_documents_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_documents_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_downloads_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_downloads_dir", "name": "user_downloads_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_downloads_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_downloads_dir", "name": "user_downloads_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_downloads_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_downloads_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_downloads_path", "name": "user_downloads_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_downloads_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_downloads_path", "name": "user_downloads_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_downloads_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_log_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_log_dir", "name": "user_log_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_log_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_log_dir", "name": "user_log_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_log_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_log_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_log_path", "name": "user_log_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_log_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_log_path", "name": "user_log_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_log_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_music_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_music_dir", "name": "user_music_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_music_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_music_dir", "name": "user_music_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_music_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_music_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_music_path", "name": "user_music_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_music_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_music_path", "name": "user_music_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_music_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_pictures_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_pictures_dir", "name": "user_pictures_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_pictures_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_pictures_dir", "name": "user_pictures_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_pictures_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_pictures_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_pictures_path", "name": "user_pictures_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_pictures_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_pictures_path", "name": "user_pictures_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_pictures_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_runtime_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_runtime_dir", "name": "user_runtime_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_runtime_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_runtime_dir", "name": "user_runtime_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_runtime_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_runtime_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_runtime_path", "name": "user_runtime_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_runtime_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_runtime_path", "name": "user_runtime_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_runtime_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_state_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_state_dir", "name": "user_state_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_state_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_state_dir", "name": "user_state_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_state_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_state_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_state_path", "name": "user_state_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_state_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_state_path", "name": "user_state_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_state_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_videos_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "platformdirs.api.PlatformDirsABC.user_videos_dir", "name": "user_videos_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_videos_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_videos_dir", "name": "user_videos_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_videos_dir of PlatformDirsABC", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_videos_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "platformdirs.api.PlatformDirsABC.user_videos_path", "name": "user_videos_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_videos_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.user_videos_path", "name": "user_videos_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["platformdirs.api.PlatformDirsABC"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_videos_path of PlatformDirsABC", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "platformdirs.api.PlatformDirsABC.version", "name": "version", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "platformdirs.api.PlatformDirsABC.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "platformdirs.api.PlatformDirsABC", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "platformdirs.api.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "platformdirs.api.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "platformdirs.api.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "platformdirs.api.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "platformdirs.api.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "platformdirs.api.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/platformdirs/api.py"}
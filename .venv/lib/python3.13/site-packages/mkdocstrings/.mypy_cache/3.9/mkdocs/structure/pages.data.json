{".class": "MypyFile", "_fullname": "mkdocs.structure.pages", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AMP_SUBSTITUTE": {".class": "SymbolTableNode", "cross_ref": "markdown.util.AMP_SUBSTITUTE", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "File": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.files.File", "kind": "Gdef"}, "Files": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.files.Files", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "MkDocsConfig": {".class": "SymbolTableNode", "cross_ref": "mkdocs.config.defaults.MkDocsConfig", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "Page": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["mkdocs.structure.StructureItem"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.structure.pages.Page", "name": "Page", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages.Page", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "mkdocs.structure.pages", "mro": ["mkdocs.structure.pages.Page", "mkdocs.structure.StructureItem", "builtins.object"], "names": {".class": "SymbolTable", "__active": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocs.structure.pages.Page.__active", "name": "__active", "type": "builtins.bool"}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages.Page.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["mkdocs.structure.pages.Page", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Page", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "title", "file", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages.Page.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "title", "file", "config"], "arg_types": ["mkdocs.structure.pages.Page", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "mkdocs.structure.files.File", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Page", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages.Page.__repr__", "name": "__repr__", "type": null}}, "_set_canonical_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "base"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages.Page._set_canonical_url", "name": "_set_canonical_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "base"], "arg_types": ["mkdocs.structure.pages.Page", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_canonical_url of Page", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_edit_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "repo_url", "edit_uri", "edit_uri_template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages.Page._set_edit_url", "name": "_set_edit_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "repo_url", "edit_uri", "edit_uri_template"], "arg_types": ["mkdocs.structure.pages.Page", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_edit_url of Page", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages.Page._set_title", "name": "_set_title", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages.Page"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_title of Page", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_title_from_render": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages.Page._title_from_render", "name": "_title_from_render", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "abs_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.structure.pages.Page.abs_url", "name": "abs_url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "mkdocs.structure.pages.Page.active", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "mkdocs.structure.pages.Page.active", "name": "active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages.Page"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "active of Page", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages.Page.active", "name": "active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages.Page"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "active of Page", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mkdocs.structure.pages.Page.active", "name": "active", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["mkdocs.structure.pages.Page", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "active of Page", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "active", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages.Page"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "active of Page", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "canonical_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.structure.pages.Page.canonical_url", "name": "canonical_url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.structure.pages.Page.children", "name": "children", "type": {".class": "NoneType"}}}, "content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.structure.pages.Page.content", "name": "content", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "edit_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.structure.pages.Page.edit_url", "name": "edit_url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.structure.pages.Page.file", "name": "file", "type": "mkdocs.structure.files.File"}}, "is_homepage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "mkdocs.structure.pages.Page.is_homepage", "name": "is_homepage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages.Page"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_homepage of Page", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages.Page.is_homepage", "name": "is_homepage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages.Page"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_homepage of Page", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "mkdocs.structure.pages.Page.is_index", "name": "is_index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages.Page"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_index of Page", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages.Page.is_index", "name": "is_index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages.Page"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_index of Page", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_link": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.structure.pages.Page.is_link", "name": "is_link", "type": "builtins.bool"}}, "is_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.structure.pages.Page.is_page", "name": "is_page", "type": "builtins.bool"}}, "is_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.structure.pages.Page.is_section", "name": "is_section", "type": "builtins.bool"}}, "links_to_anchors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.structure.pages.Page.links_to_anchors", "name": "links_to_anchors", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["mkdocs.structure.files.File", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "markdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.structure.pages.Page.markdown", "name": "markdown", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.structure.pages.Page.meta", "name": "meta", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "next_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.structure.pages.Page.next_page", "name": "next_page", "type": {".class": "UnionType", "items": ["mkdocs.structure.pages.Page", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "present_anchor_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.structure.pages.Page.present_anchor_ids", "name": "present_anchor_ids", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "previous_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.structure.pages.Page.previous_page", "name": "previous_page", "type": {".class": "UnionType", "items": ["mkdocs.structure.pages.Page", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "read_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages.Page.read_source", "name": "read_source", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_source of Page", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages.Page.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "files"], "arg_types": ["mkdocs.structure.pages.Page", "mkdocs.config.defaults.MkDocsConfig", "mkdocs.structure.files.Files"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Page", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mkdocs.structure.pages.Page.title", "name": "title", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages.Page"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "title of Page", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages.Page.title", "name": "title", "type": "mkdocs.utils.weak_property"}}}, "toc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.structure.pages.Page.toc", "name": "toc", "type": "mkdocs.structure.toc.TableOfContents"}}, "update_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages.Page.update_date", "name": "update_date", "type": "builtins.str"}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "mkdocs.structure.pages.Page.url", "name": "url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages.Page"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "url of Page", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages.Page.url", "name": "url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages.Page"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "url of Page", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate_anchor_links": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "files", "log_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages.Page.validate_anchor_links", "name": "validate_anchor_links", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "files", "log_level"], "arg_types": ["mkdocs.structure.pages.Page", "mkdocs.structure.files.Files", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_anchor_links of Page", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.structure.pages.Page.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.structure.pages.Page", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "StructureItem": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.StructureItem", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TableOfContents": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.toc.TableOfContents", "kind": "Gdef"}, "_AbsoluteLinksValidationValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.structure.pages._AbsoluteLinksValidationValue", "name": "_AbsoluteLinksValidationValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "mkdocs.structure.pages._AbsoluteLinksValidationValue", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "mkdocs.structure.pages", "mro": ["mkdocs.structure.pages._AbsoluteLinksValidationValue", "enum.IntEnum", "builtins.int", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "RELATIVE_TO_DOCS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mkdocs.structure.pages._AbsoluteLinksValidationValue.RELATIVE_TO_DOCS", "name": "RELATIVE_TO_DOCS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.structure.pages._AbsoluteLinksValidationValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.structure.pages._AbsoluteLinksValidationValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ExtractAnchorsTreeprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.structure.pages._ExtractAnchorsTreeprocessor", "name": "_ExtractAnchorsTreeprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._ExtractAnchorsTreeprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.structure.pages", "mro": ["mkdocs.structure.pages._ExtractAnchorsTreeprocessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "file", "files", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._ExtractAnchorsTreeprocessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "file", "files", "config"], "arg_types": ["mkdocs.structure.pages._ExtractAnchorsTreeprocessor", "mkdocs.structure.files.File", "mkdocs.structure.files.Files", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ExtractAnchorsTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._ExtractAnchorsTreeprocessor._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "md"], "arg_types": ["mkdocs.structure.pages._ExtractAnchorsTreeprocessor", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of _ExtractAnchorsTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "present_anchor_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages._ExtractAnchorsTreeprocessor.present_anchor_ids", "name": "present_anchor_ids", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._ExtractAnchorsTreeprocessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "root"], "arg_types": ["mkdocs.structure.pages._ExtractAnchorsTreeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of _ExtractAnchorsTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.structure.pages._ExtractAnchorsTreeprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.structure.pages._ExtractAnchorsTreeprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ExtractTitleTreeprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.structure.pages._ExtractTitleTreeprocessor", "name": "_ExtractTitleTreeprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._ExtractTitleTreeprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.structure.pages", "mro": ["mkdocs.structure.pages._ExtractTitleTreeprocessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._ExtractTitleTreeprocessor._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "md"], "arg_types": ["mkdocs.structure.pages._ExtractTitleTreeprocessor", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of _ExtractTitleTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "md": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "mkdocs.structure.pages._ExtractTitleTreeprocessor.md", "name": "md", "type": "markdown.core.Markdown"}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._ExtractTitleTreeprocessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "root"], "arg_types": ["mkdocs.structure.pages._ExtractTitleTreeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of _ExtractTitleTreeprocessor", "ret_type": "xml.etree.ElementTree.Element", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "mkdocs.structure.pages._ExtractTitleTreeprocessor.title", "name": "title", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.structure.pages._ExtractTitleTreeprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.structure.pages._ExtractTitleTreeprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HTMLHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.structure.pages._HTMLHandler", "name": "_HTMLHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "mkdocs.structure.pages._HTMLHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.structure.pages", "mro": ["mkdocs.structure.pages._HTMLHandler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._HTMLHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages._HTMLHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _HTMLHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_starttag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._HTMLHandler.handle_starttag", "name": "handle_starttag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag", "attrs"], "arg_types": ["mkdocs.structure.pages._HTMLHandler", "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_starttag of _HTMLHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "present_anchor_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages._HTMLHandler.present_anchor_ids", "name": "present_anchor_ids", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.structure.pages._HTMLHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.structure.pages._HTMLHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RawHTMLPreprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.preprocessors.Preprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.structure.pages._RawHTMLPreprocessor", "name": "_RawHTMLPreprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._RawHTMLPreprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.structure.pages", "mro": ["mkdocs.structure.pages._RawHTMLPreprocessor", "markdown.preprocessors.Preprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._RawHTMLPreprocessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mkdocs.structure.pages._RawHTMLPreprocessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _RawHTMLPreprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._RawHTMLPreprocessor._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "md"], "arg_types": ["mkdocs.structure.pages._RawHTMLPreprocessor", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of _RawHTMLPreprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "present_anchor_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages._RawHTMLPreprocessor.present_anchor_ids", "name": "present_anchor_ids", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._RawHTMLPreprocessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": ["mkdocs.structure.pages._RawHTMLPreprocessor", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of _RawHTMLPreprocessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.structure.pages._RawHTMLPreprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.structure.pages._RawHTMLPreprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RelativePathTreeprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor", "name": "_RelativePathTreeprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mkdocs.structure.pages", "mro": ["mkdocs.structure.pages._RelativePathTreeprocessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "file", "files", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "file", "files", "config"], "arg_types": ["mkdocs.structure.pages._RelativePathTreeprocessor", "mkdocs.structure.files.File", "mkdocs.structure.files.Files", "mkdocs.config.defaults.MkDocsConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _RelativePathTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_possible_target_uris": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["cls", "file", "path", "use_directory_urls", "suggest_absolute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor._possible_target_uris", "name": "_possible_target_uris", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["cls", "file", "path", "use_directory_urls", "suggest_absolute"], "arg_types": [{".class": "TypeType", "item": "mkdocs.structure.pages._RelativePathTreeprocessor"}, "mkdocs.structure.files.File", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_possible_target_uris of _RelativePathTreeprocessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor._possible_target_uris", "name": "_possible_target_uris", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["cls", "file", "path", "use_directory_urls", "suggest_absolute"], "arg_types": [{".class": "TypeType", "item": "mkdocs.structure.pages._RelativePathTreeprocessor"}, "mkdocs.structure.files.File", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_possible_target_uris of _RelativePathTreeprocessor", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "md"], "arg_types": ["mkdocs.structure.pages._RelativePathTreeprocessor", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of _RelativePathTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_target_uri": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "src_path", "dest_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor._target_uri", "name": "_target_uri", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "src_path", "dest_path"], "arg_types": [{".class": "TypeType", "item": "mkdocs.structure.pages._RelativePathTreeprocessor"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_target_uri of _RelativePathTreeprocessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor._target_uri", "name": "_target_uri", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "src_path", "dest_path"], "arg_types": [{".class": "TypeType", "item": "mkdocs.structure.pages._RelativePathTreeprocessor"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_target_uri of _RelativePathTreeprocessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor.config", "name": "config", "type": "mkdocs.config.defaults.MkDocsConfig"}}, "file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor.file", "name": "file", "type": "mkdocs.structure.files.File"}}, "files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor.files", "name": "files", "type": "mkdocs.structure.files.Files"}}, "links_to_anchors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor.links_to_anchors", "name": "links_to_anchors", "type": {".class": "Instance", "args": ["mkdocs.structure.files.File", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "path_to_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor.path_to_url", "name": "path_to_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["mkdocs.structure.pages._RelativePathTreeprocessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path_to_url of _RelativePathTreeprocessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "root"], "arg_types": ["mkdocs.structure.pages._RelativePathTreeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of _RelativePathTreeprocessor", "ret_type": "xml.etree.ElementTree.Element", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mkdocs.structure.pages._RelativePathTreeprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mkdocs.structure.pages._RelativePathTreeprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.structure.pages.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.structure.pages.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.structure.pages.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.structure.pages.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.structure.pages.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.structure.pages.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_removesuffix": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils._removesuffix", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "etree": {".class": "SymbolTableNode", "cross_ref": "xml.etree.ElementTree", "kind": "Gdef"}, "get_build_date": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils.get_build_date", "kind": "Gdef"}, "get_heading_text": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils.rendering.get_heading_text", "kind": "Gdef"}, "get_markdown_title": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils.get_markdown_title", "kind": "Gdef"}, "get_toc": {".class": "SymbolTableNode", "cross_ref": "mkdocs.structure.toc.get_toc", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mkdocs.structure.pages.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "markdown": {".class": "SymbolTableNode", "cross_ref": "markdown", "kind": "Gdef"}, "meta": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils.meta", "kind": "Gdef"}, "posixpath": {".class": "SymbolTableNode", "cross_ref": "posixpath", "kind": "Gdef"}, "urljoin": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urljoin", "kind": "Gdef"}, "urlsplit": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlsplit", "kind": "Gdef"}, "urlunquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef"}, "urlunsplit": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlunsplit", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "weak_property": {".class": "SymbolTableNode", "cross_ref": "mkdocs.utils.weak_property", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/structure/pages.py"}
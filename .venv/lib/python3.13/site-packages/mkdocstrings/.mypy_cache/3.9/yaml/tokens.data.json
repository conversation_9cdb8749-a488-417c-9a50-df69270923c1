{".class": "MypyFile", "_fullname": "yaml.tokens", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AliasToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.AliasToken", "name": "AliasToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.AliasToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.AliasToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "value", "start_mark", "end_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.tokens.AliasToken.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "value", "start_mark", "end_mark"], "arg_types": ["yaml.tokens.AliasToken", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AliasToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.AliasToken.end_mark", "name": "end_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.AliasToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "start_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.AliasToken.start_mark", "name": "start_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.AliasToken.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.AliasToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.AliasToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AnchorToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.AnchorToken", "name": "<PERSON>chor<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.AnchorToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.AnchorToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "value", "start_mark", "end_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.tokens.AnchorToken.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "value", "start_mark", "end_mark"], "arg_types": ["yaml.tokens.AnchorToken", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AnchorToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.AnchorToken.end_mark", "name": "end_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.AnchorToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "start_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.AnchorToken.start_mark", "name": "start_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.AnchorToken.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.AnchorToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.AnchorToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BlockEndToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.BlockEndToken", "name": "BlockEndToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.BlockEndToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.BlockEndToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.BlockEndToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.BlockEndToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.BlockEndToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BlockEntryToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.BlockEntryToken", "name": "BlockEntryToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.BlockEntryToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.BlockEntryToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.BlockEntryToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.BlockEntryToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.BlockEntryToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BlockMappingStartToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.BlockMappingStartToken", "name": "BlockMappingStartToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.BlockMappingStartToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.BlockMappingStartToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.BlockMappingStartToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.BlockMappingStartToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.BlockMappingStartToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BlockSequenceStartToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.BlockSequenceStartToken", "name": "BlockSequenceStartToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.BlockSequenceStartToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.BlockSequenceStartToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.BlockSequenceStartToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.BlockSequenceStartToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.BlockSequenceStartToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DirectiveToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.DirectiveToken", "name": "DirectiveToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.DirectiveToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.DirectiveToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "name", "value", "start_mark", "end_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.tokens.DirectiveToken.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "name", "value", "start_mark", "end_mark"], "arg_types": ["yaml.tokens.DirectiveToken", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DirectiveToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.DirectiveToken.end_mark", "name": "end_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.DirectiveToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.DirectiveToken.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "start_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.DirectiveToken.start_mark", "name": "start_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.DirectiveToken.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.DirectiveToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.DirectiveToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DocumentEndToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.DocumentEndToken", "name": "DocumentEndToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.DocumentEndToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.DocumentEndToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.DocumentEndToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.DocumentEndToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.DocumentEndToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DocumentStartToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.DocumentStartToken", "name": "DocumentStartToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.DocumentStartToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.DocumentStartToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.DocumentStartToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.DocumentStartToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.DocumentStartToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlowEntryToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.FlowEntryToken", "name": "FlowEntryToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.FlowEntryToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.FlowEntryToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.FlowEntryToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.FlowEntryToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.FlowEntryToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlowMappingEndToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.FlowMappingEndToken", "name": "FlowMappingEndToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.FlowMappingEndToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.FlowMappingEndToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.FlowMappingEndToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.FlowMappingEndToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.FlowMappingEndToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlowMappingStartToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.FlowMappingStartToken", "name": "FlowMappingStartToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.FlowMappingStartToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.FlowMappingStartToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.FlowMappingStartToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.FlowMappingStartToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.FlowMappingStartToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlowSequenceEndToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.FlowSequenceEndToken", "name": "FlowSequenceEndToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.FlowSequenceEndToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.FlowSequenceEndToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.FlowSequenceEndToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.FlowSequenceEndToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.FlowSequenceEndToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlowSequenceStartToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.FlowSequenceStartToken", "name": "FlowSequenceStartToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.FlowSequenceStartToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.FlowSequenceStartToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.FlowSequenceStartToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.FlowSequenceStartToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.FlowSequenceStartToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KeyToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.KeyToken", "name": "KeyToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.KeyToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.KeyToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.KeyToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.KeyToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.KeyToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ScalarToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.ScalarToken", "name": "ScalarToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.ScalarToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.ScalarToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "value", "plain", "start_mark", "end_mark", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.tokens.ScalarToken.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "value", "plain", "start_mark", "end_mark", "style"], "arg_types": ["yaml.tokens.ScalarToken", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScalarToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.ScalarToken.end_mark", "name": "end_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.ScalarToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "plain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.ScalarToken.plain", "name": "plain", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "start_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.ScalarToken.start_mark", "name": "start_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.ScalarToken.style", "name": "style", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.ScalarToken.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.ScalarToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.ScalarToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamEndToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.StreamEndToken", "name": "StreamEndToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.StreamEndToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.StreamEndToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.StreamEndToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.StreamEndToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.StreamEndToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamStartToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.StreamStartToken", "name": "StreamStartToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.StreamStartToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.StreamStartToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "start_mark", "end_mark", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.tokens.StreamStartToken.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "start_mark", "end_mark", "encoding"], "arg_types": ["yaml.tokens.StreamStartToken", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StreamStartToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.StreamStartToken.encoding", "name": "encoding", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "end_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.StreamStartToken.end_mark", "name": "end_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.StreamStartToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "start_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.StreamStartToken.start_mark", "name": "start_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.StreamStartToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.StreamStartToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TagToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.TagToken", "name": "TagToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.TagToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.TagToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "value", "start_mark", "end_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.tokens.TagToken.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "value", "start_mark", "end_mark"], "arg_types": ["yaml.tokens.TagToken", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TagToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.TagToken.end_mark", "name": "end_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.TagToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "start_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.TagToken.start_mark", "name": "start_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.TagToken.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.TagToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.TagToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.Token", "name": "Token", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.Token", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_mark", "end_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.tokens.Token.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_mark", "end_mark"], "arg_types": ["yaml.tokens.Token", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Token", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.Token.end_mark", "name": "end_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "start_mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.Token.start_mark", "name": "start_mark", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.Token.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.Token", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValueToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.tokens.Token"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.tokens.ValueToken", "name": "ValueToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.tokens.ValueToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.tokens", "mro": ["yaml.tokens.ValueToken", "yaml.tokens.Token", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.tokens.ValueToken.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.tokens.ValueToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.tokens.ValueToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.tokens.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.tokens.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.tokens.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.tokens.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.tokens.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.tokens.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/yaml-stubs/tokens.pyi"}
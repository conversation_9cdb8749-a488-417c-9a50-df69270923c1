{"data_mtime": 1741470549, "dep_lines": [37, 29, 34, 36, 36, 36, 44, 1192, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 32, 33, 36, 1192, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 5, 20, 5, 10, 10, 10, 10, 10, 10, 10, 5, 10, 5, 5, 10, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mkdocs.config.base", "urllib.parse", "pathspec.gitignore", "mkdocs.plugins", "mkdocs.theme", "mkdocs.utils", "mkdocs.exceptions", "importlib.util", "__future__", "functools", "ipaddress", "logging", "os", "string", "sys", "traceback", "types", "warnings", "collections", "typing", "markdown", "pathspec", "mkdocs", "importlib", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "genericpath", "importlib_metadata", "markdown.core", "markdown.extensions", "pathspec.pathspec", "posixpath", "typing_extensions"], "hash": "7aa09f678268657214901bf7d0f474d2f125de0c", "id": "mkdocs.config.config_options", "ignore_all": true, "interface_hash": "2751476cd2141ce203e61901fe7cfafd8a105105", "mtime": 1731169893, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/config/config_options.py", "plugin_data": null, "size": 44182, "suppressed": [], "version_id": "1.15.0"}
{".class": "MypyFile", "_fullname": "mkdocs.utils.rendering", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.rendering.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.rendering.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.rendering.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.rendering.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.rendering.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.rendering.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_extract_alt_texts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.rendering._extract_alt_texts", "name": "_extract_alt_texts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["root"], "arg_types": ["xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_alt_texts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_predicate_for_alt_texts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.rendering._predicate_for_alt_texts", "name": "_predicate_for_alt_texts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": ["xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_predicate_for_alt_texts", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_predicate_for_fnrefs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.rendering._predicate_for_fnrefs", "name": "_predicate_for_fnrefs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": ["xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_predicate_for_fnrefs", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_anchorlink": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.rendering._remove_anchorlink", "name": "_remove_anchorlink", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": ["xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_anchorlink", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_fnrefs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.rendering._remove_fnrefs", "name": "_remove_fnrefs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["root"], "arg_types": ["xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_fnrefs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_inner_html": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["el", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.rendering._render_inner_html", "name": "_render_inner_html", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["el", "md"], "arg_types": ["xml.etree.ElementTree.Element", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_inner_html", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_replace_elements_with_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["parent", "predicate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.rendering._replace_elements_with_text", "name": "_replace_elements_with_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["parent", "predicate"], "arg_types": ["xml.etree.ElementTree.Element", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace_elements_with_text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_strip_tags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.rendering._strip_tags", "name": "_strip_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_strip_tags", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unescape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mkdocs.utils.rendering._unescape", "name": "_unescape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "etree": {".class": "SymbolTableNode", "cross_ref": "xml.etree.ElementTree", "kind": "Gdef"}, "get_heading_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["el", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mkdocs.utils.rendering.get_heading_text", "name": "get_heading_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["el", "md"], "arg_types": ["xml.etree.ElementTree.Element", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_heading_text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "markdown": {".class": "SymbolTableNode", "cross_ref": "markdown", "kind": "Gdef"}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/utils/rendering.py"}
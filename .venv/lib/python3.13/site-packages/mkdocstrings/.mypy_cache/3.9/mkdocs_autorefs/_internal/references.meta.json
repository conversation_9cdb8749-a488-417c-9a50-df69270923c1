{"data_mtime": 1741470549, "dep_lines": [16, 20, 26, 35, 12, 15, 18, 19, 21, 22, 23, 29, 38, 3, 5, 6, 7, 8, 9, 10, 11, 13, 14, 24, 30, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 5, 5, 5, 5, 5, 5, 25, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["xml.etree.ElementTree", "markdown.extensions.toc", "mkdocs_autorefs._internal.backlinks", "mkdocs_autorefs._internal.plugin", "html.parser", "urllib.parse", "markdown.core", "markdown.extensions", "markdown.inlinepatterns", "markdown.treeprocessors", "markdown.util", "collections.abc", "mkdocs.plugins", "__future__", "logging", "re", "warnings", "abc", "dataclasses", "functools", "html", "io", "typing", "markupsafe", "pathlib", "markdown", "builtins", "_frozen_importlib", "_io", "_markupbase", "_typeshed", "enum", "mkdocs", "os", "xml", "xml.etree"], "hash": "71dfb7a6e2b1e4556fd46a0cfbbcf738f8e66c99", "id": "mkdocs_autorefs._internal.references", "ignore_all": true, "interface_hash": "0dcbdb465b65bcd99c6f0005989a6394a8b9d209", "mtime": 1740413643, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs_autorefs/_internal/references.py", "plugin_data": null, "size": 26131, "suppressed": [], "version_id": "1.15.0"}
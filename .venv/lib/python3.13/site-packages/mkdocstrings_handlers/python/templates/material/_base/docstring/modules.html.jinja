{#- Template for "Modules" sections in docstrings.

This template renders a list of documented modules in the format
specified with the [`docstring_section_style`][] configuration option.

Context:
  section (griffe.DocstringSectionAttributes): The section to render.
-#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
  {{ log.debug("Rendering modules section") }}
{% endblock logs %}

{# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
{% import "language"|get_template as lang with context %}
{#- Language module providing the `t` translation method. -#}

{% if config.docstring_section_style == "table" %}
  {% block table_style scoped %}
    {#- Block for the `table` section style. -#}
    <p><span class="doc-section-title">{{ section.title or lang.t("Modules:") }}</span></p>
    <table>
      <thead>
        <tr>
          <th>{{ lang.t("Name") }}</th>
          <th>{{ lang.t("Description") }}</th>
        </tr>
      </thead>
      <tbody>
        {% for module in section.value %}
          <tr class="doc-section-item">
            <td><code><autoref identifier="{{ obj.path }}.{{ module.name }}" optional hover>{{ module.name }}</autoref></code></td>
            <td>
              <div class="doc-md-description">
                {{ module.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
              </div>
            </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {% endblock table_style %}
{% elif config.docstring_section_style == "list" %}
  {% block list_style scoped %}
    {#- Block for the `list` section style. -#}
    <p><span class="doc-section-title">{{ section.title or lang.t("Modules:") }}</span></p>
    <ul>
      {% for module in section.value %}
        <li class="doc-section-item field-body">
          <b><code><autoref identifier="{{ obj.path }}.{{ module.name }}" optional hover>{{ module.name }}</autoref></code></b>
          –
          <div class="doc-md-description">
            {{ module.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
          </div>
        </li>
      {% endfor %}
    </ul>
  {% endblock list_style %}
{% elif config.docstring_section_style == "spacy" %}
  {% block spacy_style scoped %}
    {#- Block for the `spacy` section style. -#}
    <table>
      <thead>
        <tr>
          <th><span class="doc-section-title">{{ (section.title or lang.t("MODULE")).rstrip(":").upper() }}</span></th>
          <th><span>{{ lang.t("DESCRIPTION") }}</span></th>
        </tr>
      </thead>
      <tbody>
        {% for module in section.value %}
          <tr class="doc-section-item">
            <td><code><autoref identifier="{{ obj.path }}.{{ module.name }}" optional hover>{{ module.name }}</autoref></code></td>
            <td class="doc-module-details">
              <div class="doc-md-description">
                {{ module.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
              </div>
            </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {% endblock spacy_style %}
{% endif %}
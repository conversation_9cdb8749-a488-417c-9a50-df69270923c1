{"data_mtime": 1741470549, "dep_lines": [18, 21, 23, 24, 25, 26, 15, 17, 22, 3, 5, 6, 7, 12, 15, 17, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 25, 25, 25, 25, 10, 25, 5, 10, 10, 5, 5, 25, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mkdocs.config.base", "mkdocs.config.defaults", "mkdocs.structure.files", "mkdocs.structure.nav", "mkdocs.structure.pages", "mkdocs.utils.templates", "jinja2.environment", "mkdocs.utils", "mkdocs.livereload", "__future__", "logging", "sys", "typing", "importlib_metadata", "jinja2", "mkdocs", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "datetime", "http", "http.server", "mkdocs.config", "mkdocs.structure", "socketserver", "types", "wsgiref", "wsgiref.simple_server"], "hash": "1594a1cd1c0ad495d7becb61531abc203526b976", "id": "mkdocs.plugins", "ignore_all": true, "interface_hash": "166d30464a6d2fc7d151e8ff74064e47b5cb30e0", "mtime": 1731169893, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/plugins.py", "plugin_data": null, "size": 25265, "suppressed": [], "version_id": "1.15.0"}
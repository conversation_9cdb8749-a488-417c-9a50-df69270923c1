{"data_mtime": 1741470540, "dep_lines": [1, 2, 55, 56, 57, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["sys", "_operator", "_typeshed", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "44ad0d1c3068337c713f90c2beecdd8071e146c5", "id": "operator", "ignore_all": true, "interface_hash": "f05c855c611ba477df3093e909d01b974563398c", "mtime": 1741275134, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/operator.pyi", "plugin_data": null, "size": 4767, "suppressed": [], "version_id": "1.15.0"}
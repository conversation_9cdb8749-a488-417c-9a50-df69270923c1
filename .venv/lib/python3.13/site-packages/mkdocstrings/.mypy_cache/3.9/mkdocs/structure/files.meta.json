{"data_mtime": 1741470549, "dep_lines": [24, 25, 13, 16, 17, 19, 22, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 19, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 10, 10, 10, 25, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mkdocs.config.defaults", "mkdocs.structure.pages", "urllib.parse", "pathspec.gitignore", "pathspec.util", "mkdocs.utils", "jinja2.environment", "__future__", "enum", "fnmatch", "logging", "os", "posixpath", "shutil", "warnings", "functools", "pathlib", "typing", "pathspec", "mkdocs", "jinja2", "builtins", "_frozen_importlib", "_warnings", "abc", "collections", "mkdocs.config", "mkdocs.config.base", "mkdocs.plugins", "pathspec.pathspec", "pathspec.pattern", "typing_extensions"], "hash": "926ad9ac88c8c25f68ed28bb9f4269f0093a7b38", "id": "mkdocs.structure.files", "ignore_all": true, "interface_hash": "290804fd157b66852f75246affdff2424b901818", "mtime": 1731169893, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/structure/files.py", "plugin_data": null, "size": 23569, "suppressed": [], "version_id": "1.15.0"}
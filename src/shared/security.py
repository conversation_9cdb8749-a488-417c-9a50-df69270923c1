"""
Security utilities: password hashing, token generation, and validation.
Follows Gold Standard Guidelines and CCR for security and type safety.
"""
import re
import secrets
from datetime import datetime, timedelta, timezone
from typing import Any

from jose import JWTError, jwt, ExpiredSignatureError  # type: ignore[import-untyped]
from passlib.context import CryptContext  # type: ignore[import-untyped]
from pydantic import BaseModel, EmailStr, constr, validator, field_validator  # type: ignore[import-not-found]

from .errors.exceptions import SecurityError

# Password hashing context (bcrypt)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

SECRET_KEY: str = secrets.token_urlsafe(32)  # Should be loaded from env in production
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

class SecureUserInput(BaseModel):  # type: ignore[misc]
    email: EmailStr
    password: constr(min_length=12)  # type: ignore[valid-type]

    @field_validator('email')  # type: ignore[misc]
    def normalize_email(cls, v: str) -> str:
        return v.lower().strip()

    @field_validator('password')  # type: ignore[misc]
    def validate_password_strength(cls, v: constr) -> constr:
        if not re.search(r'[A-Z]', v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not re.search(r'[a-z]', v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not re.search(r'[0-9]', v):
            raise ValueError("Password must contain at least one digit")
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError("Password must contain at least one special character")
        return v

def validate_input(data: dict[str, Any]) -> SecureUserInput:
    """Validate and sanitize user input."""
    return SecureUserInput(**data)

def hash_password(password: str) -> str:
    """Hash a password for storing."""
    return pwd_context.hash(password)  # type: ignore[no-any-return]

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a stored password against one provided by user."""
    return pwd_context.verify(plain_password, hashed_password)  # type: ignore[no-any-return]

def create_access_token(
    data: dict[str, Any],
    expires_delta: timedelta | None = None,
) -> str:
    """Create a JWT access token."""
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + (
        expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)  # type: ignore[no-any-return]

def create_refresh_token(data: dict[str, Any], expires_delta: timedelta | None = None) -> str:
    """Create a refresh token with extended expiry and type marker."""
    to_encode = data.copy()
    to_encode.update({"type": "refresh"})

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=30)  # 30-day expiry

    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)  # type: ignore[no-any-return]

def verify_access_token(token: str) -> dict[str, Any]:
    """Verify a JWT access token and return the payload."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])  # type: ignore[no-any-return]
        return payload  # type: ignore[no-any-return]
    except ExpiredSignatureError:
        raise SecurityError(violation_type="TOKEN_EXPIRED", message="Token has expired")
    except JWTError:
        raise SecurityError(violation_type="TOKEN_INVALID", message="Invalid token")
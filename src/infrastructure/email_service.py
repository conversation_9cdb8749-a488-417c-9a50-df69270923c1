"""
Email service for sending password reset and notification emails via Gmail.
Follows Gold Standard Guidelines and CCR for security and observability.
"""
import os
from email.message import EmailMessage
from typing import Dict, Any

import aiosmtplib  # type: ignore[import-not-found]
from aiosmtplib import <PERSON><PERSON><PERSON>x<PERSON>, SMTPAuthenticationError, SMTPConnectError, SMTPTimeoutError  # type: ignore[import-not-found]
import structlog  # type: ignore[import-not-found]

from src.shared.errors.exceptions import IntegrationError

logger = structlog.get_logger()

def get_email_credentials() -> Dict[str, str]:
    """Get email credentials with test fallbacks."""
    gmail_user = os.getenv("GMAIL_USER", "<EMAIL>")
    gmail_pass = os.getenv("GMAIL_PASS", "test_password")

    if not os.getenv("GMAIL_USER") or not os.getenv("GMAIL_PASS"):
        logger.warning("Gmail credentials not set in environment variables.")

    return {
        "username": gmail_user,
        "password": gmail_pass
    }

async def send_email_async(subject: str, recipient: str, body: str) -> Dict[str, Any]:
    """Send an email asynchronously using Gmail SMTP."""
    credentials = get_email_credentials()

    # For testing, if no real credentials are set, just return success
    if not os.getenv("GMAIL_USER"):
        logger.error("GMAIL_USER environment variable is not set. Cannot send email.")
        raise RuntimeError("GMAIL_USER environment variable is not set. Please configure a valid sender email address.")

    message = EmailMessage()
    message["From"] = credentials["username"]
    message["To"] = recipient
    message["Subject"] = subject
    message.set_content(body)

    try:
        await aiosmtplib.send(
            message,
            hostname="smtp.gmail.com",
            port=587,
            start_tls=True,
            username=credentials["username"],
            password=credentials["password"],
        )
        logger.info("Email sent", to=recipient, subject=subject)
        return {"success": True, "message": "Email sent successfully"}

    except SMTPAuthenticationError as e:
        logger.error("Failed to send email", error=str(e), to=recipient)
        raise IntegrationError(
            service_name="email_service",
            message=f"Authentication failed: {str(e)}"
        )
    except SMTPConnectError as e:
        logger.error("Failed to send email", error=str(e), to=recipient)
        raise IntegrationError(
            service_name="email_service",
            message=f"Connection failed: {str(e)}"
        )
    except SMTPTimeoutError as e:
        logger.error("Failed to send email", error=str(e), to=recipient)
        raise IntegrationError(
            service_name="email_service",
            message=f"Timeout error: {str(e)}"
        )
    except SMTPException as e:
        logger.error("Failed to send email", error=str(e), to=recipient)
        raise IntegrationError(
            service_name="email_service",
            message=f"SMTP error: {str(e)}"
        )
    except Exception as e:
        logger.error("Failed to send email", error=str(e), to=recipient)
        raise IntegrationError(
            service_name="email_service",
            message=f"System error: {str(e)}"
        )
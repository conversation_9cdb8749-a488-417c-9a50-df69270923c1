{"data_mtime": 1741470544, "dep_lines": [10, 7, 10, 1, 3, 4, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 10, 25, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["xml.etree.ElementTree", "markdown.treeprocessors", "xml.etree", "__future__", "copy", "typing", "markdown", "builtins", "_frozen_importlib", "abc", "markdown.core", "markdown.util", "xml"], "hash": "40efa86e73c83f7e0eab95036bb69f3f7b1b9ff7", "id": "mkdocs.utils.rendering", "ignore_all": true, "interface_hash": "5dc59e019d4a4a1fedd1de68395b9dc3aa741372", "mtime": 1731169894, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/utils/rendering.py", "plugin_data": null, "size": 3504, "suppressed": [], "version_id": "1.15.0"}
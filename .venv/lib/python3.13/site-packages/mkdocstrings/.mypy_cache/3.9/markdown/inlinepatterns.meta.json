{"data_mtime": 1741470544, "dep_lines": [4, 2, 6, 7, 1, 3, 6, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["xml.etree.ElementTree", "collections.abc", "markdown.util", "markdown.core", "re", "typing", "markdown", "builtins", "_frozen_importlib", "abc", "xml", "xml.etree"], "hash": "89dbaf5f03d8a91f75a7ae53617cc0e6b3d8c1c1", "id": "markdown.inlinepatterns", "ignore_all": true, "interface_hash": "dac460979ebbe2f72c6aa5396faa69869a1db64c", "mtime": 1733498037, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/markdown-stubs/inlinepatterns.pyi", "plugin_data": null, "size": 3904, "suppressed": [], "version_id": "1.15.0"}
{"data_mtime": 1741470549, "dep_lines": [20, 21, 23, 25, 26, 32, 33, 34, 35, 17, 22, 29, 31, 9, 11, 12, 13, 14, 15, 16, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 25, 25, 25, 5, 5, 25, 25, 5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mkdocs.config.base", "mkdocs.config.config_options", "mkdocs.structure.pages", "mkdocs_autorefs._internal.backlinks", "mkdocs_autorefs._internal.references", "mkdocs.config.defaults", "mkdocs.structure.files", "mkdocs.structure.nav", "mkdocs.structure.toc", "urllib.parse", "mkdocs.plugins", "collections.abc", "jinja2.environment", "__future__", "contextlib", "functools", "logging", "collections", "pathlib", "typing", "warnings", "builtins", "_frozen_importlib", "_typeshed", "_warnings", "abc", "jinja2", "markdown", "markdown.extensions", "mkdocs", "mkdocs.config", "mkdocs.structure", "mkdocs.theme", "os", "types"], "hash": "daa80d4f6145030812e203cd1a2bfebac49b66d3", "id": "mkdocs_autorefs._internal.plugin", "ignore_all": true, "interface_hash": "5334f02e7a90cc888fa7ea0823c65722052721e9", "mtime": 1740413643, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs_autorefs/_internal/plugin.py", "plugin_data": null, "size": 23761, "suppressed": [], "version_id": "1.15.0"}
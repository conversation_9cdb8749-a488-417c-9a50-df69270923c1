{"data_mtime": 1741470546, "dep_lines": [27, 9, 20, 22, 23, 26, 27, 1, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 20, 10, 20, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["watchdog.observers.polling", "os.path", "urllib.parse", "wsgiref.simple_server", "wsgiref.util", "watchdog.events", "watchdog.observers", "__future__", "functools", "io", "ipaddress", "logging", "mimetypes", "os", "pathlib", "posixpath", "re", "socket", "socketserver", "string", "sys", "threading", "time", "traceback", "urllib", "webbrowser", "wsgiref", "typing", "watchdog", "builtins", "_frozen_importlib", "_socket", "_thread", "_typeshed", "_typeshed.wsgi", "abc", "enum", "http", "http.server", "types", "watchdog.observers.api", "watchdog.utils"], "hash": "49101d2fb03f0f2987dbf59bbe1e1c11727775fc", "id": "mkdocs.livereload", "ignore_all": true, "interface_hash": "627699aadfd789473f7e2e6d1d82c5acc38073d7", "mtime": 1731169893, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/mkdocs/livereload/__init__.py", "plugin_data": null, "size": 13530, "suppressed": [], "version_id": "1.15.0"}
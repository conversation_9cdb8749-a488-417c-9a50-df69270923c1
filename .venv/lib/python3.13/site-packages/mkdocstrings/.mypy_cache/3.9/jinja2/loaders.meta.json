{"data_mtime": 1741470548, "dep_lines": [5, 12, 17, 18, 21, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["importlib.util", "collections.abc", "jinja2.exceptions", "jinja2.utils", "jinja2.environment", "importlib", "os", "posixpath", "sys", "typing", "weakref", "zipimport", "collections", "<PERSON><PERSON><PERSON>", "types", "builtins", "_frozen_importlib", "_typeshed", "_weakref", "abc", "genericpath", "importlib.abc", "jinja2.bccache", "jinja2.nodes", "typing_extensions"], "hash": "ea4f28ed559a123f7cb005d0fa56547fd675109e", "id": "jinja2.loaders", "ignore_all": true, "interface_hash": "a59cc3a67a153ffd5120dbc9dd4fc2f2bb864d6a", "mtime": 1734903670, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/jinja2/loaders.py", "plugin_data": null, "size": 24055, "suppressed": [], "version_id": "1.15.0"}
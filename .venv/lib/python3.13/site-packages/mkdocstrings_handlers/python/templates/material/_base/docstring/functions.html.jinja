{#- Template for "Functions" sections in docstrings.

This template renders a list of documented functions in the format
specified with the [`docstring_section_style`][] configuration option.

Context:
  section (griffe.DocstringSectionAttributes): The section to render.
-#}

{% block logs scoped %}
  {#- Logging block.

  This block can be used to log debug messages, deprecation messages, warnings, etc.
  -#}
  {{ log.debug("Rendering functions section") }}
{% endblock logs %}

{# YORE: Bump 2: Replace `"|get_template` with `.html.jinja"` within line. #}
{% import "language"|get_template as lang with context %}
{#- Language module providing the `t` translation method. -#}

{% if config.docstring_section_style == "table" %}
  {% block table_style scoped %}
    {#- Block for the `table` section style. -#}
    <p><span class="doc-section-title">{{ section.title or lang.t("Methods:") if obj.is_class else lang.t("Functions:") }}</span></p>
    <table>
      <thead>
        <tr>
          <th>{{ lang.t("Name") }}</th>
          <th>{{ lang.t("Description") }}</th>
        </tr>
      </thead>
      <tbody>
        {% for function in section.value %}
          {% if not function.name == "__init__" or not config.merge_init_into_class %}
            <tr class="doc-section-item">
              <td><code><autoref identifier="{{ obj.path }}.{{ function.name }}" optional hover>{{ function.name }}</autoref></code></td>
              <td>
                <div class="doc-md-description">
                  {{ function.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
                </div>
              </td>
            </tr>
          {% endif %}
        {% endfor %}
      </tbody>
    </table>
  {% endblock table_style %}
{% elif config.docstring_section_style == "list" %}
  {% block list_style scoped %}
    {#- Block for the `list` section style. -#}
    <p><span class="doc-section-title">{{ section.title or lang.t("Methods:") if obj.is_class else lang.t("Functions:") }}</span></p>
    <ul>
      {% for function in section.value %}
        {% if not function.name == "__init__" or not config.merge_init_into_class %}
          <li class="doc-section-item field-body">
            <b><code><autoref identifier="{{ obj.path }}.{{ function.name }}" optional hover>{{ function.name }}</autoref></code></b>
            –
            <div class="doc-md-description">
              {{ function.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
            </div>
          </li>
        {% endif %}
      {% endfor %}
    </ul>
  {% endblock list_style %}
{% elif config.docstring_section_style == "spacy" %}
  {% block spacy_style scoped %}
    {#- Block for the `spacy` section style. -#}
    <table>
      <thead>
        <tr>
          <th><span class="doc-section-title">{{ (section.title or lang.t("METHOD") if obj.is_class else lang.t("FUNCTION")).rstrip(":").upper() }}</span></th>
          <th><span>{{ lang.t("DESCRIPTION") }}</span></th>
        </tr>
      </thead>
      <tbody>
        {% for function in section.value %}
          {% if not function.name == "__init__" or not config.merge_init_into_class %}
            <tr class="doc-section-item">
              <td><code><autoref identifier="{{ obj.path }}.{{ function.name }}" optional hover>{{ function.name }}</autoref></code></td>
              <td class="doc-function-details">
                <div class="doc-md-description">
                  {{ function.description|convert_markdown(heading_level, html_id, autoref_hook=autoref_hook) }}
                </div>
              </td>
            </tr>
          {% endif %}
        {% endfor %}
      </tbody>
    </table>
  {% endblock spacy_style %}
{% endif %}
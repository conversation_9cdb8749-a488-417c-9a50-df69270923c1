# FILE: tests/api/test_api_endpoints.py

import pytest
from httpx import Async<PERSON>lient
import uuid

# Helper to generate unique emails for test isolation
def unique_email():
    return f"testuser_{uuid.uuid4().hex[:8]}@example.com"

@pytest.mark.asyncio
async def test_register_endpoint_success(async_client: AsyncClient):
    """Tests the POST /api/v1/register endpoint for a successful registration."""
    email = unique_email()
    password = "StrongPassword123!"
    
    response = await async_client.post(
        "/api/v1/register", json={"email": email, "password": password}
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["email"] == email
    assert data["is_active"] is True

@pytest.mark.asyncio
async def test_register_and_login_flow(async_client: AsyncClient):
    """Tests a full registration, login, and token-protected endpoint access flow."""
    email = unique_email()
    password = "FlowTestPassword123!"
    
    # 1. Register
    register_response = await async_client.post(
        "/api/v1/register", json={"email": email, "password": password}
    )
    assert register_response.status_code == 201
    
    # 2. Login
    login_response = await async_client.post(
        "/api/v1/login", json={"email": email, "password": password}
    )
    assert login_response.status_code == 200
    token_data = login_response.json()
    assert "access_token" in token_data
    
    # 3. Access protected endpoint with token
    token = token_data["access_token"]
    me_response = await async_client.get(
        "/api/v1/me", headers={"Authorization": f"Bearer {token}"}
    )
    
    assert me_response.status_code == 200
    me_data = me_response.json()
    assert me_data["email"] == email

@pytest.mark.asyncio
async def test_login_with_invalid_credentials(async_client: AsyncClient):
    """
    Tests that logging in with an incorrect password returns a 401 error.
    This improves coverage for auth_service.py and routes.py.
    """
    # 1. Register a user so we have a valid account to test against
    email = unique_email()
    password = "CorrectPassword123!"
    register_response = await async_client.post(
        "/api/v1/register", json={"email": email, "password": password}
    )
    assert register_response.status_code == 201

    # 2. Attempt to log in with the wrong password
    response = await async_client.post(
        "/api/v1/login", json={"email": email, "password": "WRONG_PASSWORD"}
    )

    # 3. Assert we get the correct security error
    assert response.status_code == 401
    error_data = response.json()["error"]
    assert error_data["code"] == "SECURITY_INVALID_CREDENTIALS"

@pytest.mark.asyncio
async def test_register_with_duplicate_email(async_client: AsyncClient):
    """
    Tests that attempting to register with an existing email returns a 400 error.
    This improves coverage for user_service.py's error path.
    """
    email = unique_email()
    password = "SomePassword123!"
    
    # 1. Register the email for the first time
    response1 = await async_client.post(
        "/api/v1/register", json={"email": email, "password": password}
    )
    assert response1.status_code == 201

    # 2. Attempt to register with the same email again
    response2 = await async_client.post(
        "/api/v1/register", json={"email": email, "password": "AnotherPassword123!"}
    )

    # 3. Assert we get the correct business rule violation
    assert response2.status_code == 400
    error_data = response2.json()["error"]
    assert error_data["code"] == "EMAIL_ALREADY_EXISTS"
    assert "recovery_actions" in error_data

@pytest.mark.asyncio
async def test_get_me_with_invalid_token(async_client: AsyncClient):
    """
    Tests that accessing a protected endpoint with a bad token returns a 401 error.
    This improves coverage for the security dependency logic in routes.py.
    """
    response = await async_client.get(
        "/api/v1/me", headers={"Authorization": "Bearer an-invalid-token"}
    )

    assert response.status_code == 401
    error_data = response.json()["error"]
    assert error_data["code"] == "SECURITY_TOKEN_INVALID"
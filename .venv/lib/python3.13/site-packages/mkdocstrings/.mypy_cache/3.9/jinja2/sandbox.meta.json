{"data_mtime": 1741470548, "dep_lines": [9, 17, 18, 19, 5, 6, 7, 9, 11, 12, 14, 1, 1, 1, 1, 1, 1, 1, 8], "dep_prios": [10, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["collections.abc", "jinja2.environment", "jinja2.exceptions", "jinja2.runtime", "operator", "types", "typing", "collections", "functools", "string", "markupsafe", "builtins", "_frozen_importlib", "_operator", "abc", "jinja2.bccache", "jinja2.ext", "jinja2.loaders"], "hash": "40e9149d70eb1b2379aa529af87e8adb42634bd6", "id": "jinja2.sandbox", "ignore_all": true, "interface_hash": "51f63870363e55c6406e23f9aa73a8d5e4a2f48d", "mtime": 1734903670, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/jinja2/sandbox.py", "plugin_data": null, "size": 15009, "suppressed": ["_string"], "version_id": "1.15.0"}
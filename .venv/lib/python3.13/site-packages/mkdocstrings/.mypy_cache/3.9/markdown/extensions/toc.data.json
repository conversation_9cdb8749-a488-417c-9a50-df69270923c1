{".class": "MypyFile", "_fullname": "markdown.extensions.toc", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Element": {".class": "SymbolTableNode", "cross_ref": "xml.etree.ElementTree.Element", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Extension": {".class": "SymbolTableNode", "cross_ref": "markdown.extensions.Extension", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IDCOUNT_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.extensions.toc.IDCOUNT_RE", "name": "IDCOUNT_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Markdown": {".class": "SymbolTableNode", "cross_ref": "markdown.core.Markdown", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MutableSet": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSet", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TocExtension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.extensions.Extension"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.extensions.toc.TocExtension", "name": "TocExtension", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocExtension", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.extensions.toc", "mro": ["markdown.extensions.toc.TocExtension", "markdown.extensions.Extension", "builtins.object"], "names": {".class": "SymbolTable", "TreeProcessorClass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocExtension.TreeProcessorClass", "name": "TreeProcessorClass", "type": {".class": "TypeType", "item": "markdown.extensions.toc.TocTreeprocessor"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocExtension.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["markdown.extensions.toc.TocExtension", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TocExtension", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "md": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocExtension.md", "name": "md", "type": "markdown.core.Markdown"}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocExtension.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["markdown.extensions.toc.TocExtension"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of TocExtension", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.extensions.toc.TocExtension.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.extensions.toc.TocExtension", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TocTreeprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.treeprocessors.Treeprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.extensions.toc.TocTreeprocessor", "name": "TocTreeprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocTreeprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.extensions.toc", "mro": ["markdown.extensions.toc.TocTreeprocessor", "markdown.treeprocessors.Treeprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "md", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocTreeprocessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "md", "config"], "arg_types": ["markdown.extensions.toc.TocTreeprocessor", "markdown.core.Markdown", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TocTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_anchor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "c", "elem_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocTreeprocessor.add_anchor", "name": "add_anchor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "c", "elem_id"], "arg_types": ["markdown.extensions.toc.TocTreeprocessor", "xml.etree.ElementTree.Element", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_anchor of TocTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_permalink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "c", "elem_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocTreeprocessor.add_permalink", "name": "add_permalink", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "c", "elem_id"], "arg_types": ["markdown.extensions.toc.TocTreeprocessor", "xml.etree.ElementTree.Element", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_permalink of TocTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "anchorlink_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.anchorlink_class", "name": "anchorlink_class", "type": "builtins.str"}}, "base_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.base_level", "name": "base_level", "type": "builtins.int"}}, "build_toc_div": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "toc_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocTreeprocessor.build_toc_div", "name": "build_toc_div", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "toc_list"], "arg_types": ["markdown.extensions.toc.TocTreeprocessor", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "markdown.extensions.toc._TocToken"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_toc_div of TocTreeprocessor", "ret_type": "xml.etree.ElementTree.Element", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header_rgx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.header_rgx", "name": "header_rgx", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "iterparent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocTreeprocessor.iterparent", "name": "iterparent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["markdown.extensions.toc.TocTreeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iterparent of TocTreeprocessor", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["xml.etree.ElementTree.Element", "xml.etree.ElementTree.Element"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "marker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.marker", "name": "marker", "type": "builtins.str"}}, "permalink_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.permalink_class", "name": "permalink_class", "type": "builtins.str"}}, "permalink_leading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.permalink_leading", "name": "permalink_leading", "type": "builtins.bool"}}, "permalink_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.permalink_title", "name": "permalink_title", "type": "builtins.str"}}, "replace_marker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "root", "elem"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocTreeprocessor.replace_marker", "name": "replace_marker", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "root", "elem"], "arg_types": ["markdown.extensions.toc.TocTreeprocessor", "xml.etree.ElementTree.Element", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_marker of TocTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "doc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocTreeprocessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "doc"], "arg_types": ["markdown.extensions.toc.TocTreeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of TocTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.sep", "name": "sep", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "set_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "elem"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.TocTreeprocessor.set_level", "name": "set_level", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "elem"], "arg_types": ["markdown.extensions.toc.TocTreeprocessor", "xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_level of TocTreeprocessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "slugify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.slugify", "name": "slugify", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.title", "name": "title", "type": "builtins.str"}}, "title_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.title_class", "name": "title_class", "type": "builtins.str"}}, "toc_bottom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.toc_bottom", "name": "toc_bottom", "type": "builtins.int"}}, "toc_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.toc_class", "name": "toc_class", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "toc_top": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.toc_top", "name": "toc_top", "type": "builtins.int"}}, "use_anchors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.use_anchors", "name": "use_anchors", "type": "builtins.bool"}}, "use_permalinks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "markdown.extensions.toc.TocTreeprocessor.use_permalinks", "name": "use_permalinks", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.extensions.toc.TocTreeprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.extensions.toc.TocTreeprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Treeprocessor": {".class": "SymbolTableNode", "cross_ref": "markdown.treeprocessors.Treeprocessor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FlatTocToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.extensions.toc._FlatTocToken", "name": "_FlatTocToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc._FlatTocToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.extensions.toc", "mro": ["markdown.extensions.toc._FlatTocToken", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["level", "builtins.int"], ["id", "builtins.str"], ["name", "builtins.str"]], "readonly_keys": [], "required_keys": ["id", "level", "name"]}}}, "_TocToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.extensions.toc._TocToken", "name": "_TocToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc._TocToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.extensions.toc", "mro": ["markdown.extensions.toc._TocToken", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["level", "builtins.int"], ["id", "builtins.str"], ["name", "builtins.str"], ["children", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "markdown.extensions.toc._TocToken"}], "extra_attrs": null, "type_ref": "builtins.list"}]], "readonly_keys": [], "required_keys": ["children", "id", "level", "name"]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.extensions.toc.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.extensions.toc.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.extensions.toc.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.extensions.toc.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.extensions.toc.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.extensions.toc.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "escape_cdata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.escape_cdata", "name": "escape_cdata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "escape_cdata", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["el"], "dataclass_transform_spec": null, "deprecated": "function markdown.extensions.toc.get_name is deprecated: Use `render_inner_html` and `striptags` instead.", "flags": ["is_decorated"], "fullname": "markdown.extensions.toc.get_name", "name": "get_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": ["xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "markdown.extensions.toc.get_name", "name": "get_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": ["xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "makeExtension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.makeExtension", "name": "makeExtension", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makeExtension", "ret_type": "markdown.extensions.toc.TocExtension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nest_toc_tokens": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["toc_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.nest_toc_tokens", "name": "nest_toc_tokens", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["toc_list"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "markdown.extensions.toc._FlatTocToken"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nest_toc_tokens", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "markdown.extensions.toc._TocToken"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_fnrefs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.remove_fnrefs", "name": "remove_fnrefs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["root"], "arg_types": ["xml.etree.ElementTree.Element"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_fnrefs", "ret_type": "xml.etree.ElementTree.Element", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_inner_html": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["el", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.render_inner_html", "name": "render_inner_html", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["el", "md"], "arg_types": ["xml.etree.ElementTree.Element", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_inner_html", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_postprocessors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["text", "md"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.run_postprocessors", "name": "run_postprocessors", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["text", "md"], "arg_types": ["builtins.str", "markdown.core.Markdown"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_postprocessors", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "slugify": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["value", "separator", "unicode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.slugify", "name": "slugify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["value", "separator", "unicode"], "arg_types": ["builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "slugify", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "slugify_unicode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["value", "separator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.slugify_unicode", "name": "slugify_unicode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["value", "separator"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "slugify_unicode", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stashedHTML2text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["text", "md", "strip_entities"], "dataclass_transform_spec": null, "deprecated": "function markdown.extensions.toc.stashedHTML2text is deprecated: Use `run_postprocessors`, `render_inner_html` and/or `striptags` instead.", "flags": ["is_decorated"], "fullname": "markdown.extensions.toc.stashedHTML2text", "name": "stashedHTML2text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["text", "md", "strip_entities"], "arg_types": ["builtins.str", "markdown.core.Markdown", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stashedHTML2text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "markdown.extensions.toc.stashedHTML2text", "name": "stashedHTML2text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["text", "md", "strip_entities"], "arg_types": ["builtins.str", "markdown.core.Markdown", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stashedHTML2text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "strip_tags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.strip_tags", "name": "strip_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip_tags", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unescape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.unescape", "name": "unescape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unescape", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unique": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["id", "ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.extensions.toc.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["id", "ids"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableSet"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/markdown-stubs/extensions/toc.pyi"}
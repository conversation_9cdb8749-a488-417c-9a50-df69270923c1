{".class": "MypyFile", "_fullname": "markdown.postprocessors", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AndSubstitutePostprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.postprocessors.Postprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.postprocessors.AndSubstitutePostprocessor", "name": "AndSubstitutePostprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.postprocessors.AndSubstitutePostprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.postprocessors", "mro": ["markdown.postprocessors.AndSubstitutePostprocessor", "markdown.postprocessors.Postprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.postprocessors.AndSubstitutePostprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.postprocessors.AndSubstitutePostprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Markdown": {".class": "SymbolTableNode", "cross_ref": "markdown.core.Markdown", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Postprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.util.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.postprocessors.Postprocessor", "name": "Postprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.postprocessors.Postprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.postprocessors", "mro": ["markdown.postprocessors.Postprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.postprocessors.Postprocessor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["markdown.postprocessors.Postprocessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of Postprocessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.postprocessors.Postprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.postprocessors.Postprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RawHtmlPostprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["markdown.postprocessors.Postprocessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "markdown.postprocessors.RawHtmlPostprocessor", "name": "RawHtmlPostprocessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "markdown.postprocessors.RawHtmlPostprocessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "markdown.postprocessors", "mro": ["markdown.postprocessors.RawHtmlPostprocessor", "markdown.postprocessors.Postprocessor", "markdown.util.Processor", "builtins.object"], "names": {".class": "SymbolTable", "isblocklevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "html"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.postprocessors.RawHtmlPostprocessor.isblocklevel", "name": "isblocklevel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "html"], "arg_types": ["markdown.postprocessors.RawHtmlPostprocessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isblocklevel of RawHtmlPostprocessor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stash_to_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.postprocessors.RawHtmlPostprocessor.stash_to_string", "name": "stash_to_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["markdown.postprocessors.RawHtmlPostprocessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stash_to_string of RawHtmlPostprocessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "markdown.postprocessors.RawHtmlPostprocessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "markdown.postprocessors.RawHtmlPostprocessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.postprocessors.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.postprocessors.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.postprocessors.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.postprocessors.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.postprocessors.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "markdown.postprocessors.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "build_postprocessors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["md", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "markdown.postprocessors.build_postprocessors", "name": "build_postprocessors", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["md", "kwargs"], "arg_types": ["markdown.core.Markdown", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_postprocessors", "ret_type": {".class": "Instance", "args": ["markdown.postprocessors.Postprocessor"], "extra_attrs": null, "type_ref": "markdown.util.Registry"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "util": {".class": "SymbolTableNode", "cross_ref": "markdown.util", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/media/data/dev/mkdocstrings-python/.venvs/3.9/lib/python3.9/site-packages/markdown-stubs/postprocessors.pyi"}